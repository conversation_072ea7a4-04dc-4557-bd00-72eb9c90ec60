<?php

namespace Dujiao<PERSON>\Geetest;

use Illuminate\Support\ServiceProvider;

class GeetestServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton('geetest', function ($app) {
            return new Geetest($app['config']['geetest']);
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->publishes([
            __DIR__.'/../config/geetest.php' => config_path('geetest.php'),
        ], 'geetest-config');
    }
}
