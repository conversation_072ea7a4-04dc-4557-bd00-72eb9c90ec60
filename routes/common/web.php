<?php
/**
 * The file was created by <PERSON><PERSON><PERSON>.
 *
 * <AUTHOR>
 * @copyright assimon<<EMAIL>>
 * @link      http://utf8.hk/
 */
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Home\HomeController;
use App\Http\Controllers\Home\AuthController;
use App\Http\Controllers\Home\UserController;
use App\Http\Controllers\Home\OrderController;
use App\Http\Controllers\Home\ArticleController;

Route::group(['middleware' => ['dujiaoka.boot']], function () {
    // 首页
    Route::get('/', [HomeController::class, 'index']);

    // 退出登录
    Route::get('/logout', [AuthController::class, 'logout']);
    // 登录
    Route::get('/login', [AuthController::class, 'login'])->name('login');
    Route::post('/login', [AuthController::class, 'loginHandler']);
    // 注册
    Route::get('/register', [AuthController::class, 'register']);
    Route::post('/send/mail', [AuthController::class, 'sendCode']);
    Route::post('/register', [AuthController::class, 'registerHandler']);
    //用户中心
    Route::group(['middleware' => 'auth:web'], function(){
        Route::get('/user', [UserController::class, 'index']);
        Route::get('/user/invite/{type?}', [UserController::class, 'invite']);
        Route::post('/user/recharge-money', [UserController::class, 'recharge']);
        Route::post('/user/withdraw', [UserController::class, 'withdraw']);
        Route::get('/user/wholesale', [UserController::class, 'wholesale']);
        Route::post('/user/wholesale', [UserController::class, 'wholesaleSubmit']);
        Route::post('/user/change-password', [UserController::class, 'changePassword']);
    });
    // 极验效验
    Route::get('check-geetest', [HomeController::class, 'geetest']);
    // 商品详情
    Route::get('buy/{id}', [HomeController::class, 'buy']);
    // 提交订单
    Route::post('create-order', [OrderController::class, 'createOrder']);
    // 结算页
    Route::get('bill/{orderSN}', [OrderController::class, 'bill']);


    // 通过订单号详情页
    Route::get('detail-order-sn/{orderSN}', [OrderController::class, 'detailOrderSN']);
    Route::get('detail-order-sn/{orderSN}/export-carmis', [OrderController::class, 'exportCarmisToTxt']);

    // 订单查询页
    Route::get('order-search', [OrderController::class, 'orderSearch']);
    // 检查订单状态
    Route::get('check-order-status/{orderSN}', [OrderController::class, 'checkOrderStatus']);
    // 通过订单号查询
    Route::post('search-order-by-sn', [OrderController::class, 'searchOrderBySN']);
    Route::get('search-order-by-sn', [OrderController::class, 'searchOrderBySN']);
    // 通过邮箱查询
    Route::post('search-order-by-email', [OrderController::class, 'searchOrderByEmail']);
    // 通过浏览器查询
    Route::post('search-order-by-browser', [OrderController::class, 'searchOrderByBrowser']);
    // 列出文章
    Route::get('article', [ArticleController::class, 'list']);
    // 访问文章
    Route::get('article/{identifier}', [ArticleController::class, 'detail']);
});

Route::group(['middleware' => ['install.check']], function () {
    // 安装
    Route::get('install', [HomeController::class, 'install']);
    // 执行安装
    Route::post('do-install', [HomeController::class, 'doInstall']);
});

