/* theme-colors.css - 多彩背景主题样式文件 */
/* 保存路径: /assets/css/theme-colors.css */

/* 背景色主题定义 - 根据独角模版的配色方案 */
[data-bg-theme="default"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-accent: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --border-color: #dee2e6;
    --shadow-color: rgba(0, 0, 0, 0.05);
}

[data-bg-theme="pink"] {
    --bg-primary: #ff66b2;
    --bg-secondary: #ffb3d9;
    --bg-accent: #ffd1ec;
    --text-primary: #ffffff;
    --text-secondary: #ffe0f0;
    --border-color: rgba(255, 255, 255, 0.2);
    --shadow-color: rgba(255, 102, 178, 0.2);
}

[data-bg-theme="yellow"] {
    --bg-primary: #ffeb3b;
    --bg-secondary: #fff59d;
    --bg-accent: #fffde7;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --border-color: rgba(255, 235, 59, 0.3);
    --shadow-color: rgba(255, 235, 59, 0.2);
}

[data-bg-theme="red"] {
    --bg-primary: #ff5757;
    --bg-secondary: #ff8a8a;
    --bg-accent: #ffb3b3;
    --text-primary: #ffffff;
    --text-secondary: #ffe0e0;
    --border-color: rgba(255, 255, 255, 0.2);
    --shadow-color: rgba(255, 87, 87, 0.2);
}

[data-bg-theme="deepblue"] {
    --bg-primary: #2c3e50;
    --bg-secondary: #34495e;
    --bg-accent: #415b75;
    --text-primary: #ffffff;
    --text-secondary: #ecf0f1;
    --border-color: rgba(255, 255, 255, 0.1);
    --shadow-color: rgba(44, 62, 80, 0.3);
}

[data-bg-theme="black"] {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-accent: #404040;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --border-color: rgba(255, 255, 255, 0.1);
    --shadow-color: rgba(0, 0, 0, 0.5);
}

[data-bg-theme="purple"] {
    --bg-primary: #9c27b0;
    --bg-secondary: #ba68c8;
    --bg-accent: #e1bee7;
    --text-primary: #ffffff;
    --text-secondary: #f3e5f5;
    --border-color: rgba(255, 255, 255, 0.2);
    --shadow-color: rgba(156, 39, 176, 0.2);
}

[data-bg-theme="lightblue"] {
    --bg-primary: #2196f3;
    --bg-secondary: #64b5f6;
    --bg-accent: #bbdefb;
    --text-primary: #ffffff;
    --text-secondary: #e3f2fd;
    --border-color: rgba(255, 255, 255, 0.2);
    --shadow-color: rgba(33, 150, 243, 0.2);
}

/* 应用背景色到页面元素 */
body {
    background-color: var(--bg-primary) !important;
    color: var(--text-primary) !important;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* 导航栏背景 */
.navbar,
.bg-body {
    background-color: var(--bg-secondary) !important;
    transition: background-color 0.3s ease;
}

/* 暗色模式下的导航栏 */
.bg-dark {
    background-color: var(--bg-accent) !important;
}

/* 卡片和容器背景 */
.card,
.modal-content,
.dropdown-menu,
.offcanvas {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* 次要背景色 */
.bg-body-secondary,
.bg-body-tertiary {
    background-color: var(--bg-accent) !important;
    transition: background-color 0.3s ease;
}

/* 文本颜色调整 */
.text-body,
.navbar-brand,
.nav-link,
.dropdown-item {
    color: var(--text-primary) !important;
    transition: color 0.3s ease;
}

.text-muted,
.text-secondary {
    color: var(--text-secondary) !important;
    transition: color 0.3s ease;
}

/* 边框颜色调整 */
.border,
.dropdown-divider,
.modal-header,
.modal-footer {
    border-color: var(--border-color) !important;
}

/* 输入框和表单元素 */
.form-control,
.form-select {
    background-color: var(--bg-accent) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    background-color: var(--bg-accent) !important;
    color: var(--text-primary) !important;
    border-color: var(--text-secondary) !important;
}

/* 按钮调整 */
.btn-outline-secondary {
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

.btn-outline-secondary:hover {
    background-color: var(--bg-accent) !important;
    border-color: var(--text-secondary) !important;
    color: var(--text-primary) !important;
}

/* 按钮在不同主题下的适配 */
.btn-secondary {
    background-color: var(--bg-accent) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

.btn-dark {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

/* 阴影效果 */
.shadow {
    box-shadow: 0 0.5rem 1rem var(--shadow-color) !important;
}

/* 深色模式下的背景色调整 */
[data-bs-theme="dark"] {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-accent: #404040;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --border-color: rgba(255, 255, 255, 0.1);
    --shadow-color: rgba(0, 0, 0, 0.5);
}

/* 颜色预览圆圈 */
.color-preview {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: inline-block;
    border: 2px solid #dee2e6;
    margin-right: 8px;
    transition: transform 0.2s ease;
}

.dropdown-item:hover .color-preview {
    transform: scale(1.1);
}

/* 分隔线样式 */
.dropdown-divider-custom {
    height: 1px;
    margin: 0.5rem 0;
    overflow: hidden;
    background-color: var(--border-color);
}

/* 主题部分标题 */
.theme-section-title {
    font-size: 0.75rem;
    color: var(--text-secondary);
    padding: 0.25rem 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--bg-accent);
    border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* 置顶按钮适配 */
.btn-scroll-top {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

.btn-scroll-top:hover {
    background-color: var(--bg-accent) !important;
}

/* 激活指示器 */
.item-active-indicator {
    color: var(--text-primary) !important;
}

/* 头像边框适配 */
[data-bg-theme]:not([data-bg-theme="default"]) .rounded-circle + span[style*="background:#2196f3"] {
    border-color: var(--bg-secondary) !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .dropdown-menu {
        max-height: 60vh;
        overflow-y: auto;
    }
}

/* 平滑过渡效果 */
* {
    transition-property: background-color, color, border-color;
    transition-duration: 0.3s;
    transition-timing-function: ease;
}

/* 特定元素排除过渡 */
.shimmer,
.animate-target,
.position-absolute svg {
    transition: none !important;
}