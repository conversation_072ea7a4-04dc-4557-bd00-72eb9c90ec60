<!-- JavaScript库 -->
<script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>

<!-- 自定义JavaScript -->
<script>
// 全局配置
window.AppConfig = {
    baseUrl: '{{ url("/") }}',
    csrfToken: '{{ csrf_token() }}',
    currency: '{{ dujiaoka_config_get("global_currency", "元") }}',
    siteName: '{{ dujiaoka_config_get("title", "自动发卡平台") }}',
    debug: {{ config('app.debug') ? 'true' : 'false' }},
    user: @auth {
        id: {{ Auth::id() }},
        email: '{{ Auth::user()->email }}',
        money: {{ Auth::user()->money }},
        isLoggedIn: true
    } @else {
        isLoggedIn: false
    } @endauth
};

// 工具类
class NeonUtils {
    // 格式化金额
    static formatMoney(amount, currency = window.AppConfig.currency) {
        const formatted = (amount / 100).toFixed(2);
        return `${formatted} ${currency}`;
    }

    // 复制到剪贴板
    static async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showNotification('复制成功', 'success');
            return true;
        } catch (err) {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showNotification('复制成功', 'success');
            return true;
        }
    }

    // 显示通知
    static showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification ${type} fixed top-4 right-4 z-50 max-w-sm p-4 rounded-xl shadow-lg transform translate-x-full transition-transform duration-300`;

        const icons = {
            success: 'fas fa-check-circle text-green-400',
            error: 'fas fa-exclamation-circle text-red-400',
            warning: 'fas fa-exclamation-triangle text-yellow-400',
            info: 'fas fa-info-circle text-blue-400'
        };

        notification.innerHTML = `
            <div class="flex items-center space-x-3">
                <i class="${icons[type]}"></i>
                <span class="text-white font-medium">${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, duration);
    }

    // 显示加载状态
    static showLoading(message = '加载中...') {
        const loader = document.createElement('div');
        loader.id = 'globalLoader';
        loader.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        loader.innerHTML = `
            <div class="glass-effect rounded-xl p-8 text-center">
                <div class="loading-spinner mx-auto mb-4"></div>
                <div class="text-white font-medium">${message}</div>
            </div>
        `;
        document.body.appendChild(loader);
        document.body.style.overflow = 'hidden';
    }

    // 隐藏加载状态
    static hideLoading() {
        const loader = document.getElementById('globalLoader');
        if (loader) {
            loader.remove();
            document.body.style.overflow = '';
        }
    }

    // 确认对话框
    static async confirm(title, message, confirmText = '确定', cancelText = '取消') {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="modal-content bg-gray-800 rounded-xl p-6 max-w-md w-full mx-4">
                    <h3 class="text-xl font-bold text-white mb-4">${title}</h3>
                    <p class="text-gray-300 mb-6">${message}</p>
                    <div class="flex space-x-3 justify-end">
                        <button id="cancelBtn" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                            ${cancelText}
                        </button>
                        <button id="confirmBtn" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            ${confirmText}
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            document.body.style.overflow = 'hidden';

            modal.querySelector('#confirmBtn').onclick = () => {
                modal.remove();
                document.body.style.overflow = '';
                resolve(true);
            };

            modal.querySelector('#cancelBtn').onclick = () => {
                modal.remove();
                document.body.style.overflow = '';
                resolve(false);
            };

            modal.onclick = (e) => {
                if (e.target === modal) {
                    modal.remove();
                    document.body.style.overflow = '';
                    resolve(false);
                }
            };
        });
    }

    // 格式化时间
    static formatTime(timestamp) {
        const date = new Date(timestamp * 1000);
        const now = new Date();
        const diff = now - date;

        if (diff < 60000) return '刚刚';
        if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
        if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`;

        return date.toLocaleDateString('zh-CN');
    }

    // 防抖函数
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 节流函数
    static throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // 验证邮箱
    static validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    // 验证手机号
    static validatePhone(phone) {
        const re = /^1[3-9]\d{9}$/;
        return re.test(phone);
    }

    // 生成随机字符串
    static randomString(length = 8) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    // 本地存储
    static storage = {
        set(key, value, expiry = null) {
            const item = {
                value: value,
                expiry: expiry ? Date.now() + expiry : null
            };
            localStorage.setItem(key, JSON.stringify(item));
        },

        get(key) {
            const itemStr = localStorage.getItem(key);
            if (!itemStr) return null;

            const item = JSON.parse(itemStr);
            if (item.expiry && Date.now() > item.expiry) {
                localStorage.removeItem(key);
                return null;
            }

            return item.value;
        },

        remove(key) {
            localStorage.removeItem(key);
        },

        clear() {
            localStorage.clear();
        }
    };
}

// AJAX请求封装
class NeonAPI {
    static async request(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': window.AppConfig.csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        };

        const config = { ...defaultOptions, ...options };

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || '请求失败');
            }

            return data;
        } catch (error) {
            console.error('API请求错误:', error);
            throw error;
        }
    }

    static get(url, params = {}) {
        const urlParams = new URLSearchParams(params);
        const fullUrl = urlParams.toString() ? `${url}?${urlParams}` : url;
        return this.request(fullUrl);
    }

    static post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    static put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    static delete(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }
}

// 表单处理类
class NeonForm {
    constructor(formElement) {
        this.form = formElement;
        this.init();
    }

    init() {
        this.form.addEventListener('submit', this.handleSubmit.bind(this));
        this.addValidation();
    }

    async handleSubmit(e) {
        e.preventDefault();

        if (!this.validate()) {
            return;
        }

        const formData = new FormData(this.form);
        const data = Object.fromEntries(formData);

        try {
            NeonUtils.showLoading('提交中...');

            const response = await NeonAPI.post(this.form.action, data);

            NeonUtils.hideLoading();
            NeonUtils.showNotification(response.message || '提交成功', 'success');

            if (response.redirect) {
                setTimeout(() => {
                    window.location.href = response.redirect;
                }, 1000);
            }

        } catch (error) {
            NeonUtils.hideLoading();
            NeonUtils.showNotification(error.message || '提交失败', 'error');
        }
    }

    validate() {
        const inputs = this.form.querySelectorAll('input[required], select[required], textarea[required]');
        let isValid = true;

        inputs.forEach(input => {
            if (!input.value.trim()) {
                this.showFieldError(input, '此字段为必填项');
                isValid = false;
            } else if (input.type === 'email' && !NeonUtils.validateEmail(input.value)) {
                this.showFieldError(input, '请输入有效的邮箱地址');
                isValid = false;
            } else if (input.type === 'tel' && !NeonUtils.validatePhone(input.value)) {
                this.showFieldError(input, '请输入有效的手机号');
                isValid = false;
            } else {
                this.clearFieldError(input);
            }
        });

        return isValid;
    }

    showFieldError(input, message) {
        input.classList.add('border-red-500');

        let errorElement = input.parentElement.querySelector('.field-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'field-error text-red-400 text-sm mt-1';
            input.parentElement.appendChild(errorElement);
        }

        errorElement.textContent = message;
    }

    clearFieldError(input) {
        input.classList.remove('border-red-500');

        const errorElement = input.parentElement.querySelector('.field-error');
        if (errorElement) {
            errorElement.remove();
        }
    }

    addValidation() {
        const inputs = this.form.querySelectorAll('input, select, textarea');

        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                if (input.hasAttribute('required') && !input.value.trim()) {
                    this.showFieldError(input, '此字段为必填项');
                } else {
                    this.clearFieldError(input);
                }
            });

            input.addEventListener('input', () => {
                this.clearFieldError(input);
            });
        });
    }
}

// 购物车管理
class NeonCart {
    constructor() {
        this.items = NeonUtils.storage.get('cart') || [];
        this.updateCartUI();
    }

    addItem(product) {
        const existingItem = this.items.find(item => item.id === product.id);

        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            this.items.push({
                ...product,
                quantity: 1,
                addedAt: Date.now()
            });
        }

        this.saveCart();
        this.updateCartUI();
        NeonUtils.showNotification('商品已添加到购物车', 'success');
    }

    removeItem(productId) {
        this.items = this.items.filter(item => item.id !== productId);
        this.saveCart();
        this.updateCartUI();
        NeonUtils.showNotification('商品已从购物车移除', 'info');
    }

    updateQuantity(productId, quantity) {
        const item = this.items.find(item => item.id === productId);
        if (item) {
            item.quantity = Math.max(0, quantity);
            if (item.quantity === 0) {
                this.removeItem(productId);
            } else {
                this.saveCart();
                this.updateCartUI();
            }
        }
    }

    clear() {
        this.items = [];
        this.saveCart();
        this.updateCartUI();
    }

    getTotal() {
        return this.items.reduce((total, item) => total + (item.price * item.quantity), 0);
    }

    getItemCount() {
        return this.items.reduce((count, item) => count + item.quantity, 0);
    }

    saveCart() {
        NeonUtils.storage.set('cart', this.items, 7 * 24 * 60 * 60 * 1000); // 7天过期
    }

    updateCartUI() {
        const cartCountElements = document.querySelectorAll('.cart-count');
        const count = this.getItemCount();

        cartCountElements.forEach(element => {
            element.textContent = count;
            element.style.display = count > 0 ? 'flex' : 'none';
        });
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化购物车
    window.cart = new NeonCart();

    // 初始化表单
    document.querySelectorAll('form[data-ajax]').forEach(form => {
        new NeonForm(form);
    });

    // 初始化复制按钮
    document.querySelectorAll('[data-copy]').forEach(button => {
        button.addEventListener('click', function() {
            const text = this.dataset.copy || this.textContent;
            NeonUtils.copyToClipboard(text);
        });
    });

    // 初始化懒加载
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    observer.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }

    // 性能监控
    if (window.AppConfig.debug) {
        console.log('页面加载完成');
        console.log('用户信息:', window.AppConfig.user);
    }
});

// 全局错误处理
window.addEventListener('error', function(e) {
    if (window.AppConfig.debug) {
        console.error('JavaScript错误:', e.error);
    }
});

// 全局未处理的Promise拒绝
window.addEventListener('unhandledrejection', function(e) {
    if (window.AppConfig.debug) {
        console.error('未处理的Promise拒绝:', e.reason);
    }
});

// 导出到全局
window.NeonUtils = NeonUtils;
window.NeonAPI = NeonAPI;
window.NeonForm = NeonForm;
window.NeonCart = NeonCart;
</script>

<!-- 兼容性脚本 -->
<script>
// 旧版本兼容
if (typeof window.showNotification === 'undefined') {
    window.showNotification = NeonUtils.showNotification;
}

if (typeof window.showModal === 'undefined') {
    window.showModal = function(content, title) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="modal-content glass-effect rounded-xl p-6 max-w-2xl w-full mx-4">
                ${title ? `<h3 class="text-xl font-bold gradient-text mb-4">${title}</h3>` : ''}
                <div class="modal-body text-white">${content}</div>
                <div class="flex justify-end mt-6">
                    <button onclick="this.closest('.modal-overlay').remove(); document.body.style.overflow = ''" class="btn-primary">
                        确定
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        document.body.style.overflow = 'hidden';

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
                document.body.style.overflow = '';
            }
        });
    };
}
</script>
