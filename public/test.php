<?php
echo "PHP is working!<br>";
echo "Current time: " . date('Y-m-d H:i:s') . "<br>";
echo "PHP version: " . phpversion() . "<br>";

// 测试Laravel
try {
    require_once '../vendor/autoload.php';
    $app = require_once '../bootstrap/app.php';
    echo "Laravel bootstrap successful!<br>";
    
    // 测试数据库连接
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
    $request = Illuminate\Http\Request::capture();
    $response = $kernel->handle($request);
    
    echo "Laravel kernel loaded!<br>";
    
    // 测试数据库
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=aaaaaa', 'aaaaaa', 'aaaaaa');
    echo "Database connection successful!<br>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM goods");
    $count = $stmt->fetchColumn();
    echo "Goods count: " . $count . "<br>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "<br>";
}
?>
