<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_','-',strtolower(app()->getLocale())), false); ?>" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo e(isset($page_title) ? $page_title . ' | ' : '', false); ?><?php echo e(dujiaoka_config_get('title', '自动发卡平台'), false); ?></title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="<?php echo e(dujiaoka_config_get('description', '专业的自动发卡平台，提供安全、快速、可靠的数字商品交易服务'), false); ?>">
    <meta name="keywords" content="<?php echo e(dujiaoka_config_get('keywords', '自动发卡,数字商品,在线购买,安全交易'), false); ?>">
    <meta name="author" content="<?php echo e(dujiaoka_config_get('title', '自动发卡平台'), false); ?>">
    <meta name="robots" content="index, follow">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo e(isset($page_title) ? $page_title . ' | ' : '', false); ?><?php echo e(dujiaoka_config_get('title'), false); ?>">
    <meta property="og:description" content="<?php echo e(dujiaoka_config_get('description'), false); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo e(url()->current(), false); ?>">
    <meta property="og:site_name" content="<?php echo e(dujiaoka_config_get('title'), false); ?>">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo e(isset($page_title) ? $page_title . ' | ' : '', false); ?><?php echo e(dujiaoka_config_get('title'), false); ?>">
    <meta name="twitter:description" content="<?php echo e(dujiaoka_config_get('description'), false); ?>">

    <!-- Favicon and Icons -->
    <link rel="icon" type="image/png" href="/assets/neon/app-icons/icon-32x32.png" sizes="32x32">
    <link rel="apple-touch-icon" href="/assets/neon/app-icons/icon-180x180.png">
    <link rel="manifest" href="/manifest.json">

    <!-- Security Headers -->
    <?php if(\request()->getScheme() == "https"): ?>
        <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
    <?php endif; ?>
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">

    <!-- Performance Optimization -->
    <meta http-equiv="Cache-Control" content="max-age=31536000">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

    <!-- Preload Critical Resources -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" as="style">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style">
    <link rel="preload" href="/assets/neon/fonts/inter-variable-latin.woff2" as="font" type="font/woff2" crossorigin>

    <!-- CSS Libraries -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css">

    <!-- Custom Styles -->
    <style>
        /* 企业级深色主题样式 */
        :root {
            --primary-bg: #0a0e27;
            --secondary-bg: #1a1e3a;
            --accent-bg: #2a2e4a;
            --glass-bg: rgba(26, 30, 58, 0.8);
            --text-primary: #ffffff;
            --text-secondary: #b8c5d6;
            --text-muted: #8892a6;
            --border-color: rgba(255, 255, 255, 0.1);
            --glow-primary: #00d4ff;
            --glow-secondary: #ff6b9d;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
        }

        /* 全局样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            font-size: 16px;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 50%, var(--accent-bg) 100%);
            background-attachment: fixed;
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        /* 星空背景动画 */
        .stars-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
        }

        .star {
            position: absolute;
            width: 2px;
            height: 2px;
            background: white;
            border-radius: 50%;
            animation: twinkle 3s infinite;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0; transform: scale(0.5); }
            50% { opacity: 1; transform: scale(1); }
        }

        /* 玻璃态效果 */
        .glass-effect {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        /* 发光效果 */
        .glow-effect {
            box-shadow: 0 0 20px var(--glow-primary);
            animation: glow-pulse 2s ease-in-out infinite alternate;
        }

        @keyframes glow-pulse {
            from { box-shadow: 0 0 20px var(--glow-primary); }
            to { box-shadow: 0 0 30px var(--glow-primary), 0 0 40px var(--glow-primary); }
        }

        /* 渐变文字 */
        .gradient-text {
            background: linear-gradient(135deg, var(--glow-primary) 0%, var(--glow-secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, var(--glow-primary) 0%, var(--glow-secondary) 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
        }

        /* 卡片样式 */
        .card-modern {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: 24px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .card-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(255, 107, 157, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .card-modern:hover::before {
            opacity: 1;
        }

        .card-modern:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        /* 导航栏样式 */
        .navbar-glass {
            background: rgba(10, 14, 39, 0.9);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
        }

        /* 页脚样式 */
        .footer-glass {
            background: rgba(10, 14, 39, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid var(--border-color);
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--primary-bg);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--glow-primary), var(--glow-secondary));
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--glow-secondary), var(--glow-primary));
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            html {
                font-size: 14px;
            }

            .card-modern {
                padding: 16px;
                border-radius: 16px;
            }

            .btn-primary {
                padding: 10px 20px;
                border-radius: 10px;
            }
        }

        /* 加载动画 */
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--glow-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 页面过渡动画 */
        .page-transition {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 表单样式 */
        .form-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 12px 16px;
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--glow-primary);
            box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
        }

        .form-input::placeholder {
            color: var(--text-muted);
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
            padding: 16px 20px;
            border-radius: 12px;
            color: white;
            font-weight: 500;
            animation: slideInRight 0.3s ease-out;
        }

        .notification.success {
            background: linear-gradient(135deg, var(--success-color), #059669);
        }

        .notification.warning {
            background: linear-gradient(135deg, var(--warning-color), #d97706);
        }

        .notification.error {
            background: linear-gradient(135deg, var(--error-color), #dc2626);
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 9998;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease-out;
        }

        .modal-content {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: 32px;
            max-width: 500px;
            width: 90%;
            animation: scaleIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }
    </style>

    <?php echo $__env->yieldContent('styles'); ?>
</head>

<body class="bg-pattern page-transition">
    <!-- 星空背景 -->
    <div class="stars-container" id="starsContainer"></div>

    <!-- 页面加载器 -->
    <div id="pageLoader" class="fixed inset-0 bg-gray-900 bg-opacity-90 flex items-center justify-content z-50">
        <div class="loading-spinner mx-auto"></div>
    </div>

    <!-- 主要内容包装器 -->
    <div class="min-h-screen flex flex-col relative z-10">
        <!-- 导航栏 -->
        <?php echo $__env->make('neon.layouts._nav', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <!-- 主要内容区域 -->
        <main class="flex-1">
            <?php echo $__env->yieldContent('content'); ?>
        </main>

        <!-- 页脚 -->
        <?php echo $__env->make('neon.layouts._footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    </div>

    <!-- 返回顶部按钮 -->
    <button id="backToTop" class="fixed bottom-6 right-6 w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full shadow-lg opacity-0 transition-all duration-300 hover:scale-110 z-40">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <?php echo $__env->make('neon.layouts._script', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- 全局JavaScript -->
    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 隐藏加载器
            setTimeout(() => {
                document.getElementById('pageLoader').style.display = 'none';
            }, 500);

            // 初始化AOS动画
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true,
                offset: 100
            });

            // 创建星空背景
            createStars();

            // 初始化返回顶部按钮
            initBackToTop();

            // 初始化通知系统
            initNotificationSystem();
        });

        // 创建星空背景
        function createStars() {
            const container = document.getElementById('starsContainer');
            const starCount = 200;

            for (let i = 0; i < starCount; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.animationDelay = Math.random() * 3 + 's';
                star.style.animationDuration = (Math.random() * 3 + 2) + 's';
                container.appendChild(star);
            }
        }

        // 返回顶部功能
        function initBackToTop() {
            const backToTopBtn = document.getElementById('backToTop');

            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    backToTopBtn.style.opacity = '1';
                    backToTopBtn.style.transform = 'translateY(0)';
                } else {
                    backToTopBtn.style.opacity = '0';
                    backToTopBtn.style.transform = 'translateY(20px)';
                }
            });

            backToTopBtn.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // 通知系统
        function initNotificationSystem() {
            window.showNotification = function(message, type = 'success', duration = 5000) {
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.innerHTML = `
                    <div class="flex items-center justify-between">
                        <span>${message}</span>
                        <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;

                document.body.appendChild(notification);

                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, duration);
            };
        }

        // 模态框系统
        window.showModal = function(content, title = '') {
            const overlay = document.createElement('div');
            overlay.className = 'modal-overlay';
            overlay.innerHTML = `
                <div class="modal-content">
                    ${title ? `<h3 class="text-xl font-bold mb-4 gradient-text">${title}</h3>` : ''}
                    <div class="modal-body">${content}</div>
                    <div class="flex justify-end mt-6">
                        <button onclick="this.closest('.modal-overlay').remove()" class="btn-primary">
                            确定
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(overlay);

            // 点击背景关闭
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    overlay.remove();
                }
            });
        };

        // 全局错误处理
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e.error);
        });

        // 性能监控
        window.addEventListener('load', function() {
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            console.log('页面加载时间:', loadTime + 'ms');
        });
    </script>

    <?php echo $__env->yieldContent('scripts'); ?>
</body>
</html>

<?php /**PATH /www/wwwroot/www.bgoom.top/resources/views/neon/layouts/default.blade.php ENDPATH**/ ?>