<?php

namespace <PERSON>jiaoka\Currency;

class Currency
{
    /**
     * 获取汇率
     */
    public function getRate(string $from, string $to): float
    {
        // 简单的汇率实现，实际项目中应该连接真实的汇率API
        $rates = [
            'USD' => 1.0,
            'CNY' => 7.2,
            'EUR' => 0.85,
            'GBP' => 0.73,
        ];

        $fromRate = $rates[$from] ?? 1.0;
        $toRate = $rates[$to] ?? 1.0;

        return $toRate / $fromRate;
    }

    /**
     * 转换货币
     */
    public function convert(float $amount, string $from, string $to): float
    {
        $rate = $this->getRate($from, $to);
        return $amount * $rate;
    }
}
