<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('goods_group', function (Blueprint $table) {
            $table->string('picture')->nullable()->comment('分类图片');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('goods_group', function (Blueprint $table) {
            $table->dropColumn('picture');
        });
    }
};
