// theme-colors.js - 多彩背景主题切换脚本
// 保存路径: /assets/js/theme-colors.js

(function() {
    'use strict';
    
    // 背景主题管理器
    const BgThemeManager = {
        // 默认主题
        defaultTheme: 'default',
        
        // 存储键名
        storageKey: 'dujiaoka_bg_theme',
        
        // 所有可用的背景主题
        themes: {
            'default': { name: '默认白色', color: '#ffffff' },
            'pink': { name: '独角粉', color: '#ff66b2' },
            'yellow': { name: '活力黄', color: '#ffeb3b' },
            'red': { name: '热情红', color: '#ff5757' },
            'deepblue': { name: '深邃蓝', color: '#2c3e50' },
            'black': { name: '炫酷黑', color: '#1a1a1a' },
            'purple': { name: '魅力紫', color: '#9c27b0' },
            'lightblue': { name: '天空蓝', color: '#2196f3' }
        },
        
        // 初始化
        init: function() {
            // 获取保存的主题
            const savedTheme = this.getSavedTheme();
            
            // 应用主题
            this.applyTheme(savedTheme);
            
            // 绑定事件
            this.bindEvents();
            
            // 监听系统主题变化
            this.watchSystemTheme();
            
            // 监听 Bootstrap 主题切换
            this.watchBootstrapTheme();
        },
        
        // 获取保存的主题
        getSavedTheme: function() {
            try {
                const saved = localStorage.getItem(this.storageKey);
                // 验证保存的主题是否有效
                if (saved && this.themes[saved]) {
                    return saved;
                }
                return this.defaultTheme;
            } catch (e) {
                console.warn('localStorage not available:', e);
                return this.defaultTheme;
            }
        },
        
        // 保存主题
        saveTheme: function(theme) {
            try {
                localStorage.setItem(this.storageKey, theme);
            } catch (e) {
                console.warn('Failed to save theme:', e);
            }
        },
        
        // 应用主题
        applyTheme: function(theme) {
            // 验证主题
            if (!this.themes[theme]) {
                theme = this.defaultTheme;
            }
            
            // 设置 data 属性
            document.documentElement.setAttribute('data-bg-theme', theme);
            
            // 更新激活状态
            this.updateActiveState(theme);
            
            // 更新 meta 主题色
            this.updateMetaThemeColor(theme);
            
            // 触发自定义事件
            this.dispatchThemeChange(theme);
        },
        
        // 更新激活状态
        updateActiveState: function(theme) {
            const items = document.querySelectorAll('.bg-color-item');
            
            items.forEach(item => {
                const itemTheme = item.getAttribute('data-bg-color');
                const indicator = item.querySelector('.item-active-indicator');
                
                if (itemTheme === theme) {
                    item.classList.add('active');
                    item.setAttribute('aria-pressed', 'true');
                    if (indicator) {
                        indicator.classList.remove('d-none');
                    }
                } else {
                    item.classList.remove('active');
                    item.setAttribute('aria-pressed', 'false');
                    if (indicator) {
                        indicator.classList.add('d-none');
                    }
                }
            });
        },
        
        // 更新 meta 主题色
        updateMetaThemeColor: function(theme) {
            let metaThemeColor = document.querySelector('meta[name="theme-color"]');
            
            if (!metaThemeColor) {
                metaThemeColor = document.createElement('meta');
                metaThemeColor.name = 'theme-color';
                document.head.appendChild(metaThemeColor);
            }
            
            const themeData = this.themes[theme];
            if (themeData) {
                metaThemeColor.content = themeData.color;
            }
        },
        
        // 绑定事件
        bindEvents: function() {
            // 使用事件委托监听背景色切换
            document.addEventListener('click', (e) => {
                const bgColorItem = e.target.closest('.bg-color-item');
                
                if (bgColorItem) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    const theme = bgColorItem.getAttribute('data-bg-color');
                    this.changeTheme(theme);
                    
                    // 不关闭下拉菜单
                    return false;
                }
            });
            
            // 防止点击背景色选项时关闭下拉菜单
            const dropdownMenu = document.querySelector('.theme-switcher + .dropdown-menu');
            if (dropdownMenu) {
                dropdownMenu.addEventListener('click', (e) => {
                    if (e.target.closest('.bg-color-item')) {
                        e.stopPropagation();
                    }
                });
            }
        },
        
        // 切换主题
        changeTheme: function(theme) {
            // 验证主题
            if (!this.themes[theme]) {
                console.warn('Invalid theme:', theme);
                return;
            }
            
            // 保存主题
            this.saveTheme(theme);
            
            // 应用主题
            this.applyTheme(theme);
            
            // 添加切换动画效果
            document.body.style.transition = 'background-color 0.3s ease';
        },
        
        // 监听系统主题变化
        watchSystemTheme: function() {
            if (window.matchMedia) {
                const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
                
                darkModeQuery.addEventListener('change', (e) => {
                    this.onSystemThemeChange(e.matches);
                });
            }
        },
        
        // 监听 Bootstrap 主题切换
        watchBootstrapTheme: function() {
            // 监听 Bootstrap 主题切换事件
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'attributes' && 
                        mutation.attributeName === 'data-bs-theme') {
                        this.onBootstrapThemeChange(mutation.target.getAttribute('data-bs-theme'));
                    }
                });
            });
            
            // 观察 html 元素的属性变化
            observer.observe(document.documentElement, {
                attributes: true,
                attributeFilter: ['data-bs-theme']
            });
        },
        
        // 系统主题变化处理
        onSystemThemeChange: function(isDark) {
            const currentBgTheme = this.getCurrentTheme();
            
            // 如果当前是自动模式，可以在这里添加逻辑
            const bsTheme = document.documentElement.getAttribute('data-bs-theme');
            
            if (bsTheme === 'auto') {
                // 在自动模式下，可以根据系统主题调整背景色
                // 例如：深色模式下自动切换到深色背景
                if (isDark && ['default', 'yellow', 'pink'].includes(currentBgTheme)) {
                    // 可选：自动切换到深色背景
                    // this.changeTheme('black');
                }
            }
        },
        
        // Bootstrap 主题变化处理
        onBootstrapThemeChange: function(bsTheme) {
            // 可以在这里添加与 Bootstrap 主题联动的逻辑
            console.log('Bootstrap theme changed to:', bsTheme);
        },
        
        // 触发主题变化事件
        dispatchThemeChange: function(theme) {
            const event = new CustomEvent('bgThemeChange', {
                detail: { 
                    theme: theme,
                    themeData: this.themes[theme]
                }
            });
            document.dispatchEvent(event);
        },
        
        // 获取当前主题
        getCurrentTheme: function() {
            return document.documentElement.getAttribute('data-bg-theme') || this.defaultTheme;
        },
        
        // 获取所有可用主题
        getAvailableThemes: function() {
            return Object.keys(this.themes);
        },
        
        // 获取主题信息
        getThemeInfo: function(theme) {
            return this.themes[theme] || null;
        },
        
        // 重置为默认主题
        resetToDefault: function() {
            this.changeTheme(this.defaultTheme);
        }
    };
    
    // DOM 加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => BgThemeManager.init());
    } else {
        BgThemeManager.init();
    }
    
    // 导出到全局作用域
    window.BgThemeManager = BgThemeManager;
    
})();

// API 使用示例：
// BgThemeManager.getCurrentTheme() - 获取当前主题
// BgThemeManager.changeTheme('pink') - 切换到粉色主题
// BgThemeManager.getAvailableThemes() - 获取所有可用主题
// BgThemeManager.resetToDefault() - 重置为默认主题

// 监听主题变化事件示例：
// document.addEventListener('bgThemeChange', (e) => {
//     console.log('Background theme changed to:', e.detail.theme);
//     console.log('Theme data:', e.detail.themeData);
// });