<!-- 企业级现代化页脚 -->
<footer class="footer-glass mt-20 relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
        <div class="absolute top-10 left-10 w-32 h-32 bg-blue-500 rounded-full blur-3xl opacity-20"></div>
        <div class="absolute bottom-10 right-10 w-40 h-40 bg-purple-500 rounded-full blur-3xl opacity-20"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 relative z-10">
        <!-- 主要内容区域 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            <!-- 公司信息 -->
            <div class="space-y-6">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center glow-effect">
                        @if(dujiaoka_config_get('img_logo'))
                            <img src="{{ picture_url(dujiaoka_config_get('img_logo')) }}"
                                 alt="Logo"
                                 class="w-8 h-8 object-contain">
                        @else
                            <i class="fas fa-star text-white text-xl"></i>
                        @endif
                    </div>
                    <div>
                        <h3 class="text-xl font-bold gradient-text">
                            {{ dujiaoka_config_get('text_logo', dujiaoka_config_get('title', '自动发卡平台')) }}
                        </h3>
                    </div>
                </div>

                <p class="text-gray-400 leading-relaxed">
                    {{ dujiaoka_config_get('description', '专业的自动发卡平台，提供安全、快速、可靠的数字商品交易服务。我们致力于为用户提供最优质的购物体验。') }}
                </p>

                <!-- 统计数据 -->
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center p-4 bg-white bg-opacity-5 rounded-xl">
                        <div class="text-2xl font-bold text-blue-400">10K+</div>
                        <div class="text-sm text-gray-400">用户信赖</div>
                    </div>
                    <div class="text-center p-4 bg-white bg-opacity-5 rounded-xl">
                        <div class="text-2xl font-bold text-green-400">99.9%</div>
                        <div class="text-sm text-gray-400">成功率</div>
                    </div>
                </div>
            </div>

            <!-- 快速链接 -->
            <div class="space-y-6">
                <h4 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-link mr-2 text-blue-400"></i>
                    快速链接
                </h4>
                <ul class="space-y-3">
                    <li>
                        <a href="{{ url('/') }}" class="text-gray-400 hover:text-white transition-colors duration-300 flex items-center group">
                            <i class="fas fa-home w-4 mr-2 text-blue-400 group-hover:scale-110 transition-transform"></i>
                            首页
                        </a>
                    </li>
                    <li>
                        <a href="{{ url('/article') }}" class="text-gray-400 hover:text-white transition-colors duration-300 flex items-center group">
                            <i class="fas fa-newspaper w-4 mr-2 text-green-400 group-hover:scale-110 transition-transform"></i>
                            帮助中心
                        </a>
                    </li>
                    <li>
                        <a href="{{ url('/search-order') }}" class="text-gray-400 hover:text-white transition-colors duration-300 flex items-center group">
                            <i class="fas fa-search w-4 mr-2 text-purple-400 group-hover:scale-110 transition-transform"></i>
                            订单查询
                        </a>
                    </li>
                    <li>
                        <a href="{{ url('/user/invite') }}" class="text-gray-400 hover:text-white transition-colors duration-300 flex items-center group">
                            <i class="fas fa-gift w-4 mr-2 text-pink-400 group-hover:scale-110 transition-transform"></i>
                            邀请返利
                        </a>
                    </li>
                </ul>
            </div>

            <!-- 商品分类 -->
            <div class="space-y-6">
                <h4 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-shopping-bag mr-2 text-green-400"></i>
                    热门分类
                </h4>
                <ul class="space-y-3">
                    <li>
                        <a href="{{ url('/?category=gift') }}" class="text-gray-400 hover:text-white transition-colors duration-300 flex items-center group">
                            <i class="fas fa-gift w-4 mr-2 text-blue-400 group-hover:scale-110 transition-transform"></i>
                            数字礼品卡
                        </a>
                    </li>
                    <li>
                        <a href="{{ url('/?category=game') }}" class="text-gray-400 hover:text-white transition-colors duration-300 flex items-center group">
                            <i class="fas fa-gamepad w-4 mr-2 text-green-400 group-hover:scale-110 transition-transform"></i>
                            游戏充值卡
                        </a>
                    </li>
                    <li>
                        <a href="{{ url('/?category=mobile') }}" class="text-gray-400 hover:text-white transition-colors duration-300 flex items-center group">
                            <i class="fas fa-mobile-alt w-4 mr-2 text-purple-400 group-hover:scale-110 transition-transform"></i>
                            话费充值
                        </a>
                    </li>
                    <li>
                        <a href="{{ url('/?category=software') }}" class="text-gray-400 hover:text-white transition-colors duration-300 flex items-center group">
                            <i class="fas fa-laptop w-4 mr-2 text-pink-400 group-hover:scale-110 transition-transform"></i>
                            软件激活码
                        </a>
                    </li>
                </ul>
            </div>

            <!-- 联系我们 -->
            <div class="space-y-6">
                <h4 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-headset mr-2 text-purple-400"></i>
                    联系我们
                </h4>

                <!-- 客服信息 -->
                <div class="space-y-4">
                    <div class="flex items-center space-x-3 p-3 bg-white bg-opacity-5 rounded-xl hover:bg-opacity-10 transition-colors">
                        <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                            <i class="fab fa-qq text-white"></i>
                        </div>
                        <div>
                            <div class="text-white font-medium">QQ客服</div>
                            <div class="text-sm text-gray-400">在线咨询</div>
                        </div>
                    </div>

                    <div class="flex items-center space-x-3 p-3 bg-white bg-opacity-5 rounded-xl hover:bg-opacity-10 transition-colors">
                        <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                            <i class="fab fa-weixin text-white"></i>
                        </div>
                        <div>
                            <div class="text-white font-medium">微信客服</div>
                            <div class="text-sm text-gray-400">扫码咨询</div>
                        </div>
                    </div>

                    <a href="https://t.me/riniba" target="_blank" class="flex items-center space-x-3 p-3 bg-white bg-opacity-5 rounded-xl hover:bg-opacity-10 transition-colors group">
                        <div class="w-10 h-10 bg-blue-400 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                            <i class="fab fa-telegram text-white"></i>
                        </div>
                        <div>
                            <div class="text-white font-medium">Telegram</div>
                            <div class="text-sm text-gray-400">即时通讯</div>
                        </div>
                    </a>
                </div>

                <!-- 工作时间 -->
                <div class="p-4 bg-gradient-to-r from-blue-500 to-purple-600 bg-opacity-20 rounded-xl border border-white border-opacity-10">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-clock text-blue-400 mr-2"></i>
                        <span class="text-white font-medium">服务时间</span>
                    </div>
                    <div class="text-sm text-gray-300">
                        周一至周日 9:00-23:00<br>
                        节假日正常服务
                    </div>
                </div>
            </div>
        </div>

        <!-- 分隔线 -->
        <div class="border-t border-white border-opacity-10 my-8"></div>

        <!-- 底部信息 -->
        <div class="flex flex-col lg:flex-row justify-between items-center space-y-6 lg:space-y-0">
            <!-- 版权信息 -->
            <div class="text-center lg:text-left">
                <p class="text-gray-400 mb-2">
                    © {{ date('Y') }} {{ dujiaoka_config_get('title', '自动发卡平台') }}. All rights reserved.
                </p>
                <p class="text-sm text-gray-500">
                    Made with <i class="fas fa-heart text-red-500 mx-1 animate-pulse"></i> by
                    <a href="https://t.me/riniba" target="_blank" class="text-blue-400 hover:text-blue-300 transition-colors">@riniba</a>
                </p>
            </div>

            <!-- 法律链接 -->
            <div class="flex flex-wrap justify-center lg:justify-end items-center space-x-6">
                <a href="#" onclick="showModal('用户隐私政策内容...', '用户隐私政策')" class="text-gray-400 hover:text-white transition-colors text-sm">
                    用户隐私
                </a>
                <a href="#" onclick="showModal('服务条款内容...', '服务条款')" class="text-gray-400 hover:text-white transition-colors text-sm">
                    服务条款
                </a>
                <a href="#" onclick="showModal('法律声明内容...', '法律声明')" class="text-gray-400 hover:text-white transition-colors text-sm">
                    法律声明
                </a>
            </div>

            <!-- 社交媒体 -->
            <div class="flex items-center space-x-4">
                <span class="text-gray-400 text-sm">关注我们:</span>
                <div class="flex space-x-3">
                    <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center text-gray-400 hover:text-white hover:bg-blue-500 transition-all duration-300 group">
                        <i class="fab fa-youtube group-hover:scale-110 transition-transform"></i>
                    </a>
                    <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center text-gray-400 hover:text-white hover:bg-blue-600 transition-all duration-300 group">
                        <i class="fab fa-facebook group-hover:scale-110 transition-transform"></i>
                    </a>
                    <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center text-gray-400 hover:text-white hover:bg-pink-500 transition-all duration-300 group">
                        <i class="fab fa-instagram group-hover:scale-110 transition-transform"></i>
                    </a>
                    <a href="https://t.me/riniba" target="_blank" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center text-gray-400 hover:text-white hover:bg-blue-400 transition-all duration-300 group">
                        <i class="fab fa-telegram group-hover:scale-110 transition-transform"></i>
                    </a>
                    <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center text-gray-400 hover:text-white hover:bg-red-500 transition-all duration-300 group">
                        <i class="fab fa-pinterest group-hover:scale-110 transition-transform"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- 额外信息 -->
        <div class="mt-8 pt-6 border-t border-white border-opacity-5">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div class="flex items-center justify-center space-x-3">
                    <div class="w-12 h-12 bg-green-500 bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-shield-alt text-green-400 text-xl"></i>
                    </div>
                    <div class="text-left">
                        <div class="text-white font-medium">安全保障</div>
                        <div class="text-sm text-gray-400">SSL加密传输</div>
                    </div>
                </div>

                <div class="flex items-center justify-center space-x-3">
                    <div class="w-12 h-12 bg-blue-500 bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-bolt text-blue-400 text-xl"></i>
                    </div>
                    <div class="text-left">
                        <div class="text-white font-medium">极速发货</div>
                        <div class="text-sm text-gray-400">自动化处理</div>
                    </div>
                </div>

                <div class="flex items-center justify-center space-x-3">
                    <div class="w-12 h-12 bg-purple-500 bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-headset text-purple-400 text-xl"></i>
                    </div>
                    <div class="text-left">
                        <div class="text-white font-medium">24/7客服</div>
                        <div class="text-sm text-gray-400">全天候支持</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部装饰线 -->
    <div class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 opacity-50"></div>
</footer>

<!-- 页脚相关样式 -->
<style>
    .footer-glass {
        background: rgba(10, 14, 39, 0.95);
        backdrop-filter: blur(20px);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    /* 链接悬停效果 */
    .footer-glass a:hover {
        transform: translateY(-1px);
    }

    /* 社交媒体图标动画 */
    .footer-glass .fab:hover {
        animation: bounce 0.6s ease-in-out;
    }

    @keyframes bounce {
        0%, 20%, 60%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        80% {
            transform: translateY(-5px);
        }
    }

    /* 统计数据动画 */
    .footer-glass .text-2xl {
        animation: countUp 2s ease-out;
    }

    @keyframes countUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>

<!-- 页脚JavaScript -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 统计数据动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const target = entry.target;
                    const finalValue = target.textContent;
                    animateValue(target, 0, parseInt(finalValue.replace(/\D/g, '')), 2000, finalValue.includes('%') ? '%' : finalValue.includes('K') ? 'K+' : '');
                }
            });
        }, observerOptions);

        // 观察统计数字
        document.querySelectorAll('.footer-glass .text-2xl').forEach(el => {
            observer.observe(el);
        });

        // 数字动画函数
        function animateValue(element, start, end, duration, suffix = '') {
            let startTimestamp = null;
            const step = (timestamp) => {
                if (!startTimestamp) startTimestamp = timestamp;
                const progress = Math.min((timestamp - startTimestamp) / duration, 1);
                const current = Math.floor(progress * (end - start) + start);
                element.textContent = current + suffix;
                if (progress < 1) {
                    window.requestAnimationFrame(step);
                }
            };
            window.requestAnimationFrame(step);
        }

        // 客服链接点击事件
        document.querySelectorAll('.footer-glass [data-service]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const service = this.dataset.service;
                openCustomerService(service);
            });
        });
    });
</script>
