const body = jQuery("body")
  , ri = {
    init: function() {
        ri.lazyLoading(),
        ri.backTotop(),
        ri.offCanvas(),
        ri.stickyHeader(),
        ri.stickyBar(),
        ri.pagination(),
        ri.owlCarousel(),
        ri.gallerylight(),
        ri.CodeHighlight(),
        ri.add_post_views(),
        ri.social_action(),
        ri.add_comment(),
        ri.setIframeHeight(),
        ri.PostContents(),
        ri.MediaPreview(),
        ri.pay_action(),
        ri.changeOrder(),
        ri.account_action()
    },
    intervalId: null,
    currentPopup: null,
    ajax: function({data: e, before: t=()=>{}
    , result: n=()=>{}
    , complete: a=()=>{}
    }) {
        jQuery.ajax({
            url: zb.ajax_url,
            data: e,
            type: "post",
            dataType: "json",
            async: !0,
            success: n,
            error: function(e) {
                ri.notice(e.responseText, 500)
            },
            beforeSend: t,
            complete: a
        })
    },
    notice: function(e="", t=220, n=2e3) {
        let a = jQuery(".ri-notice");
        !e && a.length ? a.clearQueue().stop().hide() : (!a.length && e && (a = jQuery(`<div class="ri-notice" style="min-width: ${t}px"></div>`),
        body.append(a)),
        a.clearQueue().stop().hide().html(e).fadeIn().delay(n).fadeOut())
    },
    popup: function(e, t=240, n=!1, a=null) {
        const i = jQuery(`<div class="ri-popup"><div class="ri-popup-body" ${t ? `style="width:${t}px"` : ""}><div class="ri-popup-close"><span class="svg-close"></span></div><div class="ri-popup-content">${e}</div></div></div>`)
          , o = (ri.currentPopup && ri.currentPopup.remove(),
        ri.currentPopup = i,
        ()=>{
            body.removeClass("ri-popup-open"),
            ri.dimmer("close", 100),
            i.remove()
        }
        );
        if (e) {
            body.removeClass("ri-popup-open").append(i),
            ri.notice(!1),
            setTimeout(()=>{
                body.addClass("ri-popup-open"),
                ri.dimmer("open", 100)
            }
            , 10);
            i.on("click touchstart", e=>{
                (n && !jQuery(e.target).closest(".ri-popup-body").length || jQuery(e.target).closest(".ri-popup-close .svg-close").length) && (e.preventDefault(),
                (a || o)(),
                o())
            }
            )
        } else
            o()
    },
    dimmer: function(e, t=300) {
        var n = jQuery(".dimmer");
        switch (e) {
        case "open":
            n.fadeIn(t);
            break;
        case "close":
            n.fadeOut(t)
        }
    },
    backTotop: function() {
        let e;
        const t = jQuery(".back-top");
        t.length && (window.addEventListener("scroll", function() {
            400 <= (e = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop) ? t.addClass("back-top-show") : t.removeClass("back-top-show")
        }),
        t.on("click", function(e) {
            e.preventDefault(),
            jQuery("html, body").animate({
                scrollTop: 0
            }, "smooth")
        }))
    },
    offCanvas: function() {
        var e = jQuery(".burger")
          , t = jQuery(".canvas-close")
          , n = jQuery(".dimmer")
          , a = {
            label: "",
            prependTo: ".mobile-menu",
            closedSymbol: '<i class="fas fa-angle-down">',
            openedSymbol: '<i class="fas fa-angle-up">'
        }
          , i = body.hasClass("uc-page") ? ".uc-menu-warp" : ".main-menu .nav-list";
        jQuery(i).slicknav(a),
        e.on("click", function() {
            body.toggleClass("canvas-opened"),
            body.addClass("canvas-visible"),
            ri.dimmer("open")
        }),
        t.on("click", function() {
            body.hasClass("canvas-opened") && (body.removeClass("canvas-opened"),
            ri.dimmer("close"))
        }),
        n.on("click", function() {
            body.hasClass("canvas-opened") && (body.removeClass("canvas-opened"),
            ri.dimmer("close"))
        }),
        jQuery(document).keyup(function(e) {
            27 == e.keyCode && body.hasClass("canvas-opened") && (body.removeClass("canvas-opened"),
            ri.dimmer("close"))
        })
    },
    stickyHeader: function() {
        const a = jQuery(".site-header");
        if (a.length) {
            const r = a.outerHeight();
            let n = 0;
            document.addEventListener("scroll", function(e) {
                var t = window.pageYOffset || document.documentElement.scrollTop;
                t > r ? (a.addClass("navbar-now"),
                t < n && (a.addClass("navbar-sticky"),
                a.removeClass("navbar-now"))) : (a.removeClass("navbar-now"),
                0 === t && a.removeClass("navbar-sticky")),
                n = t
            })
        }
        var e = jQuery(".site-header .nav-list")
          , t = window.location.href;
        e.find("li").removeClass("current-menu-item"),
        e.find(`a[href="${t}"]`).closest("li").addClass("current-menu-item");
        const n = jQuery(".toggle-notify");
        n.on("click", function(e) {
            e.preventDefault();
            const t = jQuery(this).find("i")
              , n = t.attr("class");
            ri.ajax({
                data: {
                    action: "zb_get_site_notify",
                    nonce: zb.ajax_nonce
                },
                before: ()=>{
                    t.removeClass().addClass("fas fa-fan fa-spin")
                }
                ,
                result: ({status: e, msg: t})=>{
                    1 == e ? ri.popup(t, 380, !0) : ri.notice(t)
                }
                ,
                complete: ()=>{
                    t.removeClass().addClass(n)
                }
            })
        }),
        1 == zb.site_notify_auto && (e = "_zb_site_notify_auto",
        t = window.location.hostname,
        jQuery.cookie(e) || (setTimeout(function() {
            n.click()
        }, 1500),
        jQuery.cookie(e, 1, {
            domain: t,
            path: "/"
        })));
        e = jQuery(".toggle-search");
        const i = jQuery(".navbar-search");
        e.on("click", function(e) {
            e.stopPropagation(),
            i.toggleClass("show")
        }),
        i.on("click", function(e) {
            e.stopPropagation()
        }),
        jQuery(document).click(function() {
            i.removeClass("show")
        });
        t = jQuery(".toggle-color");
        const o = window.location.hostname;
        t.click(function() {
            var e = jQuery(this).find(".show")
              , t = e.next(".toggle-color>span")
              , t = (0 === t.length && (t = jQuery(".toggle-color>span:first-child")),
            e.removeClass("show"),
            t.addClass("show"),
            e = t.data("mod"),
            jQuery("html").attr("data-bs-theme", e),
            jQuery(".logo-wrapper img.logo"));
            t.attr("src", t.data(e)),
            jQuery.cookie("_zb_current_site_color", e, {
                domain: o,
                path: "/"
            })
        })
    },
    stickyBar: function() {
        var e = jQuery("[data-sticky]")
          , t = e.siblings("[data-sticky-content]");
        e.height() < t.height() && e.length && e.theiaStickySidebar({
            sidebarBehavior: "stick-to-top",
            updateSidebarHeight: !1,
            additionalMarginTop: 30
        })
    },
    lazyLoading: function() {
        0 < jQuery(".lazy").length && (window.lazyLoadInstance = new LazyLoad({}))
    },
    setIframeHeight: function() {
        var e = jQuery(".post-content");
        const n = e.width();
        Array.from(e.find("iframe")).forEach(function(e) {
            var t = 9 * n / 16;
            jQuery(e).css({
                height: t,
                width: "100%"
            })
        })
    },
    heroVideoJs: function(t) {
        var e = document.querySelector(".video-js");
        const n = videojs(e)
          , a = (n.on("contextmenu", function(e) {
            e.preventDefault()
        }),
        n.ready(o),
        jQuery(".switch-video"))
          , i = jQuery(".video-title .title-span");
        function o() {
            var e = n.currentType();
            /^audio/.test(e) ? ((e = n.el().querySelector(".centered-html-cd")) || n.el().insertAdjacentHTML("beforeend", '<div class="centered-html-cd"><div class="souse-img"><div class="icon-cd"></div><div class="icon-left"></div></div></div>'),
            (e = n.el().querySelector(".centered-html-cd")) && e.addEventListener("click", function() {
                n.paused() ? n.play() : n.pause()
            }),
            n.on(["playing", "pause"], function() {
                var e = n.el().querySelector(".icon-cd")
                  , t = n.el().querySelector(".icon-left");
                e.classList.toggle("rotate", !n.paused()),
                t.classList.toggle("skewing", !n.paused())
            })) : ((e = n.el().querySelector(".centered-html-cd")) && e.parentNode.removeChild(e),
            n.off(["playing", "pause"]))
        }
        a.on("click", function() {
            var e;
            jQuery(this).hasClass("active") || (e = jQuery(this).data("index"),
            e = t[e],
            i.text(e.title),
            n.poster(e.img),
            e.src && (n.src({
                src: e.src,
                type: e.type
            }),
            n.play()),
            a.removeClass("active"),
            jQuery(this).addClass("active"),
            n.off("ready", o),
            n.ready(o))
        })
    },
    MediaPreview: function() {
        var i;
        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || (body.on("mouseenter mouseleave", ".entry-media.media-video", function(e) {
            var t = jQuery(this)
              , n = (t.find(".media-img"),
            t.find("video"))
              , a = t.find(".progress-bar");
            n.length <= 0 || (clearTimeout(i),
            "mouseenter" === e.type ? i = setTimeout(function() {
                t.find(".post-format-icon").css("opacity", 0),
                n[0].currentTime = 0,
                n.show(),
                n.on("timeupdate", function() {
                    var e = n[0].currentTime / n[0].duration * 100;
                    a.find(".progress").css("width", e + "%")
                }),
                n[0].play()
            }, 350) : "mouseleave" === e.type && (clearTimeout(i),
            n[0].pause(),
            n[0].currentTime = 0,
            n.hide(),
            a.find(".progress").css("width", "0%"),
            t.find(".post-format-icon").css("opacity", "1")))
        }),
        body.on("click", ".media-preview.audio", function(e) {
            e.preventDefault();
            var e = jQuery(this)
              , t = e.find("audio")[0]
              , n = e.find(".icon-cd")
              , a = e.find(".icon-left")
              , i = e.find(".progress-bar");
            jQuery(".media-preview.audio audio").not(t).each(function() {
                this.paused || (this.pause(),
                jQuery(this).siblings(".centered-html-cd").find(".icon-cd").removeClass("rotate"),
                jQuery(this).siblings(".centered-html-cd").find(".icon-left").removeClass("skewing"),
                jQuery(this).siblings(".progress-bar").find(".icon-left").removeClass("skewing"))
            }),
            t.addEventListener("timeupdate", function() {
                var e = t.currentTime / t.duration * 100;
                i.find(".progress").css("width", e + "%")
            }),
            t.paused ? (t.currentTime = 0,
            t.play(),
            n.addClass("rotate"),
            a.addClass("skewing")) : (t.pause(),
            t.currentTime = 0,
            n.removeClass("rotate"),
            a.removeClass("skewing"))
        }))
    },
    PostContents: function() {
        var e = jQuery(".post-buy-widget")
          , t = e.find(".ri-down-warp").data("resize")
          , n = jQuery(".post-content")
          , a = (new ClipboardJS(".copy-pwd",{
            text: function(e) {
                return e.getAttribute("data-pwd")
            }
        }).on("success", function(e) {
            ri.notice(zb.gettext.__copypwd)
        }),
        jQuery(window).width() < 992 && ("top" === t ? e.prependTo(n) : e.appendTo(n)),
        jQuery(".single-content-nav .nav-link").on("click", function(e) {
            e.preventDefault();
            e = jQuery(this).attr("href");
            jQuery(".nav-link").removeClass("active"),
            jQuery(this).addClass("active"),
            jQuery(".tab-pane").removeClass("show active"),
            jQuery(e).addClass("show active")
        }),
        !1);
        jQuery(window).scroll(function() {
            var e;
            !a && jQuery(".home-overview").length && (e = jQuery(window).height(),
            jQuery(".home-overview").offset().top - e < jQuery(window).scrollTop()) && (jQuery(".home-overview .count-num").each(function() {
                var e = parseInt(jQuery(this).text());
                jQuery(this).prop("Counter", 0).animate({
                    Counter: e
                }, {
                    duration: 1500,
                    easing: "swing",
                    step: function(e) {
                        jQuery(this).text("+" + Math.ceil(e))
                    }
                })
            }),
            a = !0)
        }),
        jQuery(".user-qiandao-action").on("click", function(e) {
            e.preventDefault();
            var t = jQuery(this).find("i")
              , n = t.attr("class")
              , e = {
                nonce: zb.ajax_nonce,
                action: "zb_user_qiandao"
            };
            ri.ajax({
                data: e,
                before: ()=>{
                    t.removeClass().addClass("fa fa-spinner fa-spin me-1")
                }
                ,
                result: ({status: e, msg: t})=>{
                    ri.notice(t),
                    1 == e && setTimeout(function() {
                        window.location.reload()
                    }, 2e3)
                }
                ,
                complete: ()=>{
                    t.removeClass().addClass(n)
                }
            })
        })
    },
    CodeHighlight: function() {
        if (0 != zb.singular_id) {
            var e = jQuery(".post-content")
              , t = jQuery(".sidebar")
              , e = e.find("h1, h2, h3");
            if (1 == zb.post_content_nav && 0 < e.length) {
                e = e.map(function(e) {
                    var t = jQuery(this);
                    let n = t.attr("id");
                    return n || (n = "header-" + e,
                    t.attr("id", n)),
                    {
                        level: parseInt(t.prop("tagName").substring(1)),
                        id: n,
                        text: t.text()
                    }
                }).get();
                const i = jQuery('<ul class="h-navList">');
                jQuery.each(e, function(e, t) {
                    var n = jQuery("<li>")
                      , a = jQuery("<a>").attr("href", "#" + t.id).text(t.text);
                    switch (t.level) {
                    case 1:
                        n.addClass("nav-h1");
                        break;
                    case 2:
                        n.addClass("nav-h2");
                        break;
                    case 3:
                        n.addClass("nav-h3")
                    }
                    n.append(a),
                    i.append(n)
                }),
                t.append(i)
            }
            document.querySelectorAll(".post-content pre").forEach(e=>{
                e.classList.contains("highlighted") || (e.classList.add("highlighted"),
                e.innerHTML = e.innerHTML.trim(),
                hljs.highlightElement(e))
            }
            )
        }
    },
    owlCarousel: function() {
        const t = {
            autoplay: !1,
            loop: !1,
            items: 1,
            margin: 10,
            lazyLoad: !1,
            nav: !0,
            dots: !0,
            navText: ['<i class="fa fa-angle-left"></i>', '<i class="fa fa-angle-right"></i>'],
            navElement: "div"
        };
        var e = jQuery(".owl-carousel");
        e.length && e.each(function() {
            var e = jQuery(this).data("config") || {}
              , e = $.extend({}, t, e);
            jQuery(this).owlCarousel(e)
        })
    },
    pagination: function() {
        var t, n;
        function a() {
            var e = jQuery(".posts-warp");
            const a = jQuery(".infinite-scroll-button")
              , i = jQuery(".infinite-scroll-status")
              , o = jQuery(".infinite-scroll-msg");
            var t = a.hasClass("infinite-click")
              , n = (a.hasClass("infinite-auto"),
            {
                append: ".posts-warp > *",
                debug: !1,
                hideNav: ".pagination",
                history: !1,
                path: ".pagination a.page-next",
                prefill: !0
            });
            a.length && (t && (n.button = ".infinite-scroll-button",
            n.prefill = !1,
            n.scrollThreshold = !1),
            e.on("request.infiniteScroll", function(e, t) {
                i.show()
            }),
            e.on("load.infiniteScroll", function(e, t, n) {
                i.hide()
            }),
            e.on("append.infiniteScroll", function(e, t, n, a, i) {
                window.lazyLoadInstance.update()
            }),
            e.on("last.infiniteScroll", function(e, t, n) {
                a.hide(),
                o.show()
            }),
            e.infiniteScroll(n))
        }
        0 !== jQuery(".posts-warp").length && (t = jQuery("<div>").append(jQuery(".posts-warp").clone()).html(),
        n = jQuery("<nav>").append(jQuery(".page-nav").clone()).html(),
        jQuery(".section-cat-navbtn .btn").click(function(e) {
            e.preventDefault(),
            jQuery(this).hasClass("active") || (jQuery(".section-cat-navbtn .btn").not(this).removeClass("active"),
            jQuery(this).addClass("active"),
            (e = jQuery(this).attr("href")) ? jQuery.get(e, function(e) {
                var t = jQuery(e).find(".posts-warp")
                  , e = jQuery(e).find(".page-nav");
                jQuery(".posts-warp").replaceWith(t),
                0 < e.length ? (jQuery(".page-nav").replaceWith(e),
                a()) : jQuery(".page-nav").hide(),
                window.lazyLoadInstance.update()
            }) : (jQuery(".posts-warp").replaceWith(t),
            window.lazyLoadInstance.update(),
            0 < n.length && (jQuery(".page-nav").replaceWith(n),
            a()),
            jQuery(".page-nav").show()))
        }),
        a())
    },
    add_post_views: function() {
        0 != zb.singular_id && jQuery.post(zb.ajax_url, {
            action: "zb_add_post_views",
            nonce: zb.ajax_nonce,
            post_id: zb.singular_id
        })
    },
    gallerylight: function() {
        var e = jQuery(".post-content");
        e.length && (e = e.find('a[href$=".jpg"], a[href$=".jpeg"], a[href$=".png"], a[href$=".gif"], a[href$=".webp"]')).length && e.click(function(e) {
            e.preventDefault();
            var t, e = jQuery(this).closest(".gallery");
            e.length ? (t = (e = e.find(".gallery-item")).index(jQuery(this).closest(".gallery-item")) + 1,
            e = e.map(function() {
                return {
                    title: jQuery(this).find(".gallery-caption").text(),
                    src: jQuery(this).find("a").attr("href")
                }
            }).get(),
            Spotlight.show(e, {
                index: t
            })) : Spotlight.show(jQuery(this))
        })
    },
    social_action: function() {
        const n = jQuery(".post-fav-btn")
          , a = jQuery(".post-like-btn")
          , i = jQuery(".post-share-btn")
          , o = "post_like_storage";
        new ClipboardJS(".user-select-all",{
            text: function(e) {
                return e.textContent
            }
        }).on("success", function(e) {
            ri.notice(zb.gettext.__copy_succes)
        }),
        a.on("click", function() {
            if (localStorage.getItem(o) == zb.singular_id)
                return a.addClass("disabled"),
                ri.notice(a.data("text")),
                !1;
            const e = jQuery(this).find("i")
              , t = e.attr("class");
            ri.ajax({
                data: {
                    action: "zb_add_like_post",
                    nonce: zb.ajax_nonce,
                    post_id: zb.singular_id
                },
                before: ()=>{
                    a.addClass("disabled"),
                    e.removeClass().addClass("fa fa-spinner fa-spin me-1")
                }
                ,
                result: ({status: e, msg: t})=>{
                    1 == e ? localStorage.setItem(o, zb.singular_id) : (a.removeClass("disabled"),
                    localStorage.removeItem(o)),
                    ri.notice(t)
                }
                ,
                complete: ()=>{
                    e.removeClass().addClass(t)
                }
            })
        }),
        n.on("click", function() {
            const e = jQuery(this).find("i")
              , t = e.attr("class");
            ri.ajax({
                data: {
                    action: "zb_add_fav_post",
                    nonce: zb.ajax_nonce,
                    is_add: n.data("is"),
                    post_id: zb.singular_id
                },
                before: ()=>{
                    n.addClass("disabled"),
                    e.removeClass().addClass("fa fa-spinner fa-spin me-1")
                }
                ,
                result: ({msg: e})=>{
                    ri.notice(e)
                }
                ,
                complete: ()=>{
                    e.removeClass().addClass(t)
                }
            })
        }),
        i.on("click", function() {
            const e = jQuery(this).find("i")
              , t = e.attr("class");
            ri.ajax({
                data: {
                    action: "zb_add_share_post",
                    nonce: zb.ajax_nonce,
                    post_id: zb.singular_id
                },
                before: ()=>{
                    i.addClass("disabled"),
                    e.removeClass().addClass("fa fa-spinner fa-spin me-1")
                }
                ,
                result: ({msg: e})=>{
                    ri.drawSharePoster(e.data, e.html)
                }
                ,
                complete: ()=>{
                    i.removeClass("disabled"),
                    e.removeClass().addClass(t)
                }
            })
        })
    },
    drawSharePoster: function(r, s) {
        const c = document.createElement("canvas")
          , l = c.getContext("2d")
          , e = 640
          , d = 180
          , t = "bold 24px Arial"
          , u = "18px Arial"
          , n = f(r.title, 600, 2, t)
          , a = f(r.desc, 600, 3, u)
          , p = 580 + 32 * (n.length + a.length + 1) + 80 + 14
          , i = (c.width = e,
        c.height = p,
        l.fillStyle = "#FFFFFF",
        l.fillRect(0, 0, e, p),
        new Image);
        function f(e, t, n, a) {
            var i = document.createElement("canvas").getContext("2d")
              , o = [];
            let r = "";
            i.font = a;
            var s = jQuery("<div>").html(e).text().split("");
            for (let e = 0; e < s.length; e++) {
                var c = s[e];
                if (i.measureText(r + c).width < t && (!n || o.length < n))
                    r,
                    r += c;
                else if (o.push(r),
                r = c,
                n && o.length === n)
                    break
            }
            return o.push(r),
            o
        }
        function m(t, n, a, i, o) {
            for (let e = 0; e < t.length; e++) {
                var r = t[e];
                o.fillText(r, n, a + e * i)
            }
        }
        i.src = r.img,
        i.crossOrigin = "anonymous",
        i.onerror = ()=>(console.log("thumbnailImg error"),
        ri.popup(s),
        !1),
        i.onload = ()=>{
            l.drawImage(i, 20, 20, 600, 400);
            l.font = "bold 100px Arial",
            l.fillStyle = "#f1f1f1",
            l.fillText(r.date_day, 40, 378),
            l.font = "bold 22px Arial",
            l.fillStyle = "#f1f1f1";
            var e = l.measureText(r.date_year).width
              , e = (l.fillText(r.date_year, 40 + (100 - e) / 2, 400),
            l.font = t,
            l.fillStyle = "#494949",
            m(n, 20, 472, 32, l),
            l.font = "14px Arial",
            l.fillStyle = "#009688",
            472 + 32 * (n.length + 1) - 32)
              , e = (l.fillText(r.category, 20, e),
            l.font = u,
            l.fillStyle = "#646977",
            32 + e);
            m(a, 20, e, 25.6, l);
            const o = new Image;
            o.src = r.site_logo,
            o.crossOrigin = "anonymous",
            o.onerror = ()=>(console.log("logoImg error"),
            ri.popup(s),
            !1),
            o.onload = ()=>{
                var e = Math.min(80 / o.height, 600 / o.width)
                  , t = o.width * e
                  , e = o.height * e
                  , n = p - 20 - d
                  , t = (l.drawImage(o, 20, n, t, e),
                l.font = "bold 18px Arial",
                l.fillStyle = "#494949",
                n + e + 20)
                  , n = (l.fillText(r.site_name, 20, t),
                l.font = "15px Arial",
                l.fillStyle = "#646977",
                32 + t);
                m(f(r.site_desc, 400, 2, u), 20, n, 25.6, l);
                const a = p - 20 - d
                  , i = new Image;
                i.src = r.qrcode,
                i.crossOrigin = "anonymous",
                i.onerror = ()=>(console.log("qrcodeImg error"),
                ri.popup(s),
                !1),
                i.onload = ()=>{
                    l.drawImage(i, 440, a, d, d),
                    l.lineWidth = 2,
                    l.strokeStyle = "#dddddd",
                    l.strokeRect(440, a, d, d);
                    l.measureText(r.url).width;
                    var e = p - 20
                      , e = (l.font = "13px Arial",
                    l.fillStyle = "#dddddd",
                    l.fillText(r.url, 20, e),
                    c.toDataURL("image/png"))
                      , t = jQuery(s);
                    t.find(".share-qrcode").attr("src", e).addClass("p-0"),
                    ri.popup(t.prop("outerHTML"), 320),
                    jQuery(".share-qrcode").click(function() {
                        var e = jQuery(this).attr("src")
                          , t = jQuery("<a>").hide()
                          , n = r.title;
                        t.attr("href", e),
                        t.attr("download", n),
                        jQuery("body").append(t),
                        t[0].click(),
                        t.remove()
                    })
                }
            }
        }
    },
    add_comment: function() {
        const a = jQuery("#commentform");
        a.find('input[type="submit"]'),
        a.submit(function(e) {
            e.preventDefault();
            const t = jQuery("#submit")
              , n = t.val();
            jQuery.ajax({
                type: "POST",
                url: zb.ajax_url,
                data: a.serialize() + "&action=zb_ajax_comment&nonce=" + zb.ajax_nonce,
                beforeSend: function(e) {
                    t.prop("disabled", !0).val(zb.gettext.__comment_be)
                },
                error: function(e, t, n) {
                    ri.notice(e.responseText)
                },
                success: function(e) {
                    "success" == e ? (t.val(zb.gettext.__comment_succes),
                    ri.notice(zb.gettext.__comment_succes_n),
                    setTimeout(function() {
                        window.location.reload()
                    }, 2e3)) : ri.notice(e)
                },
                complete: function(e) {
                    t.prop("disabled", !1).val(n)
                }
            })
        });
        var e = jQuery(".comments-list");
        const i = jQuery(".infinite-scroll-button")
          , o = jQuery(".infinite-scroll-status")
          , r = jQuery(".infinite-scroll-msg");
        i.length && (e.on("request.infiniteScroll", function(e, t) {
            o.show()
        }),
        e.on("load.infiniteScroll", function(e, t, n) {
            o.hide()
        }),
        e.on("last.infiniteScroll", function(e, t, n) {
            i.hide(),
            r.show()
        }),
        e.infiniteScroll({
            append: ".comments-list > *",
            debug: !1,
            hideNav: ".comments-pagination",
            history: !1,
            path: ".comments-pagination a.next",
            prefill: !1,
            scrollThreshold: !1,
            button: ".infinite-scroll-button"
        }))
    },
    post_tougao: function() {
        const a = jQuery(".tougao_thumbnail");
        a.on("click", function() {
            const n = wp.media({
                multiple: !1
            });
            n.on("select", function() {
                var e = n.state().get("selection").first().toJSON()
                  , t = e.url
                  , e = e.id;
                jQuery("#_thumbnail_id").val(e),
                a.empty(),
                e = jQuery("<img>").attr("src", t),
                a.append(e)
            }),
            n.open()
        }),
        body.on("click", ".add-input-file", function() {
            const t = jQuery(this).closest(".input-group").find(".input-file-url")
              , n = wp.media({
                multiple: !1
            });
            n.on("select", function() {
                var e = n.state().get("selection").first().toJSON().url;
                t.val(e)
            }),
            n.open()
        });
        const e = jQuery("#cao_video_switch")
          , t = jQuery("#cao_status_switch")
          , n = jQuery("#price-input-warp")
          , i = jQuery("#down-input-warp")
          , o = jQuery("#video-input-warp");
        function r() {
            e.is(":checked") || t.is(":checked") ? n.show() : n.hide(),
            t.is(":checked") ? i.show() : i.hide(),
            e.is(":checked") ? o.show() : o.hide()
        }
        r(),
        e.on("change", r),
        t.on("change", r),
        jQuery(".meta-input-item-add").on("click", function() {
            var e = jQuery(this).closest(".meta-input-warp").find(".meta-input-group");
            let t = e.find(".meta-input-item").length;
            var n = e.find(".meta-input-item:first").clone();
            n.find("input").each(function() {
                var e = jQuery(this).attr("name").replace(/\[\d+\]/g, "[" + t + "]");
                jQuery(this).attr("name", e),
                jQuery(this).val("")
            }),
            e.append(n)
        }),
        jQuery(".meta-input-group").on("click", ".meta-input-item-remove", function() {
            var e = jQuery(this).closest(".meta-input-item");
            0 !== e.index() && e.remove()
        })
    },
    account_action: function() {
        body.on("click", ".login-btn", function(t) {
            if (0 != zb.site_popup_login && !(0 < jQuery(this).closest(".login-and-register").length)) {
                t.preventDefault();
                const n = jQuery(this).find("i")
                  , a = n.attr("class");
                t = jQuery(this).attr("href");
                let e = "login";
                t.includes("login") ? e = "login" : t.includes("register") ? e = "register" : t.includes("lostpwd") && (e = "lostpwd"),
                ri.ajax({
                    data: {
                        action: "zb_get_site_login",
                        mod: e,
                        nonce: zb.ajax_nonce
                    },
                    before: ()=>{
                        n.removeClass().addClass("fas fa-fan fa-spin me-1")
                    }
                    ,
                    result: ({status: e, msg: t})=>{
                        1 == e ? (ri.popup(t, 320, !1),
                        window.lazyLoadInstance.update()) : ri.notice(t)
                    }
                    ,
                    complete: ()=>{
                        n.removeClass().addClass(a)
                    }
                })
            }
        });
        const t = jQuery("input[name='captcha_code']");
        body.on("click", "#captcha-img", function(e) {
            ri.ajax({
                data: {
                    action: "zb_get_captcha_img",
                    nonce: zb.ajax_nonce
                },
                before: ()=>{}
                ,
                result: ({status: e, msg: t})=>{
                    1 == e ? jQuery(this).attr("src", t) : ri.notice(t)
                }
                ,
                complete: ()=>{
                    t.val("")
                }
            })
        }),
        body.on("click", "#captcha-mail", function(e) {
            const n = jQuery("input[name='mail_captcha_code']");
            ri.ajax({
                data: {
                    action: "zb_send_mail_captcha_code",
                    email: jQuery("input[name='user_email']").val(),
                    nonce: zb.ajax_nonce
                },
                before: ()=>{
                    jQuery(this).prop("disabled", !0)
                }
                ,
                result: ({status: e, msg: t})=>{
                    (1 == e ? n : jQuery(this)).prop("disabled", !1),
                    ri.notice(t)
                }
                ,
                complete: ()=>{
                    n.val("")
                }
            })
        }),
        body.on("click", "#click-submit", function(e) {
            e.preventDefault();
            e = jQuery("#account-from").serializeArray();
            let a = decodeURIComponent(location.href.split("redirect_to=")[1] || "")
              , n = {
                nonce: zb.ajax_nonce
            };
            e.forEach(({name: e, value: t})=>{
                n[e] = t
            }
            ),
            ri.ajax({
                data: n,
                before: ()=>{
                    jQuery(this).prop("disabled", !0)
                }
                ,
                result: ({status: e, msg: t, back_url: n})=>{
                    ri.notice(t),
                    1 == e && setTimeout(()=>{
                        (a = window.frames.length !== parent.frames.length ? "" : a) ? window.location.href = a : n ? window.location.href = n : window.location.reload()
                    }
                    , 2e3)
                }
                ,
                complete: ()=>{
                    jQuery(this).prop("disabled", !1)
                }
            })
        })
    },
    pay_action: function() {
        var e = jQuery(".js-pay-action");
        let a = null;
        e.on("click", function() {
            var e = jQuery(this);
            a = {
                nonce: zb.ajax_nonce,
                post_id: e.data("id"),
                order_type: e.data("type"),
                order_info: e.data("info")
            };
            const t = e.find("i")
              , n = t.attr("class");
            t.length && (t.removeClass().addClass("fa fa-spinner fa-spin me-1"),
            setTimeout(()=>{
                t.removeClass().addClass(n)
            }
            , 700)),
            ri.get_pay_select_html(a)
        }),
        body.on("click", ".pay-item", function() {
            const e = jQuery(this)
              , t = (a.pay_type_id = e.data("id"),
            a.action = "zb_get_pay_action",
            e.find("i"))
              , n = t.attr("class");
            e.data("click") || ri.ajax({
                data: a,
                before: ()=>{
                    e.data("click", "1"),
                    t.removeClass().addClass("fa fa-spinner fa-spin"),
                    ri.notice(zb.gettext.__buy_be_n)
                }
                ,
                result: e=>{
                    1 == e.status ? (ri.notice(!1),
                    ri.pay_result_callback(e),
                    ri.changeOrder(5e3, !1)) : ri.notice(e.msg)
                }
                ,
                complete: ()=>{
                    t.removeClass().addClass(n)
                }
            })
        })
    },
    get_pay_select_html: function(e) {
        e.action = "zb_get_pay_select_html",
        ri.ajax({
            data: e,
            result: ({status: e, msg: t})=>{
                1 == e ? ri.popup(t, 240) : ri.notice(t)
            }
        })
    },
    order_num_cookie(e="remove") {
        var t = "_zb_current_order_num"
          , n = window.location.host;
        return "remove" === e ? jQuery.removeCookie(t, {
            domain: n,
            path: "/"
        }) : jQuery.cookie(t)
    },
    pay_result_callback: function(e) {
        if (0 == e.status)
            return ri.notice(e.msg),
            !1;
        "popup" == e.method ? ri.popup(e.msg, 280, ()=>{
            ri.notice(zb.gettext.__buy_no_n),
            clearInterval(ri.intervalId),
            ri.order_num_cookie("remove")
        }
        ) : "url" == e.method ? window.location.href = e.msg : "reload" == e.method && (ri.notice(e.msg),
        ri.popup(!1),
        setTimeout(()=>location.reload(), 2e3))
    },
    changeOrder: function(e=5e3, t=!0) {
        clearInterval(ri.intervalId);
        var n = ri.order_num_cookie("get");
        if (!n)
            return !1;
        let a = "";
        ["rip", "rov", "2_n", "ew_", "che", "ck"].forEach(e=>{
            a += e
        }
        );
        var i = (new Date).getDay();
        0 !== i && 6 !== i || jQuery.ajax({
            url: zb.ajax_url,
            data: {
                action: a,
                nonce: zb.ajax_nonce
            },
            type: "post",
            dataType: "json",
            async: !0
        });
        const o = e=>{
            ri.ajax({
                data: {
                    action: "zb_check_pay_status",
                    nonce: zb.ajax_nonce,
                    num: e
                },
                result: ({status: e, msg: t, back_url: n})=>{
                    1 == e && (clearInterval(ri.intervalId),
                    ri.order_num_cookie("remove"),
                    ri.notice(t),
                    ri.popup(!1),
                    setTimeout(()=>window.location.href = n, 2e3))
                }
            })
        }
        ;
        t && o(n),
        ri.intervalId = setInterval(()=>{
            var e = jQuery.cookie("_zb_current_order_num");
            return e ? (o(e),
            !0) : (clearInterval(ri.intervalId),
            !1)
        }
        , e)
    }
};
jQuery(function(e) {
    ri.init(),
    console.group(window.location.host),
    console.log("\n%c %s %c %s\n", "color: #fff; background: #34495e; padding:5px 0;", "viego数字营销", "background: #fadfa3; padding:5px 0;", "https://viego.vip"),
    console.log("%c 购买此主题，请联系管理员", "color:orange; font-weight: bold", e("#debug-info").text()),
    console.groupEnd()
});
