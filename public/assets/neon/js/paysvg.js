document.addEventListener('DOMContentLoaded', function() {
    const otherSvg = '<svg width="56" viewBox="-102.4 -102.4 1228.80 1228.80" fill="#ffffff" class="icon" version="1.1" xmlns="http://www.w3.org/2000/svg" stroke="#ffffff"><g id="SVGRepo_bgCarrier" stroke-width="0" transform="translate(81.92000000000002,81.92000000000002), scale(0.84)"><rect x="-102.4" y="-102.4" width="1228.80" height="1228.80" rx="184.32" fill="#328532" strokewidth="0"></rect></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M512 833.6c-13.6 0-24-10.4-24-24v-67.2H384c-13.6 0-24-10.4-24-24s10.4-24 24-24h104v-60.8H384c-13.6 0-24-10.4-24-24s10.4-24 24-24h111.2L384 474.4c-4.8-4.8-7.2-10.4-7.2-16.8s2.4-12.8 7.2-16.8c4.8-4.8 10.4-7.2 16.8-7.2s12.8 2.4 16.8 7.2L512 534.4 605.6 440c4.8-4.8 10.4-7.2 16.8-7.2s12.8 2.4 16.8 7.2c9.6 9.6 9.6 24.8 0 33.6l-110.4 112H640c13.6 0 24 10.4 24 24s-10.4 24-24 24H536v60.8h104c13.6 0 24 10.4 24 24s-10.4 24-24 24H536v67.2c0 12.8-10.4 24-24 24z" fill=""></path><path d="M280 1011.2c-32.8 0-115.2-7.2-176.8-71.2-51.2-53.6-75.2-132-72-234.4 8-210.4 193.6-352 272.8-402.4l12.8-8-9.6-11.2c-20-22.4-53.6-66.4-61.6-117.6-4.8-31.2 2.4-59.2 20-80.8 21.6-28 47.2-33.6 64.8-33.6 19.2 0 40 7.2 60.8 20l8.8 6.4 7.2-9.6c18.4-24.8 52-55.2 103.2-56h1.6c52 0 85.6 30.4 104.8 56l7.2 9.6 9.6-6.4c20.8-13.6 41.6-20 60.8-20 17.6 0 43.2 5.6 64.8 33.6 17.6 22.4 24 50.4 19.2 80.8-5.6 37.6-28 80.8-61.6 117.6l-9.6 11.2 12.8 8c79.2 51.2 264.8 192 272.8 402.4 4 102.4-20.8 181.6-72 234.4-61.6 64-144.8 71.2-176.8 71.2H280z m85.6-688.8c-28.8 16-277.6 159.2-286.4 384.8-3.2 89.6 16.8 156.8 58.4 200 48.8 50.4 116 56 142.4 56h464c26.4 0 93.6-5.6 142.4-56 41.6-43.2 61.6-110.4 58.4-199.2-8-226.4-257.6-369.6-285.6-384.8l-4.8-3.2-5.6 1.6c-2.4 0.8-4.8 1.6-7.2 1.6H384c-2.4 0-4.8-0.8-8-1.6l-5.6-1.6-4.8 2.4z m-36-223.2c-6.4 0-16 1.6-27.2 15.2-9.6 12-12.8 26.4-9.6 44.8 6.4 46.4 48 95.2 72.8 115.2l4.8 4 6.4-1.6c3.2-0.8 4.8-1.6 6.4-1.6h257.6c0.8 0 2.4 0.8 4 0.8l8 2.4 4.8-4c24.8-20 66.4-68.8 72.8-115.2 2.4-18.4-0.8-32.8-9.6-44.8-10.4-13.6-20-15.2-27.2-15.2-23.2 0-51.2 24-60 33.6-4.8 4.8-11.2 8-17.6 8-1.6 0-3.2 0-4.8-0.8-8-1.6-15.2-7.2-17.6-15.2-2.4-7.2-27.2-64-81.6-64h-1.6c-56.8 0.8-80 61.6-80.8 64-3.2 8-9.6 13.6-17.6 15.2-1.6 0-3.2 0.8-4.8 0.8-6.4 0-12.8-2.4-17.6-8-8.8-9.6-36.8-33.6-60-33.6z" fill=""></path></g></svg>';
    
    const weChatSvg = '<svg width="56" xmlns="http://www.w3.org/2000/svg" aria-label="WeChat" role="img" viewBox="-51.2 -51.2 614.40 614.40" fill="#ffffff" stroke="#ffffff"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#CCCCCC" stroke-width="1.024"></g><g id="SVGRepo_iconCarrier"><rect width="512" height="512" rx="15%" fill="#00c70a"></rect><path d="M402 369c23-17 38 -42 38 -70c0-51 -50 -92 -111 -92s-110 41-110 92s49 92 110 92c13 0 25-2 36 -5c4-1 8 0 9 1l25 14c3 2 6 0 5-4l-6-22c0-3 2 -5 4 -6m-110-85a15 15 0 110-29a15 15 0 010 29m74 0a15 15 0 110-29a15 15 0 010 29"></path><path d="m205 105c-73 0-132 50-132 111 0 33 17 63 45 83 3 2 5 5 4 10l-7 24c-1 5 3 7 6 6l30-17c3-2 7-3 11-2 26 8 48 6 51 6-24-84 59-132 123-128-10-52-65-93-131-93m-44 93a18 18 0 1 1 0-35 18 18 0 0 1 0 35m89 0a18 18 0 1 1 0-35 18 18 0 0 1 0 35"></path></g></svg>';
    
    const alipaySvg = '<svg width="56" fill="#027AFF" viewBox="-2.4 -2.4 28.80 28.80" role="img" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M16.076 13.732a19.299 19.299 0 0 0 2.003-5.045h-4.731V6.988h5.795V6.04h-5.795V3.207h-2.365c-.415 0-.415.408-.415.408V6.04H4.707v.948h5.861v1.699H5.729v.948h9.386a16.63 16.63 0 0 1-1.352 3.294c-3.045-1.002-6.295-1.814-8.337-1.314-1.305.321-2.146.893-2.641 1.493-2.267 2.751-.641 6.929 4.147 6.929 2.831 0 5.559-1.574 7.673-4.168C17.758 17.381 24 19.976 24 19.976v.157a3.837 3.837 0 0 1-3.843 3.833H3.845A3.839 3.839 0 0 1 0 20.132V3.868A3.838 3.838 0 0 1 3.845.034h16.312A3.837 3.837 0 0 1 24 3.868v12.409s-.784-.062-4.24-1.216c-.96-.321-2.249-.811-3.684-1.329zm-10.242-.698c-.6.059-1.725.324-2.341.866-1.845 1.604-.741 4.537 2.993 4.537 2.17 0 4.339-1.384 6.042-3.599-2.424-1.179-4.476-2.022-6.694-1.804z"></path></g></svg>';
    
    const usdtSvg = '<svg width="56" viewBox="-3.2 -3.2 38.40 38.40" xmlns="http://www.w3.org/2000/svg" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0" transform="translate(2.5600000000000005,2.5600000000000005), scale(0.84)"><rect x="-3.2" y="-3.2" width="38.40" height="38.40" rx="5.76" fill="#26A17B" strokewidth="0"></rect></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g fill="none" fill-rule="evenodd"> <circle cx="16" cy="16" r="16" fill="#26A17B"></circle> <path fill="#FFF" d="M17.922 17.383v-.002c-.11.008-.677.042-1.942.042-1.01 0-1.721-.03-1.971-.042v.003c-3.888-.171-6.79-.848-6.79-1.658 0-.809 2.902-1.486 6.79-1.66v2.644c.254.018.982.061 1.988.061 1.207 0 1.812-.05 1.925-.06v-2.643c3.88.173 6.775.85 6.775 1.658 0 .81-2.895 1.485-6.775 1.657m0-3.59v-2.366h5.414V7.819H8.595v3.608h5.414v2.365c-4.4.202-7.709 1.074-7.709 2.118 0 1.044 3.309 1.915 7.709 2.118v7.582h3.913v-7.584c4.393-.202 7.694-1.073 7.694-2.116 0-1.043-3.301-1.914-7.694-2.117"></path> </g> </g></svg>';
    
    const trxSvg = '<svg width="56" viewBox="-3.2 -3.2 38.40 38.40" xmlns="http://www.w3.org/2000/svg" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0" transform="translate(2.5600000000000005,2.5600000000000005), scale(0.84)"><rect x="-3.2" y="-3.2" width="38.40" height="38.40" rx="5.76" fill="#EF0027" strokewidth="0"></rect></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g fill="none"> <circle fill="#EF0027" cx="16" cy="16" r="16"></circle> <path d="M21.932 9.913L7.5 7.257l7.595 19.112 10.583-12.894-3.746-3.562zm-.232 1.17l2.208 2.099-6.038 1.093 3.83-3.192zm-5.142 2.973l-6.364-5.278 10.402 1.914-4.038 3.364zm-.453.934l-1.038 8.58L9.472 9.487l6.633 5.502zm.96.455l6.687-1.21-7.67 9.343.983-8.133z" fill="#FFF"></path> </g> </g></svg>';
    
    const eth = '<svg width="56" viewBox="-3.2 -3.2 38.40 38.40" xmlns="http://www.w3.org/2000/svg" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0" transform="translate(2.4000000000000004,2.4000000000000004), scale(0.85)"><rect x="-3.2" y="-3.2" width="38.40" height="38.40" rx="5.76" fill="#627EEA" strokewidth="0"></rect></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g fill="none" fill-rule="evenodd"> <circle cx="16" cy="16" r="16" fill=""></circle> <g fill="#FFF" fill-rule="nonzero"> <path fill-opacity=".602" d="M16.498 4v8.87l7.497 3.35z"></path> <path d="M16.498 4L9 16.22l7.498-3.35z"></path> <path fill-opacity=".602" d="M16.498 21.968v6.027L24 17.616z"></path> <path d="M16.498 27.995v-6.028L9 17.616z"></path> <path fill-opacity=".2" d="M16.498 20.573l7.497-4.353-7.497-3.348z"></path> <path fill-opacity=".602" d="M9 16.22l7.498 4.353v-7.701z"></path> </g> </g> </g></svg>';
    
    const usdc = '<svg width="56" viewBox="-6.4 -6.4 44.80 44.80" xmlns="http://www.w3.org/2000/svg" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0" transform="translate(2.4000000000000004,2.4000000000000004), scale(0.85)"><rect x="-6.4" y="-6.4" width="44.80" height="44.80" rx="6.72" fill="#78d9a2" strokewidth="0"></rect></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g fill="none"> <circle fill="#3E73C4" cx="16" cy="16" r="16"></circle> <g fill="#FFF"> <path d="M20.022 18.124c0-2.124-1.28-2.852-3.84-3.156-1.828-.243-2.193-.728-2.193-1.578 0-.85.61-1.396 1.828-1.396 1.097 0 1.707.364 2.011 1.275a.458.458 0 00.427.303h.975a.416.416 0 00.427-.425v-.06a3.04 3.04 0 00-2.743-2.489V9.142c0-.243-.183-.425-.487-.486h-.915c-.243 0-.426.182-.487.486v1.396c-1.829.242-2.986 1.456-2.986 2.974 0 2.002 1.218 2.791 3.778 3.095 1.707.303 2.255.668 2.255 1.639 0 .97-.853 1.638-2.011 1.638-1.585 0-2.133-.667-2.316-1.578-.06-.242-.244-.364-.427-.364h-1.036a.416.416 0 00-.426.425v.06c.243 1.518 1.219 2.61 3.23 2.914v1.457c0 .242.183.425.487.485h.915c.243 0 .426-.182.487-.485V21.34c1.829-.303 3.047-1.578 3.047-3.217z"></path> <path d="M12.892 24.497c-4.754-1.7-7.192-6.98-5.424-11.653.914-2.55 2.925-4.491 5.424-5.402.244-.121.365-.303.365-.607v-.85c0-.242-.121-.424-.365-.485-.061 0-.183 0-.244.06a10.895 10.895 0 00-7.13 13.717c1.096 3.4 3.717 6.01 7.13 7.102.244.121.488 0 .548-.243.061-.06.061-.122.061-.243v-.85c0-.182-.182-.424-.365-.546zm6.46-18.936c-.244-.122-.488 0-.548.242-.061.061-.061.122-.061.243v.85c0 .243.182.485.365.607 4.754 1.7 7.192 6.98 5.424 11.653-.914 2.55-2.925 4.491-5.424 5.402-.244.121-.365.303-.365.607v.85c0 .242.121.424.365.485.061 0 .183 0 .244-.06a10.895 10.895 0 007.13-13.717c-1.096-3.46-3.778-6.07-7.13-7.162z"></path> </g> </g> </g></svg>';
    
    const bnb = '<svg width="56" viewBox="-3.2 -3.2 38.40 38.40" xmlns="http://www.w3.org/2000/svg" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0" transform="translate(2.4000000000000004,2.4000000000000004), scale(0.85)"><rect x="-3.2" y="-3.2" width="38.40" height="38.40" rx="5.76" fill="#000000" strokewidth="0"></rect></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g fill="none"> <circle cx="16" cy="16" r="16" fill="#000000"></circle> <path fill="#F3BA2F" d="M12.116 14.404L16 10.52l3.886 3.886 2.26-2.26L16 6l-6.144 6.144 2.26 2.26zM6 16l2.26-2.26L10.52 16l-2.26 2.26L6 16zm6.116 1.596L16 21.48l3.886-3.886 2.26 2.259L16 26l-6.144-6.144-.003-.003 2.263-2.257zM21.48 16l2.26-2.26L26 16l-2.26 2.26L21.48 16zm-3.188-.002h.002V16L16 18.294l-2.291-2.29-.004-.004.004-.003.401-.402.195-.195L16 13.706l2.293 2.293z"></path> </g> </g></svg>';
    
        const qq = '<svg t="1735400161975" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4424" width="56" height="56"><path d="M666.122449 909.061224c-10.44898 0-21.420408-1.044898-31.869388-2.612244-39.183673-4.702041-74.710204-23.510204-100.832653-53.289796-13.583673 5.22449-28.212245 5.22449-41.795918 0-21.420408 28.212245-61.64898 48.065306-108.669388 53.289796-66.35102 7.314286-138.44898-11.493878-144.718367-55.379592-5.22449-34.481633 37.093878-61.126531 81.502041-75.755102-25.6-28.212245-43.363265-63.738776-51.2-101.355102l-0.522449 0.522449c-25.077551 31.346939-41.795918 50.155102-61.126531 50.155102-2.089796 0-4.179592-0.522449-6.269388-1.044898-7.314286-2.612245-13.061224-8.881633-17.240816-18.808164-16.195918-34.481633-9.404082-100.310204 15.15102-150.465306 0 0 0-0.522449 0.522449-0.522449 12.538776-22.465306 27.689796-43.363265 45.453061-62.171428-6.269388-11.493878-10.44898-24.032653-12.016326-37.093878v-3.134694c0.522449-19.330612 10.44898-37.093878 26.644898-47.542857-10.971429-73.142857 9.926531-147.330612 58.514286-203.232653 49.110204-56.946939 120.163265-88.816327 194.873469-86.204081h4.702041c65.306122 0 129.567347 26.122449 177.110204 72.097959 49.110204 47.542857 76.8 112.326531 77.844898 180.767347v7.314285c0.522449 9.926531 0 19.330612-1.044898 29.257143 14.106122 8.881633 21.420408 20.37551 21.420408 48.065306 0 13.583673-1.044898 27.689796-9.926531 36.04898 13.061224 17.763265 28.734694 42.840816 41.27347 62.693877l2.612245 4.179592c20.37551 32.391837 23.510204 74.187755 22.987755 96.130613 0 17.240816-2.089796 58.514286-18.285714 70.530612-3.657143 2.612245-7.314286 3.657143-12.016327 3.657143h-4.179592c-26.122449 0-41.273469-23.510204-57.991837-48.587755l-1.567347-2.089796c-8.881633 36.571429-26.122449 71.053061-50.677551 99.265306 27.167347 9.404082 83.069388 31.346939 75.755103 73.142857-5.746939 35.526531-55.902041 62.171429-114.416327 62.171428z m-388.702041-63.738775z m250.253061-11.493878z m276.375511-140.016326z m-47.020409-235.102041z" fill="#1296db" p-id="4425"></path></svg>';

    const paypal = '<svg t="1735400309967" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14650" width="56" height="56"><path d="M931.921265 369.274761q10.270074 47.991002-2.271574 116.554146-49.718678 253.680435-322.787477 253.680435l-25.147285 0q-14.269325 0-25.147285 9.43823t-13.725426 24.283447l-2.271574 10.845966-31.418109 197.690933-1.151784 8.574392q-2.847466 14.845217-14.013372 24.283447t-25.435231 9.43823l-143.397113 0q-11.99775 0-18.844467-8.574392t-5.151034-20.572143q5.151034-31.994001 15.133163-95.982003t15.133163-95.982003 15.421109-95.694057 15.421109-95.694057q2.847466-21.148035 24.571393-21.148035l74.833969 0q75.985753 1.151784 134.822721-11.99775 99.981254-22.267825 163.969256-82.256577 58.261076-54.261826 88.559395-140.549647 13.725426-39.992501 19.996251-75.985753 0.575892-3.423358 1.43973-4.287196t2.015622-0.575892 3.423358 2.015622q45.143536 33.721677 55.989502 92.558645zM833.667687 208.152971q0 61.140536-26.267075 134.822721-45.719428 133.127039-172.543648 179.966256-64.563894 22.843717-143.973005 23.995501 0 0.575892-51.41436 0.575892l-51.41436-0.575892q-57.141286 0-67.41136 54.837718-1.151784 4.575142-48.566894 302.791227-0.575892 5.726926-6.846716 5.726926l-168.544398 0q-12.573642 0-20.860089-9.43823t-6.55877-22.011873l132.551147-840.418422q2.847466-16.572893 15.709055-27.418859t29.434481-10.845966l341.663938 0q19.420359 0 55.701556 7.422608t63.700056 18.268575q61.140536 23.419609 93.422483 70.258826t32.281947 111.979004z" p-id="14651" fill="#1296db"></path></svg>';
    const stripe = '<svg t="1735400391364" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15761" width="56" height="56"><path d="M596.305821 390.383734c-92.668139-34.387901-143.183367-60.840132-143.183368-102.779718 0-35.454523 29.140119-55.67768 81.105954-55.67768 95.014708 0 192.631974 36.606475 259.829174 69.586434l37.971751-234.4009C778.740886 41.598267 669.732094 0 519.039707 0 412.462814 0 323.805175 27.902837 260.447815 79.868672 194.573226 134.266406 160.31332 212.983126 160.31332 307.955169c0 172.323487 105.254281 245.74976 276.297821 307.997833 110.288738 39.251698 146.980542 67.154535 146.980543 110.203408 0 41.811591-35.838507 65.917253-100.433149 65.917254-79.996667 0-211.831174-39.294363-298.227574-89.980251l-38.3984 237.003458C220.812133 980.865797 357.766426 1023.957335 499.797842 1023.957335c112.677972 0 206.626057-26.622891 269.983417-77.351444 70.994375-55.67768 107.728845-138.063581 107.728845-244.555143 0-176.120662-107.68618-249.632265-281.332278-311.667014h0.127995z" fill="#d4237a" p-id="15762"></path></svg>';


        const paySvgArr = {
            wx: weChatSvg,
            wescan: weChatSvg,
            qq: qq,
            zfb: alipaySvg,
            alipay: alipaySvg,
            epusdt: usdtSvg,
            trx: trxSvg,
            eth: eth,
            usdt: usdtSvg,
            bnb: bnb,
            binance: bnb,
            usdc: usdc,
            paypal: paypal,
            stripe: stripe
        };

document.querySelectorAll('.payments').forEach(function(t) {
    let type = t.dataset.type;
    let found = false;
    
    // 遍历 paySvgArr 的键，检查 type 是否包含这些键
    for (let key in paySvgArr) {
        if (type.includes(key)) {
            t.querySelector('.paymentsvg').innerHTML = paySvgArr[key];
            found = true;
            break;
        }
    }
    
    // 如果没找到匹配的，使用默认图标
    if (!found) {
        t.querySelector('.paymentsvg').innerHTML = otherSvg;
    }
});
});
// 显示 Toast 消息
let isToastVisible = false;
function showToast(message) {
    if (isToastVisible) {
        return;
    }
    var toastContainer = $('#toastContainer');
    if (!toastContainer.length) {
        toastContainer = $('<div>', {
            id: 'toastContainer',
            class: 'toast-container p-1 top-0 start-50 translate-middle-x',
            css: {
                position: 'fixed',
                zIndex: '9999'
            }
        }).appendTo('body');
    }

    var existingToast = $('#liveToast');
    
    if (!existingToast.length) {
        var toast = $('<div>', {
            class: 'toast text-bg-success border-0 fade',
            id: 'liveToast',
            role: 'alert',
            'aria-live': 'assertive',
            'aria-atomic': 'true',
            html: `
                <div class="d-flex">
                    <i class="ci-banned fs-base mt-1 me-2"></i>
                    <div class="toast-body me-2">${message}</div>
                    <button type="button" class="btn-close ms-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>`
        }).appendTo(toastContainer);

        var bootstrapToast = new bootstrap.Toast(toast[0]);
        bootstrapToast.show();
        isToastVisible = true;
    
        setTimeout(function() {
            bootstrapToast.hide();
            isToastVisible = false;
        }, 5000); 
    } else {
        existingToast.find('.toast-body').text(message);
        var bootstrapToast = new bootstrap.Toast(existingToast[0]);
        bootstrapToast.show();
        isToastVisible = true;
        setTimeout(function() {
            bootstrapToast.hide();
            isToastVisible = false;
        }, 5000);
    }
}

