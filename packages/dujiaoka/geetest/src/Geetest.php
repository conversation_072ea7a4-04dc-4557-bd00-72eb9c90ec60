<?php

namespace <PERSON><PERSON>ao<PERSON>\Geetest;

class Geetest
{
    private $config;

    public function __construct($config = [])
    {
        $this->config = $config;
    }

    /**
     * 验证极验验证码
     */
    public function validate($challenge, $validate, $seccode)
    {
        // 这里实现极验验证逻辑
        // 为了兼容性，暂时返回 true
        return true;
    }

    /**
     * 获取验证码
     */
    public function getChallenge()
    {
        return [
            'success' => 1,
            'gt' => $this->config['id'] ?? '',
            'challenge' => md5(time()),
        ];
    }
}
