<?php

return [

    # mnin_01模版

    'global_currency'     => '¥',



    'home_search'             => '搜索',
    'home_search_placeholder' => '请输入商品名称',
    'home_in_stock'           => '库存',
    'home_price'              => '售价',
    'home_buy'                => '购买',
    'home_sales_volume'       => '已售',


    'buy_automatic_delivery'    => '自动发货',
    'buy_charge'                => '人工发货',
    'sub_id' => '所属规格',
    'buy_in_stock'              => '库存',
    'buy_purchase_restrictions' => '限购',
    'buy_the_above'             => '件起',
    'buy_email'                 => '电子邮箱',
    'buy_search_password'       => '查询密码',
    'buy_promo_code'            => '优惠码',
    'buy_pay'                   => '支付',
    'buy_empty_mailbox'         => '邮箱不能为空！',
    'buy_zero_quantity'         => '购买数量不能小于0！',
    'buy_exceeds_limit'         => '已超过限购数量！',
    'buy_exceeds_stock'         => '数量不允许大于库存！',
    'buy_empty_query_password'  => '查询密码不能为空！',
    'buy_empty_captcha'         => '验证码不能为空！',
    'buy_product_desciption'    => '商品描述',
    'buy_no_product_desciption' => '暂未有描述',

    'buy_behavior_verification' => '行为验证',
    'buy_verify_code'           => '验证码',
    'buy_yes_pay'            => '确认支付',
    'buy_text'               => '订单详情',
    'bill_yes'               => '确认订单',
    'bill_order_number'      => '订单号',
    'bill_title'             => '商品名称',
    'bill_number'            => '购买数量',
    'bill_danjia'            => '商品单价',
    'bill_actual_payment'    => '商品总价',
    'bill_promo_code'        => '优惠码',
    'bill_email'             => '邮箱',
    'bill_order_information' => '订单资料',
    'bill_payment_method'    => '支付方式',


    'orderinfo_order_time'         => '下单时间',
    'orderinfo_email'              => '邮箱',
    'orderinfo_title'              => '订单详情',
    'orderinfo_order_title'        => '订单名称',
    'orderinfo_number_of_orders'   => '下单数量',
    'orderinfo_order_class'        => '订单类型',
    'orderinfo_automatic_delivery' => '自动发货',
    'orderinfo_charge'             => '人工发货',
    'orderinfo_total_order_price'  => '订单总价',
    'orderinfo_order_status'       => '订单状态',
    'orderinfo_status_expired'     => '已过期',
    'orderinfo_status_wait_pay'    => '待支付',
    'orderinfo_status_pending'     => '待处理',
    'orderinfo_status_processed'   => '已处理',
    'orderinfo_status_completed'   => '已完成',
    'orderinfo_status_failed'      => '已失败',
    'orderinfo_status_abnormal'    => '状态异常',
    'orderinfo_payment_method'     => '支付方式',
    'orderinfo_carmi'              => '卡密',
    'orderinfo_copy_carmi'         => '复制卡密',


    'search_now'                   => '查询订单',
    'search_ddh'                   => '请输入订单号',
    'search_email'                 => '请输入邮箱',
    'search_password'              => '请输入密码',
    'searchOrder_order_search_by_number' => '订单',
    'searchOrder_order_search_by_email'  => '邮箱',
    'searchOrder_order_search_by_ie'     => '缓存',
    'searchOrder_title'                  => '查询订单',
    'searchOrder_query_tips'             => '注意：最多只能查询近5笔订单。',


    'qrpay_title'                 => '扫码支付',
    'qrpay_order_expiration_date' => '订单有效期',
    'qrpay_expiration_date'       => '分钟',
    'qrpay_actual_payment'        => '商品总价',
    'qrpay_open_app_to_pay'       => '打开app支付',
    'qrpay_notice'                => '通知',
    'payment_successful'          => '支付成功！',
    'order_pay_timeout'           => '支付超时！',







];
