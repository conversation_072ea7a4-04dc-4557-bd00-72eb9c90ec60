

/* daohang */

.daohang {
  /* display: grid; */
  grid-template-columns: 100% 0%;
}

/* LEFT daohang */

.left-daohang {
  padding: 30px 20px;
  color: #e5e5e5;
}

/* SLIDER */

.slider-container {
  margin: 0 auto;
  width: 100%;
}

.swiper {
  width: 90%;
  padding: 16px 0 20px;
  margin-bottom: 15px;
}

.swiper-slide {
  position: relative;
  width: 480px;
}

.swiper-slide img {
  border-radius: 20px;
  height: 260px;
  object-fit: cover;
  border: 1px solid rgba(159, 160, 168, 0.5);
}

.swiper-pagination {
  --swiper-pagination-bottom: -4px;
}

.swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  background-color: #ffffffe7;
  transition: all 0.3s ease-in-out;
}

.swiper-pagination-bullet-active {
  background-color: #fff;
  width: 18px;
  border-radius: 8px;
}

.slide-overlay {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-daohang: flex-end;
  row-gap: 12px;
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    240deg,
    transparent,
    transparent,
    rgba(38, 21, 149, 0.8)
  );
  border-radius: 20px;
  padding: 30px;
}

.slide-overlay h2 {
  font-size: clamp(1.2rem, 3vw, 2.2rem);
  font-weight: 700;
}

.slide-overlay button {
  display: flex;
  align-items: center;
  justify-daohang: center;
  column-gap: 10px;
  width: max-daohang;
  padding: 12px 16px;
  background: #e9e6eb2a;
  color: #e8e5e5;
  border: 1px solid rgba(159, 160, 168, 0.4);
  border-radius: 12px;
  outline: 0;
  font-size: clamp(0.7rem, 3vw, 1rem);
  font-weight: 500;
  text-transform: uppercase;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 10px 36px 0px,
    rgba(0, 0, 0, 0.06) 0px 0px 0px 1px;
  transition: all 0.4s linear;
  cursor: pointer;
}

.slide-overlay button:is(:hover, :focus-visible) {
  background: #e9e6eb;
  color: #000;
}

/* Containers of Artist and Albums */

.containers {
  width:90%;
  display: flex;
  margin:0 auto;
  align-items: center;
  padding: 0 0 12px;
  overflow-x: auto;
  cursor: grab;
}

/* ARTISTS */

.artists h1 {
  margin-bottom: 24px;
}

.artist-container {
  column-gap: 20px;
}

.artist {
    margin: 0 auto;
  display: grid;
  grid-auto-flow: dense;
  align-items: center;
  grid-template-rows: 8fr 2fr;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
}

.artist img {
  object-fit: cover;
  border-radius: 50%;
  border: 2px solid rgba(169, 150, 253, 0.5);
  width: 140px;
  aspect-ratio: 1/1;
  margin-bottom: 8px;
  box-shadow: rgba(221, 221, 221, 0.3) 0px 6px 18px -3px,
    rgba(221, 221, 221, 0.2) 0px -3px 0px inset;
  transition: all 0.2s;
}

.artist img:hover {
  border: 4px solid rgba(169, 150, 253, 0.6);
}

.artist p {
  font-size: clamp(0.9rem, 3vw, 1rem);
  font-weight: 500;
  text-align: center;
}

/* ALBUMS */

.albums h1 {
  margin: 60px 0 14px;
}

.album-container {
  column-gap: 24px;
}

.album {
  display: grid;
  margin: 0 auto;
  grid-auto-flow: dense;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
}

.album-frame {
  position: relative;
  margin: 0 auto;
  width: 180px;
  aspect-ratio: 1/1;
  border: 2px solid rgba(169, 150, 253, 0.5);
  border-radius: 10px;
  box-shadow: rgba(221, 221, 221, 0.3) 0px 8px 18px -3px,
    rgba(221, 221, 221, 0.2) 0px -3px 0px inset;
  margin-bottom: 15px;
  overflow: hidden;
}

.album-frame img {
  position: absolute;
  inset: 0;
  height: 100%;
  object-fit: cover;
  transition: transform 0.8s;
}

.album-frame img:hover {
  transform: rotate(3deg) scale(1.2);
}

.album h2 {
     text-align: center;
  font-size: clamp(0.9rem, 4vw, 1.1rem);
  font-weight: 500;
  line-height: 1.3;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;

  @supports (-webkit-line-clamp: 2) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: initial;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}

.album p {
     text-align: center;
  font-size: clamp(0.9rem, 4vw, 1rem);
  opacity: 0.5;
}

/* Containers Scrollbar Style */

.artist-container::-webkit-scrollbar,
.album-container::-webkit-scrollbar {
  height: 10px;
}

.artist-container::-webkit-scrollbar-track,
.album-container::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0.3rem rgb(79, 78, 78);
  border-radius: 40px;
}

.artist-container::-webkit-scrollbar-thumb,
.album-container::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 0.5rem rgb(116, 116, 116);
  background-color: rgba(25, 43, 206, 0.2);
  outline: none;
  border-radius: 40px;
}

/* RIGHT daohang */

.right-daohang {
  display: grid;
  grid-template-rows: 60% 40%;
  border-radius: 0 15px 15px 0;
  border-left: 1px solid rgba(255, 255, 255, 0.5);
  padding: 30px 20px;
  color: #e5e5e5;
}

/* SONGS */

.recommended-songs h1 {
  margin-bottom: 24px;
}

.song-container {
  align-items: center;
}

.song {
  display: grid;
  grid-template-columns: 26% auto 10%;
  align-items: center;
  margin-bottom: 16px;
}

.song-img {
  position: relative;
  width: 60px;
  border-radius: 6px;
}

.song-img img {
  aspect-ratio: 4/3;
  border-radius: inherit;
  object-fit: cover;
  border: 2px solid rgba(159, 160, 168, 0.5);
  box-shadow: rgba(221, 221, 221, 0.3) 0px 6px 18px -3px,
    rgba(221, 221, 221, 0.2) 0px -3px 0px inset;
}

.song-img .overlay {
  display: flex;
  align-items: center;
  justify-daohang: center;
  position: absolute;
  inset: 0;
  width: 100%;
  height: 92%;
  background-color: rgba(169, 150, 253, 0.6);
  border-radius: inherit;
  font-size: 1.75rem;
  opacity: 0;
  transition: all 0.4s linear;
  cursor: pointer;
}

.song-img:hover .overlay {
  opacity: 1;
}

.song h2 {
  font-size: 1rem;
}

.song p,
.song span {
  font-size: 0.8rem;
  font-weight: 300;
}

.song p {
  opacity: 0.8;
}

/* MUSIC PLAYER */

.music-player {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-daohang: center;
  color: #fff;
  background: rgba(188, 184, 198, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: inset 2px -2px 6px rgba(214, 214, 214, 0.2),
    inset -3px 3px 3px rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 30px 20px;
  margin-top: 20px;
}

.album-cover {
  position: relative;
}

.album-cover img {
  border-radius: 50%;
  border: 2px solid rgba(222, 215, 255, 0.9);
  max-width: 120px;
  aspect-ratio: 1/1;
  object-fit: cover;
  box-shadow: 0 10px 60px rgba(200, 187, 255, 01.75rem);
  transition: transform 0.5s ease-out;
  pointer-events: none;
  user-select: none;
}

.point {
  position: absolute;
  top: 50%;
  left: 50%;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 16px;
  background-color: rgba(17, 6, 58, 0.7);
  border: 2px solid rgba(222, 215, 255, 0.9);
  aspect-ratio: 1/1;
  border-radius: 50%;
  z-index: 2;
}

.music-player h2 {
  font-size: 1.2rem;
  font-weight: 500;
  margin: 16px 0 2px;
}

.music-player p {
  font-size: 1rem;
  font-weight: 300;
  margin-bottom: 26px;
  opacity: 0.8;
}

/* Music Player Controls */

#progress {
  appearance: none;
  -webkit-appearance: none;
  width: 100%;
  height: 6px;
  background: rgba(200, 187, 255, 0.6);
  border-radius: 4px;
  margin-bottom: 16px;
  cursor: pointer;
}

#progress::-webkit-slider-thumb {
  appearance: none;
  -webkit-appearance: none;
  background: rgb(77, 58, 162);
  width: 20px;
  aspect-ratio: 1/1;
  border-radius: 50%;
  border: 4px solid rgb(234, 229, 255);
  box-shadow: 0 6px 10px rgba(200, 187, 255, 0.4);
}

.controls {
  display: flex;
  justify-daohang: center;
  align-items: center;
}

.controls button {
  display: flex;
  align-items: center;
  justify-daohang: center;
  width: 36px;
  aspect-ratio: 1/1;
  margin: 20px;
  background: rgba(200, 187, 255, 0.6);
  border-radius: 50%;
  border: 0;
  outline: 0;
  color: #fff;
  font-size: 1.1rem;
  box-shadow: 0 4px 8px rgba(200, 187, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s linear;
}

.controls button:is(:hover, :focus-visible) {
  transform: scale(0.96);
}

.controls button:nth-child(2) {
  transform: scale(1.3);
}

.controls button:nth-child(2):is(:hover, :focus-visible) {
  transform: scale(1.25);
}

/* MEDIA QUERIES */

@media (max-width: 1500px) {
  main {
    grid-template-columns: 6% 94%;
  }

  .user-info img {
    border-radius: 50%;
    padding: 12px 12px 6px;
  }

  .nav-icon {
    text-align: center;
    transform: translateY(2px);
  }

  .nav-text {
    display: none;
  }

  .daohang {
    grid-template-columns: 70% 30%;
  }
}

@media (max-width: 1310px) {
  main {
    grid-template-columns: 8% 92%;
    margin: 30px;
  }
}

@media (max-width: 1250px) {
  .swiper-slide {
    width: 450px;
  }

  .swiper-slide img {
    border-radius: 16px;
    height: 230px;
  }

  .artist {
    grid-template-rows: 7fr 2fr;
  }

  .artist img {
    width: 120px;
  }

  .album-frame {
    width: 160px;
  }
  
  .song {
    grid-template-columns: 29% auto 10%;
  }

  .controls button {
    margin: 15px;
  }
}

@media (max-width: 1100px) {
  .daohang {
    grid-template-columns: 60% 40%;
  }

  .left-daohang {
    padding: 40px 20px 20px;
  }

  .swiper-slide {
    width: 410px;
  }

  .swiper-slide img {
    height: 220px;
  }

  .artist {
    grid-template-rows: 5fr 2fr;
  }

  .artist img {
       margin: 0 auto;
    width: 90px;
  }

  .album {
    grid-template-rows: 4fr 2fr;
  }

  .album-frame {
    width: 130px;
  }
  
   .song {
    grid-template-columns: 26% auto 10%;
  }

  .song:nth-child(8),
  .song:nth-child(9) {
    display: none;
  }
}

@media (max-width: 910px) {
  main {
    grid-template-columns: 10% 90%;
    margin: 20px;
  }

  .left-daohang {
    padding: 30px 20px 20px;
  }

  .swiper-slide {
    width: 350px;
  }

  .swiper-slide img {
    height: 180px;
  }

  .artist {
    grid-template-rows: 4fr 2fr;
  }

  .artist img {
    width: 80px;
  }

  .album {
    grid-template-rows: 3fr 2fr;
  }

  .album-frame {
    width: 100px;
  }

  .right-daohang {
    grid-template-rows: 55% 45%;
  }
   
  .song {
    grid-template-columns: 30% auto 12%;
  }
  
  .slide-overlay h2 {
  font-size: clamp(1.2rem, 1vw, 1.2rem);
  font-weight: 600;
}
}

@media (max-width: 825px) {
  .daohang {
    grid-template-columns: 52% 48%;
  }

  .swiper-slide {
    width: 320px;
  }

  .swiper-slide img {
    height: 180px;
  }

  .slide-overlay {
    row-gap: 8px;
    padding: 12px 36px;
  }

  .slide-overlay button {
    padding: 8px 12px;
  }

  .song {
    grid-template-columns: 28% auto 10%;
  }
}

@media (max-width: 700px) {
  .daohang {
    grid-template-columns: 100%;
    grid-template-areas:
      "leftdaohang"
      "rightdaohang";
  }

  .left-daohang {
    grid-area: leftdaohang;
  }

  .slide-overlay {
    row-gap: 12px;
    padding: 20px 30px;
  }

  .swiper-slide {
    width: 300px;
  }

  .artist img {
    width: 110px;
  }

  .album {
    grid-template-rows: 3fr 2fr;
  }

  .album-frame {
    width: 140px;
  }

  .right-daohang {
    grid-area: rightdaohang;
    border-left: unset;
    grid-template-rows: 60% 40%;
    row-gap: 16px;
  }
  
   .song {
    grid-template-columns: 18% auto 8%;
  }

  .song:nth-child(7),
  .song:nth-child(8),
  .song:nth-child(9) {
    display: grid;
  }

  .controls button {
    margin: 20px;
  }
}

@media (max-width: 580px) {
  .swiper-slide {
    width: 300px;
  }

  .swiper-slide img {
    height: 150px;
  }

  .artist img {
       margin: 0 auto;
    width: 80px;
  }

  .album {
    grid-template-rows: 3fr 2fr;
  }

  .album-frame {
    width: 100px;
  }
  
   .slide-overlay h2 {
  font-size: clamp(1.2rem, 1vw, 1.2rem);
  font-weight: 600;
}
}

@media (max-width: 450px) {
  .user-info img {
    border-radius: 50%;
    padding: 6px 6px 2px;
  }

  .swiper-slide {
    width: 280px;
  }

 .swiper-slide img {
    height: 140px;
  }
  
  .slide-overlay {
    row-gap: 8px;
    padding: 10px 28px;
  }
  
   .slide-overlay h2 {
  font-size: clamp(1rem, 1vw, 1rem);
  font-weight: 600;
}
}


.card-body2 {
   display: none;
  }


.artists {
   display: none;
  }
  
  
/* CONTACTS */

.contacts h1 {
  color: var(--app-content-main-color);
  margin: 30px auto;
  padding-top: 10px;
  font-size:1.2em;
}

.contact-status {
  display: flex;
  flex-direction: column;
  padding: 5px 0;
  border-bottom: 1px solid var(--app-content-main-color);
  margin-bottom: 15px;
  margin: 0 20px;
}

.contact-status:last-child {
  border-bottom: unset;
}

.contact-activity {
  display: flex;
  align-items: center;
  column-gap: 14px;
}

.contact-activity img {
    margin-left: 8px;
  max-width: 45px;
  aspect-ratio: 1/1;
  border-radius: 50%;
  object-fit: cover;
}

.contact-activity p {
    color: var(--app-content-main-color);
  font-size: 0.9rem;
  line-height: 1.5;
  padding-right: 10px;
}

.contact-activity p span {
  font-weight: 700;
  color: var(--accent-color);
  margin-left: 2px;
  cursor: pointer;
}

.contact-activity p span:hover {
  text-decoration: underline;
  text-decoration-color: var(--accent-color);
  text-decoration-thickness: 1.5px;
}

.contact-status small {
  color: var(--app-content-main-color);
  font-size: 0.8rem;
  opacity: 0.6;
  margin-left: auto;
  margin-right: 10px;
}
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  