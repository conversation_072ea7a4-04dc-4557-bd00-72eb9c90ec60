.footerlink {
  color: #000;
  position: absolute;
  width: 100%;
  text-align: center;
}

.ftr {
  display:inline-block;
  width:60px;
  height: 60px;
  background: #f1f1f1;
  margin: 15px;
  border-radius: 30%;
  box-shadow:-5px 5px 15px -5px #f1f1f1; 
  overflow: hidden;
  position: relative;
  transition: 0.3s linear;
}

.ftr i {
  line-height: 60px;
  font-size: 26px;
  transition: 0.3s linear;
}

.ftr:nth-child(1) i {
  color:#3b5998;
}

.ftr:nth-child(2) i {
  color:#1da1f2;
}

.ftr:nth-child(3) i {
  color:#c32aa3;
}

.ftr:nth-child(4) i {
  color:#db4437;
}

.ftr:hover {
  transform: scale(1.1);
}

.ftr:hover i {
  transform: scale(1.2);
  color: #fff;
}

.ftr:before {
  content:"";
  position:absolute;
  width:120%;
  height:120%;
  transform: rotate(45deg);
  left: -110%;
  top:90%;
}

.ftr:nth-child(1)::before {
  background: #3b5998;
}

.ftr:nth-child(2)::before {
  background: #1da1f2;
}

.ftr:nth-child(3)::before {
  background: #c32aa3;
}

.ftr:nth-child(4)::before {
  background: #db4437;
}

.ftr:hover::before {
  animation: aaa 0.7s 1;
  top: -10%;
  left: -10%;
}

