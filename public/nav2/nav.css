
:root,[data-bs-theme=light] {
    --ri-blue: #2163e8;
    --ri-indigo: #6610f2;
    --ri-purple: #6f42c1;
    --ri-pink: #e83e8c;
    --ri-red: #d6293e;
    --ri-orange: #fd7e14;
    --ri-yellow: #f7c32e;
    --ri-green: #0cbc87;
    --ri-teal: #20c997;
    --ri-cyan: #4f9ef8;
    --ri-black: #000;
    --ri-white: #fff;
    --ri-gray: #595d69;
    --ri-gray-dark: #191a1f;
    --ri-gray-100: #f7f8f9;
    --ri-gray-200: #f0f1f3;
    --ri-gray-300: #dfe0e5;
    --ri-gray-400: #d0d4d9;
    --ri-gray-500: #a1a1a8;
    --ri-gray-600: #595d69;
    --ri-gray-700: #29292e;
    --ri-gray-800: #191a1f;
    --ri-gray-900: #0f0f10;
    --ri-primary: #2163e8;
    --ri-secondary: #595d69;
    --ri-success: #0cbc87;
    --ri-info: #4f9ef8;
    --ri-warning: #f7c32e;
    --ri-danger: #d6293e;
    --ri-white: #fff;
    --ri-light: #f7f8f9;
    --ri-dark: #191a1f;
    --ri-primary-rgb: 33,99,232;
    --ri-secondary-rgb: 89,93,105;
    --ri-success-rgb: 12,188,135;
    --ri-info-rgb: 79,158,248;
    --ri-warning-rgb: 247,195,46;
    --ri-danger-rgb: 214,41,62;
    --ri-white-rgb: 255,255,255;
    --ri-light-rgb: 247,248,249;
    --ri-dark-rgb: 25,26,31;
    --ri-primary-text: #1a4fba;
    --ri-secondary-text: #595d69;
    --ri-success-text: #0a966c;
    --ri-info-text: #2f5f95;
    --ri-warning-text: #94751c;
    --ri-danger-text: #ab2132;
    --ri-light-text: #595d69;
    --ri-dark-text: #29292e;
    --ri-primary-bg-subtle: #d3e0fa;
    --ri-secondary-bg-subtle: #f7f8f9;
    --ri-success-bg-subtle: #cef2e7;
    --ri-info-bg-subtle: #dcecfe;
    --ri-warning-bg-subtle: #fdf3d5;
    --ri-danger-bg-subtle: #f7d4d8;
    --ri-light-bg-subtle: #fbfcfc;
    --ri-dark-bg-subtle: #d0d4d9;
    --ri-primary-border-subtle: #a6c1f6;
    --ri-secondary-border-subtle: #f0f1f3;
    --ri-success-border-subtle: #9ee4cf;
    --ri-info-border-subtle: #b9d8fc;
    --ri-warning-border-subtle: #fce7ab;
    --ri-danger-border-subtle: #efa9b2;
    --ri-light-border-subtle: #f0f1f3;
    --ri-dark-border-subtle: #a1a1a8;
    --ri-white-rgb: 255,255,255;
    --ri-black-rgb: 0,0,0;
    --ri-body-color-rgb: 89,93,105;
    --ri-body-bg-rgb: 237,237,237;
    --ri-font-sans-serif: system-ui,-apple-system,"Segoe UI",Roboto,"Helvetica Neue","Noto Sans","Liberation Sans",Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";
    --ri-font-monospace: SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;
    --ri-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
    --ri-body-font-family: var(--ri-font-sans-serif);
    --ri-body-font-size: 0.9375rem;
    --ri-body-font-weight: 500;
    --ri-body-line-height: 1.5;
    --ri-body-color: #595d69;
    --ri-emphasis-color: #000;
    --ri-emphasis-color-rgb: 0,0,0;
    --ri-secondary-color: rgba(89, 93, 105, 0.75);
    --ri-secondary-color-rgb: 89,93,105;
    --ri-secondary-bg: #f0f1f3;
    --ri-secondary-bg-rgb: 240,241,243;
    --ri-tertiary-color: rgba(89, 93, 105, 0.5);
    --ri-tertiary-color-rgb: 89,93,105;
    --ri-tertiary-bg: #f7f8f9;
    --ri-tertiary-bg-rgb: 247,248,249;
    --ri-body-bg: #ededed;
    --ri-body-bg-rgb: 237,237,237;
    --ri-link-color: #595d69;
    --ri-link-color-rgb: 255,255,255;
    --ri-link-decoration: none;
    --ri-link-hover-color: #09090b;
    --ri-link-hover-color-rgb: 255,255,255;
    --ri-link-hover-decoration: none;
    --ri-code-color: #e83e8c;
    --ri-highlight-bg: #fdf3d5;
    --ri-border-width: 1px;
    --ri-border-style: solid;
    --ri-border-color: rgba(0, 0, 0, 0.1);
    --ri-border-color-translucent: rgba(0, 0, 0, 0.175);
    --ri-border-radius: 0.25rem;
    --ri-border-radius-sm: 0.2rem;
    --ri-border-radius-lg: 0.7rem;
    --ri-border-radius-xl: 1rem;
    --ri-border-radius-2xl: 2rem;
    --ri-border-radius-pill: 50rem;
    --ri-box-shadow: 0 0.125rem 1rem rgba(var(--ri-body-color-rgb), 0.075);
    --ri-box-shadow-sm: 0 0.125rem 0.25rem rgba(var(--ri-body-color-rgb), 0.075);
    --ri-box-shadow-lg: 0 1rem 3rem rgba(var(--ri-body-color-rgb), 0.175);
    --ri-box-shadow-inset: inset 0 1px 2px rgba(var(--ri-body-color-rgb), 0.075);
    --ri-emphasis-color: #000;
    --ri-form-control-bg: var(--ri-body-bg);
    --ri-form-control-disabled-bg: var(--ri-secondary-bg);
    --ri-highlight-bg: #fdf3d5;
    --ri-breakpoint-xs: 0;
    --ri-breakpoint-sm: 576px;
    --ri-breakpoint-md: 768px;
    --ri-breakpoint-lg: 992px;
    --ri-breakpoint-xl: 1200px;
    --ri-breakpoint-xxl: 1400px
}

[data-bs-theme=dark] {
    --ri-body-color: #a1a1a8;
    --ri-body-color-rgb: 161,161,168;
    --ri-body-bg: #222529;
    --ri-body-bg-rgb: 34,37,41;
    --ri-emphasis-color: #f7f8f9;
    --ri-emphasis-color-rgb: 247,248,249;
    --ri-secondary-color: rgba(161, 161, 168, 0.75);
    --ri-secondary-color-rgb: 161,161,168;
    --ri-secondary-bg: #191a1f;
    --ri-secondary-bg-rgb: 25,26,31;
    --ri-tertiary-color: rgba(161, 161, 168, 0.5);
    --ri-tertiary-color-rgb: 161,161,168;
    --ri-tertiary-bg: #141518;
    --ri-tertiary-bg-rgb: 20,21,24;
    --ri-emphasis-color: #fff;
    --ri-primary-text: #7aa1f1;
    --ri-secondary-text: #dfe0e5;
    --ri-success-text: #6dd7b7;
    --ri-info-text: #95c5fb;
    --ri-warning-text: #fadb82;
    --ri-danger-text: #e67f8b;
    --ri-light-text: #f7f8f9;
    --ri-dark-text: #dfe0e5;
    --ri-primary-bg-subtle: #07142e;
    --ri-secondary-bg-subtle: #0f0f10;
    --ri-success-bg-subtle: #02261b;
    --ri-info-bg-subtle: #102032;
    --ri-warning-bg-subtle: #312709;
    --ri-danger-bg-subtle: #2b080c;
    --ri-light-bg-subtle: #191a1f;
    --ri-dark-bg-subtle: #0d0d10;
    --ri-primary-border-subtle: #143b8b;
    --ri-secondary-border-subtle: #29292e;
    --ri-success-border-subtle: #077151;
    --ri-info-border-subtle: #203f63;
    --ri-warning-border-subtle: #634e12;
    --ri-danger-border-subtle: #801925;
    --ri-light-border-subtle: #29292e;
    --ri-dark-border-subtle: #191a1f;
    --ri-heading-color: #fff;
    --ri-link-color: #a1a1a8;
    --ri-link-hover-color: #595d69;
    --ri-link-color-rgb: 161,161,168;
    --ri-link-hover-color-rgb: 89,93,105;
    --ri-code-color: #f18bba;
    --ri-border-color: #29292e;
    --ri-border-color-translucent: rgba(255, 255, 255, 0.15)
}

*,::after,::before {
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

body {
    margin: 0;
    font-family: var(--ri-body-font-family);
    font-size: var(--ri-body-font-size);
    font-weight: var(--ri-body-font-weight);
    line-height: var(--ri-body-line-height);
    color: var(--ri-body-color);
    text-align: var(--ri-body-text-align);
    background-color: var(--ri-body-bg);
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

hr {
    margin: 1rem 0;
    color: inherit;
    border: 0;
    border-top: var(--ri-border-width) solid;
    opacity: .25
}

.h1,.h2,.h3,.h4,.h5,.h6,h1,h2,h3,h4,h5,h6 {
    margin-top: 0;
    margin-bottom: .5rem;
    font-weight: 500;
    line-height: 1.2;
    color: var(--ri-heading-color,inherit)
}

.h1,h1 {
    font-size: calc(1.359375rem + 1.3125vw)
}

.h2,h2 {
    font-size: calc(1.3125rem + .75vw)
}

.h3,h3 {
    font-size: calc(1.2890625rem + .46875vw)
}

.h4,h4 {
    font-size: calc(1.265625rem + .1875vw)
}

.h5,h5 {
    font-size: 1.171875rem
}

.h6,h6 {
    font-size: .9375rem
}

p {
    margin-top: 0;
    margin-bottom: 1rem
}

abbr[title] {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
    cursor: help;
    -webkit-text-decoration-skip-ink: none;
    text-decoration-skip-ink: none
}

address {
    margin-bottom: 1rem;
    font-style: normal;
    line-height: inherit
}

ol,ul {
    padding-left: 2rem
}

dl,ol,ul {
    margin-top: 0;
    margin-bottom: 1rem
}

ol ol,ol ul,ul ol,ul ul {
    margin-bottom: 0
}

dt {
    font-weight: 700
}

dd {
    margin-bottom: .5rem;
    margin-left: 0
}

blockquote {
    margin: 0 0 1rem
}

b,strong {
    font-weight: bolder
}

.small,small {
    font-size: .875em
}

.mark,mark {
    padding: .1875em;
    background-color: var(--ri-highlight-bg)
}

sub,sup {
    position: relative;
    font-size: .75em;
    line-height: 0;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

a {
    color: #fff;
    text-decoration: none
}

a:hover {
    --ri-link-color-rgb: var(--ri-link-hover-color-rgb);
    text-decoration: none
}

a:not([href]):not([class]),a:not([href]):not([class]):hover {
    color: inherit;
    text-decoration: none
}

code,kbd,pre,samp {
    font-family: var(--ri-font-monospace);
    font-size: 1em
}

pre {
    display: block;
    margin-top: 0;
    margin-bottom: 1rem;
    overflow: auto;
    font-size: .875em
}

pre code {
    font-size: inherit;
    color: inherit;
    word-break: normal
}

code {
    font-size: .875em;
    color: var(--ri-code-color);
    word-wrap: break-word
}

a>code {
    color: inherit
}

kbd {
    padding: .1875rem .375rem;
    font-size: .875em;
    color: var(--ri-white);
    background-color: #191a1f;
    border-radius: .2rem
}

kbd kbd {
    padding: 0;
    font-size: 1em
}

figure {
    margin: 0 0 1rem
}

img,svg {
    vertical-align: middle
}

table {
    caption-side: bottom;
    border-collapse: collapse
}

caption {
    padding-top: .5rem;
    padding-bottom: .5rem;
    color: var(--ri-secondary-color);
    text-align: left
}

th {
    text-align: inherit;
    text-align: -webkit-match-parent
}

tbody,td,tfoot,th,thead,tr {
    border-color: inherit;
    border-style: solid;
    border-width: 0
}

label {
    display: inline-block
}

button {
    border-radius: 0
}

button:focus:not(:focus-visible) {
    outline: 0
}

button,input,optgroup,select,textarea {
    margin: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit
}

button,select {
    text-transform: none
}

[role=button] {
    cursor: pointer
}

select {
    word-wrap: normal
}

select:disabled {
    opacity: 1
}

[list]:not([type=date]):not([type=datetime-local]):not([type=month]):not([type=week]):not([type=time])::-webkit-calendar-picker-indicator {
    display: none!important
}

[type=button],[type=reset],[type=submit],button {
    -webkit-appearance: button
}

[type=button]:not(:disabled),[type=reset]:not(:disabled),[type=submit]:not(:disabled),button:not(:disabled) {
    cursor: pointer
}

::-moz-focus-inner {
    padding: 0;
    border-style: none
}

textarea {
    resize: vertical
}

fieldset {
    min-width: 0;
    padding: 0;
    margin: 0;
    border: 0
}

legend {
    float: left;
    width: 100%;
    padding: 0;
    margin-bottom: .5rem;
    font-size: calc(1.275rem + .3vw);
    line-height: inherit
}

legend+* {
    clear: left
}

::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-fields-wrapper,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-text,::-webkit-datetime-edit-year-field {
    padding: 0
}

::-webkit-inner-spin-button {
    height: auto
}

[type=search] {
    outline-offset: -2px;
    -webkit-appearance: textfield
}

::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-color-swatch-wrapper {
    padding: 0
}

::file-selector-button {
    font: inherit;
    -webkit-appearance: button
}

output {
    display: inline-block
}

iframe {
    border: 0
}

summary {
    display: list-item;
    cursor: pointer
}

progress {
    vertical-align: baseline
}

[hidden] {
    display: none!important
}

.lead {
    font-size: 1.171875rem;
    font-weight: 300
}

.display-1 {
    font-size: calc(1.625rem + 4.5vw);
    font-weight: 300;
    line-height: 1.2
}

.display-2 {
    font-size: calc(1.575rem + 3.9vw);
    font-weight: 300;
    line-height: 1.2
}

.display-3 {
    font-size: calc(1.525rem + 3.3vw);
    font-weight: 300;
    line-height: 1.2
}

.display-4 {
    font-size: calc(1.475rem + 2.7vw);
    font-weight: 300;
    line-height: 1.2
}

.display-5 {
    font-size: calc(1.425rem + 2.1vw);
    font-weight: 300;
    line-height: 1.2
}

.display-6 {
    font-size: calc(1.375rem + 1.5vw);
    font-weight: 300;
    line-height: 1.2
}

.list-unstyled {
    padding-left: 0;
    list-style: none
}

.list-inline {
    padding-left: 0;
    list-style: none
}

.list-inline-item {
    display: inline-block
}

.list-inline-item:not(:last-child) {
    margin-right: .5rem
}

.initialism {
    font-size: .875em;
    text-transform: uppercase
}

.blockquote {
    margin-bottom: 1rem;
    font-size: 1.171875rem
}

.blockquote>:last-child {
    margin-bottom: 0
}

.blockquote-footer {
    margin-top: -1rem;
    margin-bottom: 1rem;
    font-size: .875em;
    color: #595d69
}

.blockquote-footer::before {
    content: "— "
}

.container,.container-fluid,.container-lg,.container-md,.container-sm,.container-xl,.container-xxl {
    --ri-gutter-x: 1.5rem;
    --ri-gutter-y: 0;
    width: 100%;
    padding-right: calc(var(--ri-gutter-x) * .5);
    padding-left: calc(var(--ri-gutter-x) * .5);
    margin-right: auto;
    margin-left: auto
}

.row {
    --ri-gutter-x: 1.5rem;
    --ri-gutter-y: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-top: calc(-1 * var(--ri-gutter-y));
    margin-right: calc(-.5 * var(--ri-gutter-x));
    margin-left: calc(-.5 * var(--ri-gutter-x))
}

.row>* {
    -ms-flex-negative: 0;
    flex-shrink: 0;
    width: 100%;
    max-width: 100%;
    padding-right: calc(var(--ri-gutter-x) * .5);
    padding-left: calc(var(--ri-gutter-x) * .5);
    margin-top: var(--ri-gutter-y)
}

.col {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%
}

.row-cols-auto>* {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto
}

.row-cols-1>* {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%
}

.row-cols-2>* {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%
}

.row-cols-3>* {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.3333333333%
}

.row-cols-4>* {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%
}

.row-cols-5>* {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 20%
}

.row-cols-6>* {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.6666666667%
}

.col-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto
}

.col-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 8.33333333%
}

.col-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.66666667%
}

.col-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%
}

.col-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.33333333%
}

.col-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 41.66666667%
}

.col-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%
}

.col-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 58.33333333%
}

.col-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 66.66666667%
}

.col-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 75%
}

.col-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 83.33333333%
}

.col-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 91.66666667%
}

.col-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%
}

.offset-1 {
    margin-left: 8.33333333%
}

.offset-2 {
    margin-left: 16.66666667%
}

.offset-3 {
    margin-left: 25%
}

.offset-4 {
    margin-left: 33.33333333%
}

.offset-5 {
    margin-left: 41.66666667%
}

.offset-6 {
    margin-left: 50%
}

.offset-7 {
    margin-left: 58.33333333%
}

.offset-8 {
    margin-left: 66.66666667%
}

.offset-9 {
    margin-left: 75%
}

.offset-10 {
    margin-left: 83.33333333%
}

.offset-11 {
    margin-left: 91.66666667%
}

.g-0,.gx-0 {
    --ri-gutter-x: 0
}

.g-0,.gy-0 {
    --ri-gutter-y: 0
}

.g-1,.gx-1 {
    --ri-gutter-x: 0.25rem
}

.g-1,.gy-1 {
    --ri-gutter-y: 0.25rem
}

.g-2,.gx-2 {
    --ri-gutter-x: 0.5rem
}

.g-2,.gy-2 {
    --ri-gutter-y: 0.5rem
}

.g-3,.gx-3 {
    --ri-gutter-x: 1rem
}

.g-3,.gy-3 {
    --ri-gutter-y: 1rem
}

.g-4,.gx-4 {
    --ri-gutter-x: 1.5rem
}

.g-4,.gy-4 {
    --ri-gutter-y: 1.5rem
}

.g-5,.gx-5 {
    --ri-gutter-x: 3rem
}

.g-5,.gy-5 {
    --ri-gutter-y: 3rem
}

.g-6,.gx-6 {
    --ri-gutter-x: 4.5rem
}

.g-6,.gy-6 {
    --ri-gutter-y: 4.5rem
}

.g-7,.gx-7 {
    --ri-gutter-x: 6rem
}

.g-7,.gy-7 {
    --ri-gutter-y: 6rem
}

.g-8,.gx-8 {
    --ri-gutter-x: 7.5rem
}

.g-8,.gy-8 {
    --ri-gutter-y: 7.5rem
}

.g-9,.gx-9 {
    --ri-gutter-x: 9rem
}

.g-9,.gy-9 {
    --ri-gutter-y: 9rem
}

.g-10,.gx-10 {
    --ri-gutter-x: 10.5rem
}

.g-10,.gy-10 {
    --ri-gutter-y: 10.5rem
}

.form-label {
    margin-bottom: .5rem
}

.col-form-label {
    padding-top: calc(.375rem + var(--ri-border-width));
    padding-bottom: calc(.375rem + var(--ri-border-width));
    margin-bottom: 0;
    font-size: inherit;
    line-height: 1.5
}

.col-form-label-lg {
    padding-top: calc(.5rem + var(--ri-border-width));
    padding-bottom: calc(.5rem + var(--ri-border-width));
    font-size: 1.171875rem
}

.col-form-label-sm {
    padding-top: calc(.25rem + var(--ri-border-width));
    padding-bottom: calc(.25rem + var(--ri-border-width));
    font-size: .8203125rem
}

.form-text {
    margin-top: .25rem;
    font-size: .875em;
    color: var(--ri-secondary-color)
}

.form-control {
    display: block;
    width: 100%;
    padding: .375rem .75rem;
    font-family: none;
    font-size: .9375rem;
    font-weight: 500;
    line-height: 1.5;
    color: var(--ri-body-color);
    background-color: var(--ri-form-control-bg);
    background-clip: padding-box;
    border: var(--ri-border-width) solid var(--ri-border-color);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: .25rem;
    -webkit-transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out,-webkit-box-shadow .15s ease-in-out
}

.form-control[type=file] {
    overflow: hidden
}

.form-control[type=file]:not(:disabled):not([readonly]) {
    cursor: pointer
}

.form-control:focus {
    color: var(--ri-body-color);
    background-color: var(--ri-form-control-bg);
    border-color: #90b1f4;
    outline: 0;
    -webkit-box-shadow: 0 0 0 0 rgba(33,99,232,.25);
    box-shadow: 0 0 0 0 rgba(33,99,232,.25)
}

.form-control::-webkit-date-and-time-value {
    height: 1.5em
}

.form-control::-webkit-datetime-edit {
    display: block;
    padding: 0
}

.form-control::-webkit-input-placeholder {
    color: var(--ri-secondary-color);
    opacity: 1
}

.form-control::-moz-placeholder {
    color: var(--ri-secondary-color);
    opacity: 1
}

.form-control:-ms-input-placeholder {
    color: var(--ri-secondary-color);
    opacity: 1
}

.form-control::-ms-input-placeholder {
    color: var(--ri-secondary-color);
    opacity: 1
}

.form-control::placeholder {
    color: var(--ri-secondary-color);
    opacity: 1
}

.form-control:disabled {
    background-color: var(--ri-form-control-disabled-bg);
    opacity: 1
}

.form-control::file-selector-button {
    padding: .375rem .75rem;
    margin: -.375rem -.75rem;
    -webkit-margin-end: .75rem;
    margin-inline-end:.75rem;color: var(--ri-body-color);
    background-color: var(--ri-tertiary-bg);
    pointer-events: none;
    border-color: inherit;
    border-style: solid;
    border-width: 0;
    border-inline-end-width:var(--ri-border-width);border-radius: 0;
    -webkit-transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out,-webkit-box-shadow .15s ease-in-out
}

.form-control:hover:not(:disabled):not([readonly])::file-selector-button {
    background-color: var(--ri-secondary-bg)
}

.form-control-plaintext {
    display: block;
    width: 100%;
    padding: .375rem 0;
    margin-bottom: 0;
    line-height: 1.5;
    color: var(--ri-body-color);
    background-color: transparent;
    border: solid transparent;
    border-width: var(--ri-border-width) 0
}

.form-control-plaintext:focus {
    outline: 0
}

.form-control-plaintext.form-control-lg,.form-control-plaintext.form-control-sm {
    padding-right: 0;
    padding-left: 0
}

.form-control-sm {
    min-height: calc(1.5em + .5rem + calc(var(--ri-border-width) * 2));
    padding: .25rem .5rem;
    font-size: .8203125rem;
    border-radius: .2rem
}

.form-control-sm::file-selector-button {
    padding: .25rem .5rem;
    margin: -.25rem -.5rem;
    -webkit-margin-end: .5rem;
    margin-inline-end:.5rem}

.form-control-lg {
    min-height: calc(1.5em + 1rem + calc(var(--ri-border-width) * 2));
    padding: .5rem 1rem;
    font-size: 1.171875rem;
    border-radius: .7rem
}

.form-control-lg::file-selector-button {
    padding: .5rem 1rem;
    margin: -.5rem -1rem;
    -webkit-margin-end: 1rem;
    margin-inline-end:1rem}

textarea.form-control {
    min-height: calc(1.5em + .75rem + calc(var(--ri-border-width) * 2))
}

textarea.form-control-sm {
    min-height: calc(1.5em + .5rem + calc(var(--ri-border-width) * 2))
}

textarea.form-control-lg {
    min-height: calc(1.5em + 1rem + calc(var(--ri-border-width) * 2))
}

.form-control-color {
    width: 3rem;
    height: calc(1.5em + .75rem + calc(var(--ri-border-width) * 2));
    padding: .375rem
}

.form-control-color:not(:disabled):not([readonly]) {
    cursor: pointer
}

.form-control-color::-moz-color-swatch {
    border: 0!important;
    border-radius: .25rem
}

.form-control-color::-webkit-color-swatch {
    border-radius: .25rem
}

.form-control-color.form-control-sm {
    height: calc(1.5em + .5rem + calc(var(--ri-border-width) * 2))
}

.form-control-color.form-control-lg {
    height: calc(1.5em + 1rem + calc(var(--ri-border-width) * 2))
}

.form-select {
    --ri-form-select-bg-img: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23191a1f' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
    display: block;
    width: 100%;
    padding: .375rem 2.25rem .375rem .75rem;
    -moz-padding-start: calc(.75rem - 3px);
    font-family: none;
    font-size: .9375rem;
    font-weight: 500;
    line-height: 1.5;
    color: var(--ri-body-color);
    background-color: var(--ri-form-control-bg);
    background-image: var(--ri-form-select-bg-img),var(--ri-form-select-bg-icon,none);
    background-repeat: no-repeat;
    background-position: right .75rem center;
    background-size: 16px 12px;
    border: var(--ri-border-width) solid var(--ri-border-color);
    border-radius: .25rem;
    -webkit-transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

.form-select:focus {
    border-color: #90b1f4;
    outline: 0;
    -webkit-box-shadow: 0 0 0 0 rgba(33,99,232,.25);
    box-shadow: 0 0 0 0 rgba(33,99,232,.25)
}

.form-select[multiple],.form-select[size]:not([size="1"]) {
    padding-right: .75rem;
    background-image: none
}

.form-select:disabled {
    background-color: var(--ri-form-control-disabled-bg)
}

.form-select:-moz-focusring {
    color: transparent;
    text-shadow: 0 0 0 var(--ri-body-color)
}

.form-select-sm {
    padding-top: .25rem;
    padding-bottom: .25rem;
    padding-left: .5rem;
    font-size: .8203125rem;
    border-radius: .2rem
}

.form-select-lg {
    padding-top: .5rem;
    padding-bottom: .5rem;
    padding-left: 1rem;
    font-size: 1.171875rem;
    border-radius: .7rem
}

[data-bs-theme=dark] .form-select {
    --ri-form-select-bg-img: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23a1a1a8' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e")
}

.form-check {
    display: block;
    min-height: 1.40625rem;
    padding-left: 1.5em;
    margin-bottom: .125rem
}

.form-check .form-check-input {
    float: left;
    margin-left: -1.5em
}

.form-check-reverse {
    padding-right: 1.5em;
    padding-left: 0;
    text-align: right
}

.form-check-reverse .form-check-input {
    float: right;
    margin-right: -1.5em;
    margin-left: 0
}

.form-check-input {
    --ri-form-check-bg: var(--ri-form-control-bg);
    width: 1em;
    height: 1em;
    margin-top: .25em;
    vertical-align: top;
    background-color: var(--ri-form-check-bg);
    background-image: var(--ri-form-check-bg-image);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    border: var(--ri-border-width) solid var(--ri-border-color);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    print-color-adjust: exact
}

.form-check-input[type=checkbox] {
    border-radius: .25em
}

.form-check-input[type=radio] {
    border-radius: 50%
}

.form-check-input:active {
    -webkit-filter: brightness(90%);
    filter: brightness(90%)
}

.form-check-input:focus {
    border-color: #90b1f4;
    outline: 0;
    -webkit-box-shadow: 0 0 0 0 rgba(33,99,232,.25);
    box-shadow: 0 0 0 0 rgba(33,99,232,.25)
}

.form-check-input:checked {
    background-color: #2163e8;
    border-color: #2163e8
}

.form-check-input:checked[type=checkbox] {
    --ri-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e")
}

.form-check-input:checked[type=radio] {
    --ri-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e")
}

.form-check-input[type=checkbox]:indeterminate {
    background-color: #2163e8;
    border-color: #2163e8;
    --ri-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e")
}

.form-check-input:disabled {
    pointer-events: none;
    -webkit-filter: none;
    filter: none;
    opacity: .5
}

.form-check-input:disabled~.form-check-label,.form-check-input[disabled]~.form-check-label {
    cursor: default;
    opacity: .5
}

.form-switch {
    padding-left: 2.5em
}

.form-switch .form-check-input {
    --ri-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
    width: 2em;
    margin-left: -2.5em;
    background-image: var(--ri-form-switch-bg);
    background-position: left center;
    border-radius: 2em;
    -webkit-transition: background-position .15s ease-in-out;
    transition: background-position .15s ease-in-out
}

.form-switch .form-check-input:focus {
    --ri-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%2390b1f4'/%3e%3c/svg%3e")
}

.form-switch .form-check-input:checked {
    background-position: right center;
    --ri-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e")
}

.form-switch.form-check-reverse {
    padding-right: 2.5em;
    padding-left: 0
}

.form-switch.form-check-reverse .form-check-input {
    margin-right: -2.5em;
    margin-left: 0
}

.form-check-inline {
    display: inline-block;
    margin-right: 1rem
}

.btn-check {
    position: absolute;
    clip: rect(0,0,0,0);
    pointer-events: none
}

.btn-check:disabled+.btn,.btn-check[disabled]+.btn {
    pointer-events: none;
    -webkit-filter: none;
    filter: none;
    opacity: .65
}

[data-bs-theme=dark] .form-switch .form-check-input:not(:checked):not(:focus) {
    --ri-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%28255, 255, 255, 0.25%29'/%3e%3c/svg%3e")
}

.form-range {
    width: 100%;
    height: 1rem;
    padding: 0;
    background-color: transparent;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

.form-range:focus {
    outline: 0
}

.form-range:focus::-webkit-slider-thumb {
    -webkit-box-shadow: 0 0 0 1px #ededed,0 0 0 0 rgba(33,99,232,.25);
    box-shadow: 0 0 0 1px #ededed,0 0 0 0 rgba(33,99,232,.25)
}

.form-range:focus::-moz-range-thumb {
    box-shadow: 0 0 0 1px #ededed,0 0 0 0 rgba(33,99,232,.25)
}

.form-range::-moz-focus-outer {
    border: 0
}

.form-range::-webkit-slider-thumb {
    width: 1rem;
    height: 1rem;
    margin-top: -.25rem;
    background-color: #2163e8;
    border: 0;
    border-radius: 1rem;
    -webkit-transition: background-color .15s ease-in-out,border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: background-color .15s ease-in-out,border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    transition: background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    -webkit-appearance: none;
    appearance: none
}

.form-range::-webkit-slider-thumb:active {
    background-color: #bcd0f8
}

.form-range::-webkit-slider-runnable-track {
    width: 100%;
    height: .5rem;
    color: transparent;
    cursor: pointer;
    background-color: var(--ri-tertiary-bg);
    border-color: transparent;
    border-radius: 1rem
}

.form-range::-moz-range-thumb {
    width: 1rem;
    height: 1rem;
    background-color: #2163e8;
    border: 0;
    border-radius: 1rem;
    -moz-transition: background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    transition: background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    -moz-appearance: none;
    appearance: none
}

.form-range::-moz-range-thumb:active {
    background-color: #bcd0f8
}

.form-range::-moz-range-track {
    width: 100%;
    height: .5rem;
    color: transparent;
    cursor: pointer;
    background-color: var(--ri-tertiary-bg);
    border-color: transparent;
    border-radius: 1rem
}

.form-range:disabled {
    pointer-events: none
}

.form-range:disabled::-webkit-slider-thumb {
    background-color: var(--ri-secondary-color)
}

.form-range:disabled::-moz-range-thumb {
    background-color: var(--ri-secondary-color)
}

.form-floating {
    position: relative
}

.form-floating::before:not(.form-control:disabled) {
    position: absolute;
    top: var(--ri-border-width);
    left: var(--ri-border-width);
    width: calc(100% - (calc(calc(.375em + .1875rem) + calc(.75em + .375rem))));
    height: 1.875em;
    content: "";
    background-color: var(--ri-form-control-bg);
    border-radius: .25rem
}

.form-floating>.form-control,.form-floating>.form-control-plaintext,.form-floating>.form-select {
    height: calc(3.5rem + calc(var(--ri-border-width) * 2));
    line-height: 1.25
}

.form-floating>label {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 1rem .75rem;
    overflow: hidden;
    text-align: start;
    text-overflow: ellipsis;
    white-space: nowrap;
    pointer-events: none;
    border: var(--ri-border-width) solid transparent;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transition: opacity .1s ease-in-out,-webkit-transform .1s ease-in-out;
    transition: opacity .1s ease-in-out,-webkit-transform .1s ease-in-out;
    transition: opacity .1s ease-in-out,transform .1s ease-in-out;
    transition: opacity .1s ease-in-out,transform .1s ease-in-out,-webkit-transform .1s ease-in-out
}

.form-floating>.form-control,.form-floating>.form-control-plaintext {
    padding: 1rem .75rem
}

.form-floating>.form-control-plaintext::-webkit-input-placeholder,.form-floating>.form-control::-webkit-input-placeholder {
    color: transparent
}

.form-floating>.form-control-plaintext::-moz-placeholder,.form-floating>.form-control::-moz-placeholder {
    color: transparent
}

.form-floating>.form-control-plaintext:-ms-input-placeholder,.form-floating>.form-control:-ms-input-placeholder {
    color: transparent
}

.form-floating>.form-control-plaintext::-ms-input-placeholder,.form-floating>.form-control::-ms-input-placeholder {
    color: transparent
}

.form-floating>.form-control-plaintext::placeholder,.form-floating>.form-control::placeholder {
    color: transparent
}

.form-floating>.form-control-plaintext:not(:-moz-placeholder-shown),.form-floating>.form-control:not(:-moz-placeholder-shown) {
    padding-top: 1.625rem;
    padding-bottom: .625rem
}

.form-floating>.form-control-plaintext:not(:-ms-input-placeholder),.form-floating>.form-control:not(:-ms-input-placeholder) {
    padding-top: 1.625rem;
    padding-bottom: .625rem
}

.form-floating>.form-control-plaintext:focus,.form-floating>.form-control-plaintext:not(:placeholder-shown),.form-floating>.form-control:focus,.form-floating>.form-control:not(:placeholder-shown) {
    padding-top: 1.625rem;
    padding-bottom: .625rem
}

.form-floating>.form-control-plaintext:-webkit-autofill,.form-floating>.form-control:-webkit-autofill {
    padding-top: 1.625rem;
    padding-bottom: .625rem
}

.form-floating>.form-select {
    padding-top: 1.625rem;
    padding-bottom: .625rem
}

.form-floating>.form-control:not(:-moz-placeholder-shown)~label {
    opacity: .65;
    transform: scale(.85) translateY(-.5rem) translateX(.15rem)
}

.form-floating>.form-control:not(:-ms-input-placeholder)~label {
    opacity: .65;
    transform: scale(.85) translateY(-.5rem) translateX(.15rem)
}

.form-floating>.form-control-plaintext~label,.form-floating>.form-control:focus~label,.form-floating>.form-control:not(:placeholder-shown)~label,.form-floating>.form-select~label {
    opacity: .65;
    -webkit-transform: scale(.85) translateY(-.5rem) translateX(.15rem);
    transform: scale(.85) translateY(-.5rem) translateX(.15rem)
}

.form-floating>.form-control:-webkit-autofill~label {
    opacity: .65;
    -webkit-transform: scale(.85) translateY(-.5rem) translateX(.15rem);
    transform: scale(.85) translateY(-.5rem) translateX(.15rem)
}

.form-floating>.form-control-plaintext~label {
    border-width: var(--ri-border-width) 0
}

.form-floating>.form-control:disabled~label {
    color: #595d69
}

.input-group {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    width: 100%
}

.input-group>.form-control,.input-group>.form-floating,.input-group>.form-select {
    position: relative;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0
}

.input-group>.form-control:focus,.input-group>.form-floating:focus-within,.input-group>.form-select:focus {
    z-index: 5
}

.input-group .btn {
    position: relative;
    z-index: 2
}

.input-group .btn:focus {
    z-index: 5
}

.input-group-text {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: .375rem .75rem;
    font-size: .9375rem;
    font-weight: 500;
    line-height: 1.5;
    color: var(--ri-body-color);
    text-align: center;
    white-space: nowrap;
    background-color: var(--ri-tertiary-bg);
    border: var(--ri-border-width) solid var(--ri-border-color);
    border-radius: .25rem
}

.input-group-lg>.btn,.input-group-lg>.form-control,.input-group-lg>.form-select,.input-group-lg>.input-group-text {
    padding: .5rem 1rem;
    font-size: 1.171875rem;
    border-radius: .7rem
}

.input-group-sm>.btn,.input-group-sm>.form-control,.input-group-sm>.form-select,.input-group-sm>.input-group-text {
    padding: .25rem .5rem;
    font-size: .8203125rem;
    border-radius: .2rem
}

.input-group-lg>.form-select,.input-group-sm>.form-select {
    padding-right: 3rem
}

.input-group:not(.has-validation)>.dropdown-toggle:nth-last-child(n+3),.input-group:not(.has-validation)>.form-floating:not(:last-child)>.form-control,.input-group:not(.has-validation)>.form-floating:not(:last-child)>.form-select,.input-group:not(.has-validation)>:not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.input-group.has-validation>.dropdown-toggle:nth-last-child(n+4),.input-group.has-validation>.form-floating:nth-last-child(n+3)>.form-control,.input-group.has-validation>.form-floating:nth-last-child(n+3)>.form-select,.input-group.has-validation>:nth-last-child(n+3):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.input-group>:not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
    margin-left: calc(var(--ri-border-width) * -1);
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.input-group>.form-floating:not(:first-child)>.form-control,.input-group>.form-floating:not(:first-child)>.form-select {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.valid-feedback {
    display: none;
    width: 100%;
    margin-top: .25rem;
    font-size: .875em;
    color: var(--ri-success-text)
}

.valid-tooltip {
    position: absolute;
    top: 100%;
    z-index: 5;
    display: none;
    max-width: 100%;
    padding: .25rem .5rem;
    margin-top: .1rem;
    font-size: .8203125rem;
    color: #fff;
    background-color: var(--ri-success);
    border-radius: var(--ri-border-radius)
}

.is-valid~.valid-feedback,.is-valid~.valid-tooltip,.was-validated :valid~.valid-feedback,.was-validated :valid~.valid-tooltip {
    display: block
}

.form-control.is-valid,.was-validated .form-control:valid {
    border-color: var(--ri-success);
    padding-right: calc(1.5em + .75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%230cbc87' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(.375em + .1875rem) center;
    background-size: calc(.75em + .375rem) calc(.75em + .375rem)
}

.form-control.is-valid:focus,.was-validated .form-control:valid:focus {
    border-color: var(--ri-success);
    -webkit-box-shadow: 0 0 0 0 rgba(var(--ri-success-rgb),.25);
    box-shadow: 0 0 0 0 rgba(var(--ri-success-rgb),.25)
}

.was-validated textarea.form-control:valid,textarea.form-control.is-valid {
    padding-right: calc(1.5em + .75rem);
    background-position: top calc(.375em + .1875rem) right calc(.375em + .1875rem)
}

.form-select.is-valid,.was-validated .form-select:valid {
    border-color: var(--ri-success)
}

.form-select.is-valid:not([multiple]):not([size]),.form-select.is-valid:not([multiple])[size="1"],.was-validated .form-select:valid:not([multiple]):not([size]),.was-validated .form-select:valid:not([multiple])[size="1"] {
    --ri-form-select-bg-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%230cbc87' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    padding-right: 4.125rem;
    background-position: right .75rem center,center right 2.25rem;
    background-size: 16px 12px,calc(.75em + .375rem) calc(.75em + .375rem)
}

.form-select.is-valid:focus,.was-validated .form-select:valid:focus {
    border-color: var(--ri-success);
    -webkit-box-shadow: 0 0 0 0 rgba(var(--ri-success-rgb),.25);
    box-shadow: 0 0 0 0 rgba(var(--ri-success-rgb),.25)
}

.form-control-color.is-valid,.was-validated .form-control-color:valid {
    width: calc(3rem + calc(1.5em + .75rem))
}

.form-check-input.is-valid,.was-validated .form-check-input:valid {
    border-color: var(--ri-success)
}

.form-check-input.is-valid:checked,.was-validated .form-check-input:valid:checked {
    background-color: var(--ri-success-text)
}

.form-check-input.is-valid:focus,.was-validated .form-check-input:valid:focus {
    -webkit-box-shadow: 0 0 0 0 rgba(var(--ri-success-rgb),.25);
    box-shadow: 0 0 0 0 rgba(var(--ri-success-rgb),.25)
}

.form-check-input.is-valid~.form-check-label,.was-validated .form-check-input:valid~.form-check-label {
    color: var(--ri-success-text)
}

.form-check-inline .form-check-input~.valid-feedback {
    margin-left: .5em
}

.input-group>.form-control:not(:focus).is-valid,.input-group>.form-floating:not(:focus-within).is-valid,.input-group>.form-select:not(:focus).is-valid,.was-validated .input-group>.form-control:not(:focus):valid,.was-validated .input-group>.form-floating:not(:focus-within):valid,.was-validated .input-group>.form-select:not(:focus):valid {
    z-index: 3
}

.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: .25rem;
    font-size: .875em;
    color: var(--ri-danger-text)
}

.invalid-tooltip {
    position: absolute;
    top: 100%;
    z-index: 5;
    display: none;
    max-width: 100%;
    padding: .25rem .5rem;
    margin-top: .1rem;
    font-size: .8203125rem;
    color: #fff;
    background-color: var(--ri-danger);
    border-radius: var(--ri-border-radius)
}

.is-invalid~.invalid-feedback,.is-invalid~.invalid-tooltip,.was-validated :invalid~.invalid-feedback,.was-validated :invalid~.invalid-tooltip {
    display: block
}

.form-control.is-invalid,.was-validated .form-control:invalid {
    border-color: var(--ri-danger);
    padding-right: calc(1.5em + .75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23d6293e'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23d6293e' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(.375em + .1875rem) center;
    background-size: calc(.75em + .375rem) calc(.75em + .375rem)
}

.form-control.is-invalid:focus,.was-validated .form-control:invalid:focus {
    border-color: var(--ri-danger);
    -webkit-box-shadow: 0 0 0 0 rgba(var(--ri-danger-rgb),.25);
    box-shadow: 0 0 0 0 rgba(var(--ri-danger-rgb),.25)
}

.was-validated textarea.form-control:invalid,textarea.form-control.is-invalid {
    padding-right: calc(1.5em + .75rem);
    background-position: top calc(.375em + .1875rem) right calc(.375em + .1875rem)
}

.form-select.is-invalid,.was-validated .form-select:invalid {
    border-color: var(--ri-danger)
}

.form-select.is-invalid:not([multiple]):not([size]),.form-select.is-invalid:not([multiple])[size="1"],.was-validated .form-select:invalid:not([multiple]):not([size]),.was-validated .form-select:invalid:not([multiple])[size="1"] {
    --ri-form-select-bg-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23d6293e'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23d6293e' stroke='none'/%3e%3c/svg%3e");
    padding-right: 4.125rem;
    background-position: right .75rem center,center right 2.25rem;
    background-size: 16px 12px,calc(.75em + .375rem) calc(.75em + .375rem)
}

.form-select.is-invalid:focus,.was-validated .form-select:invalid:focus {
    border-color: var(--ri-danger);
    -webkit-box-shadow: 0 0 0 0 rgba(var(--ri-danger-rgb),.25);
    box-shadow: 0 0 0 0 rgba(var(--ri-danger-rgb),.25)
}

.form-control-color.is-invalid,.was-validated .form-control-color:invalid {
    width: calc(3rem + calc(1.5em + .75rem))
}

.form-check-input.is-invalid,.was-validated .form-check-input:invalid {
    border-color: var(--ri-danger)
}

.form-check-input.is-invalid:checked,.was-validated .form-check-input:invalid:checked {
    background-color: var(--ri-danger-text)
}

.form-check-input.is-invalid:focus,.was-validated .form-check-input:invalid:focus {
    -webkit-box-shadow: 0 0 0 0 rgba(var(--ri-danger-rgb),.25);
    box-shadow: 0 0 0 0 rgba(var(--ri-danger-rgb),.25)
}

.form-check-input.is-invalid~.form-check-label,.was-validated .form-check-input:invalid~.form-check-label {
    color: var(--ri-danger-text)
}

.form-check-inline .form-check-input~.invalid-feedback {
    margin-left: .5em
}

.input-group>.form-control:not(:focus).is-invalid,.input-group>.form-floating:not(:focus-within).is-invalid,.input-group>.form-select:not(:focus).is-invalid,.was-validated .input-group>.form-control:not(:focus):invalid,.was-validated .input-group>.form-floating:not(:focus-within):invalid,.was-validated .input-group>.form-select:not(:focus):invalid {
    z-index: 4
}

.alert {
    --ri-alert-bg: transparent;
    --ri-alert-padding-x: 1rem;
    --ri-alert-padding-y: 1rem;
    --ri-alert-margin-bottom: 1rem;
    --ri-alert-color: inherit;
    --ri-alert-border-color: transparent;
    --ri-alert-border: var(--ri-border-width) solid var(--ri-alert-border-color);
    --ri-alert-border-radius: 0.25rem;
    --ri-alert-link-color: inherit;
    position: relative;
    padding: var(--ri-alert-padding-y) var(--ri-alert-padding-x);
    margin-bottom: var(--ri-alert-margin-bottom);
    color: var(--ri-alert-color);
    background-color: var(--ri-alert-bg);
    border: var(--ri-alert-border);
    border-radius: var(--ri-alert-border-radius)
}

.alert-heading {
    color: inherit
}

.alert-link {
    font-weight: 700;
    color: var(--ri-alert-link-color)
}

.alert-dismissible {
    padding-right: 3rem
}

.alert-dismissible .btn-close {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    padding: 1.25rem 1rem
}

.alert-primary {
    --ri-alert-color: var(--ri-primary-text);
    --ri-alert-bg: var(--ri-primary-bg-subtle);
    --ri-alert-border-color: var(--ri-primary-border-subtle);
    --ri-alert-link-color: var(--ri-primary-text)
}

.alert-secondary {
    --ri-alert-color: var(--ri-secondary-text);
    --ri-alert-bg: var(--ri-secondary-bg-subtle);
    --ri-alert-border-color: var(--ri-secondary-border-subtle);
    --ri-alert-link-color: var(--ri-secondary-text)
}

.alert-success {
    --ri-alert-color: var(--ri-success-text);
    --ri-alert-bg: var(--ri-success-bg-subtle);
    --ri-alert-border-color: var(--ri-success-border-subtle);
    --ri-alert-link-color: var(--ri-success-text)
}

.alert-info {
    --ri-alert-color: var(--ri-info-text);
    --ri-alert-bg: var(--ri-info-bg-subtle);
    --ri-alert-border-color: var(--ri-info-border-subtle);
    --ri-alert-link-color: var(--ri-info-text)
}

.alert-warning {
    --ri-alert-color: var(--ri-warning-text);
    --ri-alert-bg: var(--ri-warning-bg-subtle);
    --ri-alert-border-color: var(--ri-warning-border-subtle);
    --ri-alert-link-color: var(--ri-warning-text)
}

.alert-danger {
    --ri-alert-color: var(--ri-danger-text);
    --ri-alert-bg: var(--ri-danger-bg-subtle);
    --ri-alert-border-color: var(--ri-danger-border-subtle);
    --ri-alert-link-color: var(--ri-danger-text)
}

.alert-white {
    --ri-alert-color: var(--ri-white-text);
    --ri-alert-bg: var(--ri-white-bg-subtle);
    --ri-alert-border-color: var(--ri-white-border-subtle);
    --ri-alert-link-color: var(--ri-white-text)
}

.alert-light {
    --ri-alert-color: var(--ri-light-text);
    --ri-alert-bg: var(--ri-light-bg-subtle);
    --ri-alert-border-color: var(--ri-light-border-subtle);
    --ri-alert-link-color: var(--ri-light-text)
}

.alert-dark {
    --ri-alert-color: var(--ri-dark-text);
    --ri-alert-bg: var(--ri-dark-bg-subtle);
    --ri-alert-border-color: var(--ri-dark-border-subtle);
    --ri-alert-link-color: var(--ri-dark-text)
}


.fade {
    -webkit-transition: opacity .15s linear;
    transition: opacity .15s linear
}

.fade:not(.show) {
    opacity: 0
}

.collapse:not(.show) {
    display: none
}

.collapsing {
    height: 0;
    overflow: hidden;
    -webkit-transition: height .35s ease;
    transition: height .35s ease
}

.collapsing.collapse-horizontal {
    width: 0;
    height: auto;
    -webkit-transition: width .35s ease;
    transition: width .35s ease
}

.btn-group,.btn-group-vertical {
    position: relative;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    vertical-align: middle
}

.btn-group-vertical>.btn,.btn-group>.btn {
    position: relative;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto
}

.btn-group-vertical>.btn-check:checked+.btn,.btn-group-vertical>.btn-check:focus+.btn,.btn-group-vertical>.btn.active,.btn-group-vertical>.btn:active,.btn-group-vertical>.btn:focus,.btn-group-vertical>.btn:hover,.btn-group>.btn-check:checked+.btn,.btn-group>.btn-check:focus+.btn,.btn-group>.btn.active,.btn-group>.btn:active,.btn-group>.btn:focus,.btn-group>.btn:hover {
    z-index: 1
}

.btn-toolbar {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start
}

.btn-toolbar .input-group {
    width: auto
}

.btn-group {
    border-radius: .25rem
}

.btn-group>.btn-group:not(:first-child),.btn-group>:not(.btn-check:first-child)+.btn {
    margin-left: calc(var(--ri-border-width) * -1)
}

.btn-group>.btn-group:not(:last-child)>.btn,.btn-group>.btn.dropdown-toggle-split:first-child,.btn-group>.btn:not(:last-child):not(.dropdown-toggle) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.btn-group>.btn-group:not(:first-child)>.btn,.btn-group>.btn:nth-child(n+3),.btn-group>:not(.btn-check)+.btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.dropdown-toggle-split {
    padding-right: .5625rem;
    padding-left: .5625rem
}

.dropdown-toggle-split::after,.dropend .dropdown-toggle-split::after,.dropup .dropdown-toggle-split::after {
    margin-left: 0
}

.dropstart .dropdown-toggle-split::before {
    margin-right: 0
}

.btn-group-sm>.btn+.dropdown-toggle-split,.btn-sm+.dropdown-toggle-split {
    padding-right: .375rem;
    padding-left: .375rem
}

.btn-group-lg>.btn+.dropdown-toggle-split,.btn-lg+.dropdown-toggle-split {
    padding-right: .75rem;
    padding-left: .75rem
}

.btn-group-vertical {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.btn-group-vertical>.btn,.btn-group-vertical>.btn-group {
    width: 100%
}

.btn-group-vertical>.btn-group:not(:first-child),.btn-group-vertical>.btn:not(:first-child) {
    margin-top: calc(var(--ri-border-width) * -1)
}

.btn-group-vertical>.btn-group:not(:last-child)>.btn,.btn-group-vertical>.btn:not(:last-child):not(.dropdown-toggle) {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group-vertical>.btn-group:not(:first-child)>.btn,.btn-group-vertical>.btn~.btn {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.badge {
    --ri-badge-padding-x: 0.65em;
    --ri-badge-padding-y: 0.35em;
    --ri-badge-font-size: 0.85em;
    --ri-badge-font-weight: 400;
    
    --ri-badge-border-radius: 0.25rem;
    display: inline-block;
    padding: var(--ri-badge-padding-y) var(--ri-badge-padding-x);
    font-size: var(--ri-badge-font-size);
    font-weight: var(--ri-badge-font-weight);
    line-height: 1;
    color: var(--ri-badge-color);
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--ri-badge-border-radius)
}

.badge:empty {
    display: none
}

.btn .badge {
    position: relative;
    top: -1px
}

.spinner-border,.spinner-grow {
    display: inline-block;
    width: var(--ri-spinner-width);
    height: var(--ri-spinner-height);
    vertical-align: var(--ri-spinner-vertical-align);
    border-radius: 50%;
    -webkit-animation: var(--ri-spinner-animation-speed) linear infinite var(--ri-spinner-animation-name);
    animation: var(--ri-spinner-animation-speed) linear infinite var(--ri-spinner-animation-name)
}

.spinner-border {
    --ri-spinner-width: 2rem;
    --ri-spinner-height: 2rem;
    --ri-spinner-vertical-align: -0.125em;
    --ri-spinner-border-width: 0.25em;
    --ri-spinner-animation-speed: 0.75s;
    --ri-spinner-animation-name: spinner-border;
    border: var(--ri-spinner-border-width) solid currentcolor;
    border-right-color: transparent
}

.spinner-border-sm {
    --ri-spinner-width: 1rem;
    --ri-spinner-height: 1rem;
    --ri-spinner-border-width: 0.2em
}

.spinner-grow {
    --ri-spinner-width: 2rem;
    --ri-spinner-height: 2rem;
    --ri-spinner-vertical-align: -0.125em;
    --ri-spinner-animation-speed: 0.75s;
    --ri-spinner-animation-name: spinner-grow;
    background-color: currentcolor;
    opacity: 0
}

.spinner-grow-sm {
    --ri-spinner-width: 1rem;
    --ri-spinner-height: 1rem
}

.list-group {
    --ri-list-group-color: var(--ri-body-color);
    --ri-list-group-bg: var(--ri-body-bg);
    --ri-list-group-border-color: var(--ri-border-color);
    --ri-list-group-border-width: var(--ri-border-width);
    --ri-list-group-border-radius: var(--ri-border-radius);
    --ri-list-group-item-padding-x: 1rem;
    --ri-list-group-item-padding-y: 0.5rem;
    --ri-list-group-action-color: var(--ri-secondary-color);
    --ri-list-group-action-hover-color: var(--ri-emphasis-color);
    --ri-list-group-action-hover-bg: var(--ri-tertiary-bg);
    --ri-list-group-action-active-color: var(--ri-body-color);
    --ri-list-group-action-active-bg: var(--ri-secondary-bg);
    --ri-list-group-disabled-color: var(--ri-secondary-color);
    --ri-list-group-disabled-bg: var(--ri-body-bg);
    --ri-list-group-active-color: #fff;
    --ri-list-group-active-bg: #2163e8;
    --ri-list-group-active-border-color: #2163e8;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
    border-radius: var(--ri-list-group-border-radius)
}

.list-group-numbered {
    list-style-type: none;
    counter-reset: section
}

.list-group-numbered>.list-group-item::before {
    content: counters(section, ".") ". ";
    counter-increment: section
}

.list-group-item-action {
    width: 100%;
    color: var(--ri-list-group-action-color);
    text-align: inherit
}

.list-group-item-action:focus,.list-group-item-action:hover {
    z-index: 1;
    color: var(--ri-list-group-action-hover-color);
    text-decoration: none;
    background-color: var(--ri-list-group-action-hover-bg)
}

.list-group-item-action:active {
    color: var(--ri-list-group-action-active-color);
    background-color: var(--ri-list-group-action-active-bg)
}

.list-group-item {
    position: relative;
    display: block;
    padding: var(--ri-list-group-item-padding-y) var(--ri-list-group-item-padding-x);
    color: var(--ri-list-group-color);
    background-color: var(--ri-list-group-bg);
    border: var(--ri-list-group-border-width) solid var(--ri-list-group-border-color)
}

.list-group-item:first-child {
    border-top-left-radius: inherit;
    border-top-right-radius: inherit
}

.list-group-item:last-child {
    border-bottom-right-radius: inherit;
    border-bottom-left-radius: inherit
}

.list-group-item.disabled,.list-group-item:disabled {
    color: var(--ri-list-group-disabled-color);
    pointer-events: none;
    background-color: var(--ri-list-group-disabled-bg)
}

.list-group-item.active {
    z-index: 2;
    color: var(--ri-list-group-active-color);
    background-color: var(--ri-list-group-active-bg);
    border-color: var(--ri-list-group-active-border-color)
}

.list-group-item+.list-group-item {
    border-top-width: 0
}

.list-group-item+.list-group-item.active {
    margin-top: calc(-1 * var(--ri-list-group-border-width));
    border-top-width: var(--ri-list-group-border-width)
}

.list-group-horizontal {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row
}

.list-group-horizontal>.list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--ri-list-group-border-radius);
    border-top-right-radius: 0
}

.list-group-horizontal>.list-group-item:last-child:not(:first-child) {
    border-top-right-radius: var(--ri-list-group-border-radius);
    border-bottom-left-radius: 0
}

.list-group-horizontal>.list-group-item.active {
    margin-top: 0
}

.list-group-horizontal>.list-group-item+.list-group-item {
    border-top-width: var(--ri-list-group-border-width);
    border-left-width: 0
}

.list-group-horizontal>.list-group-item+.list-group-item.active {
    margin-left: calc(-1 * var(--ri-list-group-border-width));
    border-left-width: var(--ri-list-group-border-width)
}

.list-group-flush {
    border-radius: 0
}

.list-group-flush>.list-group-item {
    border-width: 0 0 var(--ri-list-group-border-width)
}

.list-group-flush>.list-group-item:last-child {
    border-bottom-width: 0
}

.list-group-item-primary {
    --ri-list-group-color: var(--ri-primary-text);
    --ri-list-group-bg: var(--ri-primary-bg-subtle);
    --ri-list-group-border-color: var(--ri-primary-border-subtle)
}

.list-group-item-primary.list-group-item-action:focus,.list-group-item-primary.list-group-item-action:hover {
    --ri-list-group-action-hover-color: var(--ri-emphasis-color);
    --ri-list-group-action-hover-bg: var(--ri-primary-border-subtle)
}

.list-group-item-primary.list-group-item-action:active {
    --ri-list-group-active-color: var(--ri-emphasis-color);
    --ri-list-group-active-bg: var(--ri-primary-text);
    --ri-list-group-active-border-color: var(--ri-primary-text)
}

.list-group-item-secondary {
    --ri-list-group-color: var(--ri-secondary-text);
    --ri-list-group-bg: var(--ri-secondary-bg-subtle);
    --ri-list-group-border-color: var(--ri-secondary-border-subtle)
}

.list-group-item-secondary.list-group-item-action:focus,.list-group-item-secondary.list-group-item-action:hover {
    --ri-list-group-action-hover-color: var(--ri-emphasis-color);
    --ri-list-group-action-hover-bg: var(--ri-secondary-border-subtle)
}

.list-group-item-secondary.list-group-item-action:active {
    --ri-list-group-active-color: var(--ri-emphasis-color);
    --ri-list-group-active-bg: var(--ri-secondary-text);
    --ri-list-group-active-border-color: var(--ri-secondary-text)
}

.list-group-item-success {
    --ri-list-group-color: var(--ri-success-text);
    --ri-list-group-bg: var(--ri-success-bg-subtle);
    --ri-list-group-border-color: var(--ri-success-border-subtle)
}

.list-group-item-success.list-group-item-action:focus,.list-group-item-success.list-group-item-action:hover {
    --ri-list-group-action-hover-color: var(--ri-emphasis-color);
    --ri-list-group-action-hover-bg: var(--ri-success-border-subtle)
}

.list-group-item-success.list-group-item-action:active {
    --ri-list-group-active-color: var(--ri-emphasis-color);
    --ri-list-group-active-bg: var(--ri-success-text);
    --ri-list-group-active-border-color: var(--ri-success-text)
}

.list-group-item-info {
    --ri-list-group-color: var(--ri-info-text);
    --ri-list-group-bg: var(--ri-info-bg-subtle);
    --ri-list-group-border-color: var(--ri-info-border-subtle)
}

.list-group-item-info.list-group-item-action:focus,.list-group-item-info.list-group-item-action:hover {
    --ri-list-group-action-hover-color: var(--ri-emphasis-color);
    --ri-list-group-action-hover-bg: var(--ri-info-border-subtle)
}

.list-group-item-info.list-group-item-action:active {
    --ri-list-group-active-color: var(--ri-emphasis-color);
    --ri-list-group-active-bg: var(--ri-info-text);
    --ri-list-group-active-border-color: var(--ri-info-text)
}

.list-group-item-warning {
    --ri-list-group-color: var(--ri-warning-text);
    --ri-list-group-bg: var(--ri-warning-bg-subtle);
    --ri-list-group-border-color: var(--ri-warning-border-subtle)
}

.list-group-item-warning.list-group-item-action:focus,.list-group-item-warning.list-group-item-action:hover {
    --ri-list-group-action-hover-color: var(--ri-emphasis-color);
    --ri-list-group-action-hover-bg: var(--ri-warning-border-subtle)
}

.list-group-item-warning.list-group-item-action:active {
    --ri-list-group-active-color: var(--ri-emphasis-color);
    --ri-list-group-active-bg: var(--ri-warning-text);
    --ri-list-group-active-border-color: var(--ri-warning-text)
}

.list-group-item-danger {
    --ri-list-group-color: var(--ri-danger-text);
    --ri-list-group-bg: var(--ri-danger-bg-subtle);
    --ri-list-group-border-color: var(--ri-danger-border-subtle)
}

.list-group-item-danger.list-group-item-action:focus,.list-group-item-danger.list-group-item-action:hover {
    --ri-list-group-action-hover-color: var(--ri-emphasis-color);
    --ri-list-group-action-hover-bg: var(--ri-danger-border-subtle)
}

.list-group-item-danger.list-group-item-action:active {
    --ri-list-group-active-color: var(--ri-emphasis-color);
    --ri-list-group-active-bg: var(--ri-danger-text);
    --ri-list-group-active-border-color: var(--ri-danger-text)
}

.list-group-item-white {
    --ri-list-group-color: var(--ri-white-text);
    --ri-list-group-bg: var(--ri-white-bg-subtle);
    --ri-list-group-border-color: var(--ri-white-border-subtle)
}

.list-group-item-white.list-group-item-action:focus,.list-group-item-white.list-group-item-action:hover {
    --ri-list-group-action-hover-color: var(--ri-emphasis-color);
    --ri-list-group-action-hover-bg: var(--ri-white-border-subtle)
}

.list-group-item-white.list-group-item-action:active {
    --ri-list-group-active-color: var(--ri-emphasis-color);
    --ri-list-group-active-bg: var(--ri-white-text);
    --ri-list-group-active-border-color: var(--ri-white-text)
}

.list-group-item-light {
    --ri-list-group-color: var(--ri-light-text);
    --ri-list-group-bg: var(--ri-light-bg-subtle);
    --ri-list-group-border-color: var(--ri-light-border-subtle)
}

.list-group-item-light.list-group-item-action:focus,.list-group-item-light.list-group-item-action:hover {
    --ri-list-group-action-hover-color: var(--ri-emphasis-color);
    --ri-list-group-action-hover-bg: var(--ri-light-border-subtle)
}

.list-group-item-light.list-group-item-action:active {
    --ri-list-group-active-color: var(--ri-emphasis-color);
    --ri-list-group-active-bg: var(--ri-light-text);
    --ri-list-group-active-border-color: var(--ri-light-text)
}

.list-group-item-dark {
    --ri-list-group-color: var(--ri-dark-text);
    --ri-list-group-bg: var(--ri-dark-bg-subtle);
    --ri-list-group-border-color: var(--ri-dark-border-subtle)
}

.list-group-item-dark.list-group-item-action:focus,.list-group-item-dark.list-group-item-action:hover {
    --ri-list-group-action-hover-color: var(--ri-emphasis-color);
    --ri-list-group-action-hover-bg: var(--ri-dark-border-subtle)
}

.list-group-item-dark.list-group-item-action:active {
    --ri-list-group-active-color: var(--ri-emphasis-color);
    --ri-list-group-active-bg: var(--ri-dark-text);
    --ri-list-group-active-border-color: var(--ri-dark-text)
}

.clearfix::after {
    display: block;
    clear: both;
    content: ""
}

.text-bg-primary {
    color: #fff!important;
    background-color: RGBA(33,99,232,var(--ri-bg-opacity,1))!important
}

.text-bg-secondary {
    color: #fff!important;
    background-color: RGBA(89,93,105,var(--ri-bg-opacity,1))!important
}

.text-bg-success {
    color: #fff!important;
    background-color: RGBA(12,188,135,var(--ri-bg-opacity,1))!important
}

.text-bg-info {
    color: #fff!important;
    background-color: RGBA(79,158,248,var(--ri-bg-opacity,1))!important
}

.text-bg-warning {
    color: #000!important;
    background-color: RGBA(247,195,46,var(--ri-bg-opacity,1))!important
}

.text-bg-danger {
    color: #fff!important;
    background-color: RGBA(214,41,62,var(--ri-bg-opacity,1))!important
}

.text-bg-white {
    color: #000!important;
    background-color: RGBA(255,255,255,var(--ri-bg-opacity,1))!important
}

.text-bg-light {
    color: #000!important;
    background-color: RGBA(247,248,249,var(--ri-bg-opacity,1))!important
}

.text-bg-dark {
    color: #fff!important;
    background-color: RGBA(25,26,31,var(--ri-bg-opacity,1))!important
}

.link-primary {
    color: #2163e8!important
}

.link-primary:focus,.link-primary:hover {
    color: #030a17!important
}

.link-secondary {
    color: #595d69!important
}

.link-secondary:focus,.link-secondary:hover {
    color: #09090b!important
}

.link-success {
    color: #0cbc87!important
}

.link-success:focus,.link-success:hover {
    color: #01130e!important
}

.link-info {
    color: #4f9ef8!important
}

.link-info:focus,.link-info:hover {
    color: #081019!important
}

.link-warning {
    color: #f7c32e!important
}

.link-warning:focus,.link-warning:hover {
    color: #fef9ea!important
}

.link-danger {
    color: #d6293e!important
}

.link-danger:focus,.link-danger:hover {
    color: #150406!important
}

.link-white {
    color: #fff!important
}

.link-white:focus,.link-white:hover {
    color: #fff!important
}

.link-light {
    color: #f7f8f9!important
}

.link-light:focus,.link-light:hover {
    color: #fefefe!important
}

.link-dark {
    color: #191a1f!important
}

.link-dark:focus,.link-dark:hover {
    color: #030303!important
}

.ratio {
    position: relative;
    width: 100%
}

.ratio::before {
    display: block;
    padding-top: var(--ri-aspect-ratio);
    content: ""
}

.ratio>* {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

.ratio-1x1 {
    --ri-aspect-ratio: 100%
}

.ratio-2x3 {
    --ri-aspect-ratio: 150%
}

.ratio-3x2 {
    --ri-aspect-ratio: 66.6666666667%
}

.ratio-3x4 {
    --ri-aspect-ratio: 133.3333333333%
}

.ratio-4x3 {
    --ri-aspect-ratio: 75%
}

.ratio-16x9 {
    --ri-aspect-ratio: 56.25%
}

.ratio-21x9 {
    --ri-aspect-ratio: 42.8571428571%
}

.fixed-top {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030
}

.fixed-bottom {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1030
}

.sticky-top {
    position: sticky;
    top: 0;
    z-index: 1020
}

.sticky-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020
}

.hstack {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-item-align: stretch;
    align-self: stretch
}

.vstack {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -ms-flex-item-align: stretch;
    align-self: stretch
}

.visually-hidden,.visually-hidden-focusable:not(:focus):not(:focus-within) {
    position: absolute!important;
    width: 1px!important;
    height: 1px!important;
    padding: 0!important;
    margin: -1px!important;
    overflow: hidden!important;
    clip: rect(0,0,0,0)!important;
    white-space: nowrap!important;
    border: 0!important
}

.stretched-link::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    content: ""
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.vr {
    display: inline-block;
    -ms-flex-item-align: stretch;
    align-self: stretch;
    width: 1px;
    min-height: 1em;
    background-color: currentcolor;
    opacity: .25
}

.align-baseline {
    vertical-align: baseline!important
}

.align-top {
    vertical-align: top!important
}

.align-middle {
    vertical-align: middle!important
}

.align-bottom {
    vertical-align: bottom!important
}

.align-text-bottom {
    vertical-align: text-bottom!important
}

.align-text-top {
    vertical-align: text-top!important
}

.float-start {
    float: left!important
}

.float-end {
    float: right!important
}

.float-none {
    float: none!important
}

.object-fit-contain {
    -o-object-fit: contain!important;
    object-fit: contain!important
}

.object-fit-cover {
    -o-object-fit: cover!important;
    object-fit: cover!important
}

.object-fit-fill {
    -o-object-fit: fill!important;
    object-fit: fill!important
}

.object-fit-scale {
    -o-object-fit: scale-down!important;
    object-fit: scale-down!important
}

.object-fit-none {
    -o-object-fit: none!important;
    object-fit: none!important
}

.opacity-0 {
    opacity: 0!important
}

.opacity-25 {
    opacity: .25!important
}

.opacity-50 {
    opacity: .5!important
}

.opacity-75 {
    opacity: .75!important
}

.opacity-100 {
    opacity: 1!important
}

.overflow-auto {
    overflow: auto!important
}

.overflow-hidden {
    overflow: hidden!important
}

.overflow-visible {
    overflow: visible!important
}

.overflow-scroll {
    overflow: scroll!important
}

.overflow-x-auto {
    overflow-x: auto!important
}

.overflow-x-hidden {
    overflow-x: hidden!important
}

.overflow-x-visible {
    overflow-x: visible!important
}

.overflow-x-scroll {
    overflow-x: scroll!important
}

.overflow-y-auto {
    overflow-y: auto!important
}

.overflow-y-hidden {
    overflow-y: hidden!important
}

.overflow-y-visible {
    overflow-y: visible!important
}

.overflow-y-scroll {
    overflow-y: scroll!important
}

.d-inline {
    display: inline!important
}

.d-inline-block {
    display: inline-block!important
}

.d-block {
    display: block!important
}

.d-grid {
    display: grid!important
}

.d-table {
    display: table!important
}

.d-table-row {
    display: table-row!important
}

.d-table-cell {
    display: table-cell!important
}

.d-flex {
    display: -webkit-box!important;
    display: -ms-flexbox!important;
    display: flex!important
}

.d-inline-flex {
    display: -webkit-inline-box!important;
    display: -ms-inline-flexbox!important;
    display: inline-flex!important
}

.d-none {
    display: none!important
}

.shadow {
    -webkit-box-shadow: 0 .125rem 1rem rgba(var(--ri-body-color-rgb),.075)!important;
    box-shadow: 0 .125rem 1rem rgba(var(--ri-body-color-rgb),.075)!important
}

.shadow-sm {
    -webkit-box-shadow: 0 .125rem .25rem rgba(var(--ri-body-color-rgb),.075)!important;
    box-shadow: 0 .125rem .25rem rgba(var(--ri-body-color-rgb),.075)!important
}

.shadow-lg {
    -webkit-box-shadow: 0 1rem 3rem rgba(var(--ri-body-color-rgb),.175)!important;
    box-shadow: 0 1rem 3rem rgba(var(--ri-body-color-rgb),.175)!important
}

.shadow-none {
    -webkit-box-shadow: none!important;
    box-shadow: none!important
}

.position-static {
    position: static!important
}

.position-relative {
    position: relative!important
}

.position-absolute {
    position: absolute!important
}

.position-fixed {
    position: fixed!important
}

.position-sticky {
    position: sticky!important
}

.top-0 {
    top: 0!important
}

.top-50 {
    top: 50%!important
}

.top-100 {
    top: 100%!important
}

.bottom-0 {
    bottom: 0!important
}

.bottom-50 {
    bottom: 50%!important
}

.bottom-100 {
    bottom: 100%!important
}

.start-0 {
    left: 0!important
}

.start-50 {
    left: 50%!important
}

.start-100 {
    left: 100%!important
}

.end-0 {
    right: 0!important
}

.end-50 {
    right: 50%!important
}

.end-100 {
    right: 100%!important
}

.translate-middle {
    -webkit-transform: translate(-50%,-50%)!important;
    transform: translate(-50%,-50%)!important
}

.translate-middle-x {
    -webkit-transform: translateX(-50%)!important;
    transform: translateX(-50%)!important
}

.translate-middle-y {
    -webkit-transform: translateY(-50%)!important;
    transform: translateY(-50%)!important
}

.border {
    border: var(--ri-border-width) var(--ri-border-style) var(--ri-border-color)!important
}

.border-0 {
    border: 0!important
}

.border-top {
    border-top: var(--ri-border-width) var(--ri-border-style) var(--ri-border-color)!important
}

.border-top-0 {
    border-top: 0!important
}

.border-end {
    border-right: var(--ri-border-width) var(--ri-border-style) var(--ri-border-color)!important
}

.border-end-0 {
    border-right: 0!important
}

.border-bottom {
    border-bottom: var(--ri-border-width) var(--ri-border-style) var(--ri-border-color)!important
}

.border-bottom-0 {
    border-bottom: 0!important
}

.border-start {
    border-left: var(--ri-border-width) var(--ri-border-style) var(--ri-border-color)!important
}

.border-start-0 {
    border-left: 0!important
}

.border-primary {
    --ri-border-opacity: 1;
    border-color: rgba(var(--ri-primary-rgb),var(--ri-border-opacity))!important
}

.border-secondary {
    --ri-border-opacity: 1;
    border-color: rgba(var(--ri-secondary-rgb),var(--ri-border-opacity))!important
}

.border-success {
    --ri-border-opacity: 1;
    border-color: rgba(var(--ri-success-rgb),var(--ri-border-opacity))!important
}

.border-info {
    --ri-border-opacity: 1;
    border-color: rgba(var(--ri-info-rgb),var(--ri-border-opacity))!important
}

.border-warning {
    --ri-border-opacity: 1;
    border-color: rgba(var(--ri-warning-rgb),var(--ri-border-opacity))!important
}

.border-danger {
    --ri-border-opacity: 1;
    border-color: rgba(var(--ri-danger-rgb),var(--ri-border-opacity))!important
}

.border-white {
    --ri-border-opacity: 1;
    border-color: rgba(var(--ri-white-rgb),var(--ri-border-opacity))!important
}

.border-light {
    --ri-border-opacity: 1;
    border-color: rgba(var(--ri-light-rgb),var(--ri-border-opacity))!important
}

.border-dark {
    --ri-border-opacity: 1;
    border-color: rgba(var(--ri-dark-rgb),var(--ri-border-opacity))!important
}

.border-primary-subtle {
    border-color: var(--ri-primary-border-subtle)!important
}

.border-secondary-subtle {
    border-color: var(--ri-secondary-border-subtle)!important
}

.border-success-subtle {
    border-color: var(--ri-success-border-subtle)!important
}

.border-info-subtle {
    border-color: var(--ri-info-border-subtle)!important
}

.border-warning-subtle {
    border-color: var(--ri-warning-border-subtle)!important
}

.border-danger-subtle {
    border-color: var(--ri-danger-border-subtle)!important
}

.border-light-subtle {
    border-color: var(--ri-light-border-subtle)!important
}

.border-dark-subtle {
    border-color: var(--ri-dark-border-subtle)!important
}

.border-1 {
    --ri-border-width: 1px
}

.border-2 {
    --ri-border-width: 2px
}

.border-3 {
    --ri-border-width: 3px
}

.border-4 {
    --ri-border-width: 4px
}

.border-5 {
    --ri-border-width: 5px
}

.border-opacity-10 {
    --ri-border-opacity: 0.1
}

.border-opacity-25 {
    --ri-border-opacity: 0.25
}

.border-opacity-50 {
    --ri-border-opacity: 0.5
}

.border-opacity-75 {
    --ri-border-opacity: 0.75
}

.border-opacity-100 {
    --ri-border-opacity: 1
}

.w-25 {
    width: 25%!important
}

.w-50 {
    width: 50%!important
}

.w-75 {
    width: 75%!important
}

.w-100 {
    width: 100%!important
}

.w-auto {
    width: auto!important
}

.mw-100 {
    max-width: 100%!important
}

.vw-100 {
    width: 100vw!important
}

.min-vw-100 {
    min-width: 100vw!important
}

.h-25 {
    height: 25%!important
}

.h-50 {
    height: 50%!important
}

.h-75 {
    height: 75%!important
}

.h-100 {
    height: 100%!important
}

.h-auto {
    height: auto!important
}

.mh-100 {
    max-height: 100%!important
}

.vh-100 {
    height: 100vh!important
}

.min-vh-100 {
    min-height: 100vh!important
}

.flex-fill {
    -webkit-box-flex: 1!important;
    -ms-flex: 1 1 auto!important;
    flex: 1 1 auto!important
}

.flex-row {
    -webkit-box-orient: horizontal!important;
    -webkit-box-direction: normal!important;
    -ms-flex-direction: row!important;
    flex-direction: row!important
}

.flex-column {
    -webkit-box-orient: vertical!important;
    -webkit-box-direction: normal!important;
    -ms-flex-direction: column!important;
    flex-direction: column!important
}

.flex-row-reverse {
    -webkit-box-orient: horizontal!important;
    -webkit-box-direction: reverse!important;
    -ms-flex-direction: row-reverse!important;
    flex-direction: row-reverse!important
}

.flex-column-reverse {
    -webkit-box-orient: vertical!important;
    -webkit-box-direction: reverse!important;
    -ms-flex-direction: column-reverse!important;
    flex-direction: column-reverse!important
}

.flex-grow-0 {
    -webkit-box-flex: 0!important;
    -ms-flex-positive: 0!important;
    flex-grow: 0!important
}

.flex-grow-1 {
    -webkit-box-flex: 1!important;
    -ms-flex-positive: 1!important;
    flex-grow: 1!important
}

.flex-shrink-0 {
    -ms-flex-negative: 0!important;
    flex-shrink: 0!important
}

.flex-shrink-1 {
    -ms-flex-negative: 1!important;
    flex-shrink: 1!important
}

.flex-wrap {
    -ms-flex-wrap: wrap!important;
    flex-wrap: wrap!important
}

.flex-nowrap {
    -ms-flex-wrap: nowrap!important;
    flex-wrap: nowrap!important
}

.flex-wrap-reverse {
    -ms-flex-wrap: wrap-reverse!important;
    flex-wrap: wrap-reverse!important
}

.justify-content-start {
    -webkit-box-pack: start!important;
    -ms-flex-pack: start!important;
    justify-content: flex-start!important
}

.justify-content-end {
    -webkit-box-pack: end!important;
    -ms-flex-pack: end!important;
    justify-content: flex-end!important
}

.justify-content-center {
    -webkit-box-pack: center!important;
    -ms-flex-pack: center!important;
    justify-content: center!important
}

.justify-content-between {
    -webkit-box-pack: justify!important;
    -ms-flex-pack: justify!important;
    justify-content: space-between!important
}

.justify-content-around {
    -ms-flex-pack: distribute!important;
    justify-content: space-around!important
}

.justify-content-evenly {
    -webkit-box-pack: space-evenly!important;
    -ms-flex-pack: space-evenly!important;
    justify-content: space-evenly!important
}

.align-items-start {
    -webkit-box-align: start!important;
    -ms-flex-align: start!important;
    align-items: flex-start!important
}

.align-items-end {
    -webkit-box-align: end!important;
    -ms-flex-align: end!important;
    align-items: flex-end!important
}

.align-items-center {
    -webkit-box-align: center!important;
    -ms-flex-align: center!important;
    align-items: center!important
}

.align-items-baseline {
    -webkit-box-align: baseline!important;
    -ms-flex-align: baseline!important;
    align-items: baseline!important
}

.align-items-stretch {
    -webkit-box-align: stretch!important;
    -ms-flex-align: stretch!important;
    align-items: stretch!important
}

.align-content-start {
    -ms-flex-line-pack: start!important;
    align-content: flex-start!important
}

.align-content-end {
    -ms-flex-line-pack: end!important;
    align-content: flex-end!important
}

.align-content-center {
    -ms-flex-line-pack: center!important;
    align-content: center!important
}

.align-content-between {
    -ms-flex-line-pack: justify!important;
    align-content: space-between!important
}

.align-content-around {
    -ms-flex-line-pack: distribute!important;
    align-content: space-around!important
}

.align-content-stretch {
    -ms-flex-line-pack: stretch!important;
    align-content: stretch!important
}

.align-self-auto {
    -ms-flex-item-align: auto!important;
    align-self: auto!important
}

.align-self-start {
    -ms-flex-item-align: start!important;
    align-self: flex-start!important
}

.align-self-end {
    -ms-flex-item-align: end!important;
    align-self: flex-end!important
}

.align-self-center {
    -ms-flex-item-align: center!important;
    align-self: center!important
}

.align-self-baseline {
    -ms-flex-item-align: baseline!important;
    align-self: baseline!important
}

.align-self-stretch {
    -ms-flex-item-align: stretch!important;
    align-self: stretch!important
}

.order-first {
    -webkit-box-ordinal-group: 0!important;
    -ms-flex-order: -1!important;
    order: -1!important
}

.order-0 {
    -webkit-box-ordinal-group: 1!important;
    -ms-flex-order: 0!important;
    order: 0!important
}

.order-1 {
    -webkit-box-ordinal-group: 2!important;
    -ms-flex-order: 1!important;
    order: 1!important
}

.order-2 {
    -webkit-box-ordinal-group: 3!important;
    -ms-flex-order: 2!important;
    order: 2!important
}

.order-3 {
    -webkit-box-ordinal-group: 4!important;
    -ms-flex-order: 3!important;
    order: 3!important
}

.order-4 {
    -webkit-box-ordinal-group: 5!important;
    -ms-flex-order: 4!important;
    order: 4!important
}

.order-5 {
    -webkit-box-ordinal-group: 6!important;
    -ms-flex-order: 5!important;
    order: 5!important
}

.order-last {
    -webkit-box-ordinal-group: 7!important;
    -ms-flex-order: 6!important;
    order: 6!important
}

.m-0 {
    margin: 0!important
}

.m-1 {
    margin: .25rem!important
}

.m-2 {
    margin: .5rem!important
}

.m-3 {
    margin: 1rem!important
}

.m-4 {
    margin: 1.5rem!important
}

.m-5 {
    margin: 3rem!important
}

.m-6 {
    margin: 4.5rem!important
}

.m-7 {
    margin: 6rem!important
}

.m-8 {
    margin: 7.5rem!important
}

.m-9 {
    margin: 9rem!important
}

.m-10 {
    margin: 10.5rem!important
}

.m-auto {
    margin: auto!important
}

.mx-0 {
    margin-right: 0!important;
    margin-left: 0!important
}

.mx-1 {
    margin-right: .25rem!important;
    margin-left: .25rem!important
}

.mx-2 {
    margin-right: .5rem!important;
    margin-left: .5rem!important
}

.mx-3 {
    margin-right: 1rem!important;
    margin-left: 1rem!important
}

.mx-4 {
    margin-right: 1.5rem!important;
    margin-left: 1.5rem!important
}

.mx-5 {
    margin-right: 3rem!important;
    margin-left: 3rem!important
}

.mx-6 {
    margin-right: 4.5rem!important;
    margin-left: 4.5rem!important
}

.mx-7 {
    margin-right: 6rem!important;
    margin-left: 6rem!important
}

.mx-8 {
    margin-right: 7.5rem!important;
    margin-left: 7.5rem!important
}

.mx-9 {
    margin-right: 9rem!important;
    margin-left: 9rem!important
}

.mx-10 {
    margin-right: 10.5rem!important;
    margin-left: 10.5rem!important
}

.mx-auto {
    margin-right: auto!important;
    margin-left: auto!important
}

.my-0 {
    margin-top: 0!important;
    margin-bottom: 0!important
}

.my-1 {
    margin-top: .25rem!important;
    margin-bottom: .25rem!important
}

.my-2 {
    margin-top: .5rem!important;
    margin-bottom: .5rem!important
}

.my-3 {
    margin-top: 1rem!important;
    margin-bottom: 1rem!important
}

.my-4 {
    margin-top: 1.5rem!important;
    margin-bottom: 1.5rem!important
}

.my-5 {
    margin-top: 3rem!important;
    margin-bottom: 3rem!important
}

.my-6 {
    margin-top: 4.5rem!important;
    margin-bottom: 4.5rem!important
}

.my-7 {
    margin-top: 6rem!important;
    margin-bottom: 6rem!important
}

.my-8 {
    margin-top: 7.5rem!important;
    margin-bottom: 7.5rem!important
}

.my-9 {
    margin-top: 9rem!important;
    margin-bottom: 9rem!important
}

.my-10 {
    margin-top: 10.5rem!important;
    margin-bottom: 10.5rem!important
}

.my-auto {
    margin-top: auto!important;
    margin-bottom: auto!important
}

.mt-0 {
    margin-top: 0!important
}

.mt-1 {
    margin-top: .25rem!important
}

.mt-2 {
    margin-top: .5rem!important
}

.mt-3 {
    margin-top: 1rem!important
}

.mt-3 a {
    color: #FF0000;
    }

.mt-4 {
    margin-top: 1.5rem!important
}

.mt-5 {
    margin-top: 3rem!important
}

.mt-6 {
    margin-top: 4.5rem!important
}

.mt-7 {
    margin-top: 6rem!important
}

.mt-8 {
    margin-top: 7.5rem!important
}

.mt-9 {
    margin-top: 9rem!important
}

.mt-10 {
    margin-top: 10.5rem!important
}

.mt-auto {
    margin-top: auto!important
}

.me-0 {
    margin-right: 0!important
}

.me-1 {
    margin-right: .25rem!important
}

.me-2 {
    margin-right: .5rem!important
}

.me-3 {
    margin-right: 1rem!important
}

.me-4 {
    margin-right: 1.5rem!important
}

.me-5 {
    margin-right: 3rem!important
}

.me-6 {
    margin-right: 4.5rem!important
}

.me-7 {
    margin-right: 6rem!important
}

.me-8 {
    margin-right: 7.5rem!important
}

.me-9 {
    margin-right: 9rem!important
}

.me-10 {
    margin-right: 10.5rem!important
}

.me-auto {
    margin-right: auto!important
}

.mb-0 {
    margin-bottom: 0!important
}

.mb-1 {
    margin-bottom: .25rem!important
}

.mb-2 {
    margin-bottom: .5rem!important
}

.mb-3 {
    margin-bottom: 1rem!important
}

.mb-4 {
    margin-bottom: 1.5rem!important
}

.mb-5 {
    margin-bottom: 3rem!important
}

.mb-6 {
    margin-bottom: 4.5rem!important
}

.mb-7 {
    margin-bottom: 6rem!important
}

.mb-8 {
    margin-bottom: 7.5rem!important
}

.mb-9 {
    margin-bottom: 9rem!important
}

.mb-10 {
    margin-bottom: 10.5rem!important
}

.mb-auto {
    margin-bottom: auto!important
}

.ms-0 {
    margin-left: 0!important
}

.ms-1 {
    margin-left: .25rem!important
}

.ms-2 {
    margin-left: .5rem!important
}

.ms-3 {
    margin-left: 1rem!important
}

.ms-4 {
    margin-left: 1.5rem!important
}

.ms-5 {
    margin-left: 3rem!important
}

.ms-6 {
    margin-left: 4.5rem!important
}

.ms-7 {
    margin-left: 6rem!important
}

.ms-8 {
    margin-left: 7.5rem!important
}

.ms-9 {
    margin-left: 9rem!important
}

.ms-10 {
    margin-left: 10.5rem!important
}

.ms-auto {
    margin-left: auto!important
}

.p-0 {
    padding: 0!important
}

.p-1 {
    padding: .25rem!important
}

.p-2 {
    padding: .5rem!important
}

.p-3 {
    padding: 1rem!important
}

.p-4 {
    padding: 1.5rem!important
}

.p-5 {
    padding: 3rem!important
}

.p-6 {
    padding: 4.5rem!important
}

.p-7 {
    padding: 6rem!important
}

.p-8 {
    padding: 7.5rem!important
}

.p-9 {
    padding: 9rem!important
}

.p-10 {
    padding: 10.5rem!important
}

.px-0 {
    padding-right: 0!important;
    padding-left: 0!important
}

.px-1 {
    padding-right: .25rem!important;
    padding-left: .25rem!important
}

.px-2 {
    padding-right: .5rem!important;
    padding-left: .5rem!important
}

.px-3 {
    padding-right: 1rem!important;
    padding-left: 1rem!important
}

.px-4 {
    padding-right: 1.5rem!important;
    padding-left: 1.5rem!important
}

.px-5 {
    padding-right: 3rem!important;
    padding-left: 3rem!important
}

.px-6 {
    padding-right: 4.5rem!important;
    padding-left: 4.5rem!important
}

.px-7 {
    padding-right: 6rem!important;
    padding-left: 6rem!important
}

.px-8 {
    padding-right: 7.5rem!important;
    padding-left: 7.5rem!important
}

.px-9 {
    padding-right: 9rem!important;
    padding-left: 9rem!important
}

.px-10 {
    padding-right: 10.5rem!important;
    padding-left: 10.5rem!important
}

.py-0 {
    padding-top: 0!important;
    padding-bottom: 0!important
}

.py-1 {
    padding-top: .25rem!important;
    padding-bottom: .25rem!important
}

.py-2 {
    padding-top: .5rem!important;
    padding-bottom: .5rem!important
}

.py-3 {
    padding-top: 1rem!important;
    padding-bottom: 1rem!important
}

.py-4 {
    padding-top: 1.5rem!important;
    padding-bottom: 1.5rem!important
}

.py-5 {
    padding-top: 3rem!important;
    padding-bottom: 3rem!important
}

.py-6 {
    padding-top: 4.5rem!important;
    padding-bottom: 4.5rem!important
}

.py-7 {
    padding-top: 6rem!important;
    padding-bottom: 6rem!important
}

.py-8 {
    padding-top: 7.5rem!important;
    padding-bottom: 7.5rem!important
}

.py-9 {
    padding-top: 9rem!important;
    padding-bottom: 9rem!important
}

.py-10 {
    padding-top: 10.5rem!important;
    padding-bottom: 10.5rem!important
}

.pt-0 {
    padding-top: 0!important
}

.pt-1 {
    padding-top: .25rem!important
}

.pt-2 {
    padding-top: .5rem!important
}

.pt-3 {
    padding-top: 1rem!important
}

.pt-4 {
    padding-top: 1.5rem!important
}

.pt-5 {
    padding-top: 3rem!important
}

.pt-6 {
    padding-top: 4.5rem!important
}

.pt-7 {
    padding-top: 6rem!important
}

.pt-8 {
    padding-top: 7.5rem!important
}

.pt-9 {
    padding-top: 9rem!important
}

.pt-10 {
    padding-top: 10.5rem!important
}

.pe-0 {
    padding-right: 0!important
}

.pe-1 {
    padding-right: .25rem!important
}

.pe-2 {
    padding-right: .5rem!important
}

.pe-3 {
    padding-right: 1rem!important
}

.pe-4 {
    padding-right: 1.5rem!important
}

.pe-5 {
    padding-right: 3rem!important
}

.pe-6 {
    padding-right: 4.5rem!important
}

.pe-7 {
    padding-right: 6rem!important
}

.pe-8 {
    padding-right: 7.5rem!important
}

.pe-9 {
    padding-right: 9rem!important
}

.pe-10 {
    padding-right: 10.5rem!important
}

.pb-0 {
    padding-bottom: 0!important
}

.pb-1 {
    padding-bottom: .25rem!important
}

.pb-2 {
    padding-bottom: .5rem!important
}

.pb-3 {
    padding-bottom: 1rem!important
}

.pb-4 {
    padding-bottom: 1.5rem!important
}

.pb-5 {
    padding-bottom: 3rem!important
}

.pb-6 {
    padding-bottom: 4.5rem!important
}

.pb-7 {
    padding-bottom: 6rem!important
}

.pb-8 {
    padding-bottom: 7.5rem!important
}

.pb-9 {
    padding-bottom: 9rem!important
}

.pb-10 {
    padding-bottom: 10.5rem!important
}

.ps-0 {
    padding-left: 0!important
}

.ps-1 {
    padding-left: .25rem!important
}

.ps-2 {
    padding-left: .5rem!important
}

.ps-3 {
    padding-left: 1rem!important
}

.ps-4 {
    padding-left: 1.5rem!important
}

.ps-5 {
    padding-left: 3rem!important
}

.ps-6 {
    padding-left: 4.5rem!important
}

.ps-7 {
    padding-left: 6rem!important
}

.ps-8 {
    padding-left: 7.5rem!important
}

.ps-9 {
    padding-left: 9rem!important
}

.ps-10 {
    padding-left: 10.5rem!important
}

.gap-0 {
    gap: 0!important
}

.gap-1 {
    gap: .25rem!important
}

.gap-2 {
    gap: .5rem!important
}

.gap-3 {
    gap: 1rem!important
}

.gap-4 {
    gap: 1.5rem!important
}

.gap-5 {
    gap: 3rem!important
}

.gap-6 {
    gap: 4.5rem!important
}

.gap-7 {
    gap: 6rem!important
}

.gap-8 {
    gap: 7.5rem!important
}

.gap-9 {
    gap: 9rem!important
}

.gap-10 {
    gap: 10.5rem!important
}

.row-gap-0 {
    row-gap: 0!important
}

.row-gap-1 {
    row-gap: .25rem!important
}

.row-gap-2 {
    row-gap: .5rem!important
}

.row-gap-3 {
    row-gap: 1rem!important
}

.row-gap-4 {
    row-gap: 1.5rem!important
}

.row-gap-5 {
    row-gap: 3rem!important
}

.row-gap-6 {
    row-gap: 4.5rem!important
}

.row-gap-7 {
    row-gap: 6rem!important
}

.row-gap-8 {
    row-gap: 7.5rem!important
}

.row-gap-9 {
    row-gap: 9rem!important
}

.row-gap-10 {
    row-gap: 10.5rem!important
}

.column-gap-0 {
    -webkit-column-gap: 0!important;
    -moz-column-gap: 0!important;
    column-gap: 0!important
}

.column-gap-1 {
    -webkit-column-gap: .25rem!important;
    -moz-column-gap: .25rem!important;
    column-gap: .25rem!important
}

.column-gap-2 {
    -webkit-column-gap: .5rem!important;
    -moz-column-gap: .5rem!important;
    column-gap: .5rem!important
}

.column-gap-3 {
    -webkit-column-gap: 1rem!important;
    -moz-column-gap: 1rem!important;
    column-gap: 1rem!important
}

.column-gap-4 {
    -webkit-column-gap: 1.5rem!important;
    -moz-column-gap: 1.5rem!important;
    column-gap: 1.5rem!important
}

.column-gap-5 {
    -webkit-column-gap: 3rem!important;
    -moz-column-gap: 3rem!important;
    column-gap: 3rem!important
}

.column-gap-6 {
    -webkit-column-gap: 4.5rem!important;
    -moz-column-gap: 4.5rem!important;
    column-gap: 4.5rem!important
}

.column-gap-7 {
    -webkit-column-gap: 6rem!important;
    -moz-column-gap: 6rem!important;
    column-gap: 6rem!important
}

.column-gap-8 {
    -webkit-column-gap: 7.5rem!important;
    -moz-column-gap: 7.5rem!important;
    column-gap: 7.5rem!important
}

.column-gap-9 {
    -webkit-column-gap: 9rem!important;
    -moz-column-gap: 9rem!important;
    column-gap: 9rem!important
}

.column-gap-10 {
    -webkit-column-gap: 10.5rem!important;
    -moz-column-gap: 10.5rem!important;
    column-gap: 10.5rem!important
}

.font-monospace {
    font-family: var(--ri-font-monospace)!important
}

.fs-1 {
    font-size: calc(1.359375rem + 1.3125vw)!important
}

.fs-2 {
    font-size: calc(1.3125rem + .75vw)!important
}

.fs-3 {
    font-size: calc(1.2890625rem + .46875vw)!important
}

.fs-4 {
    font-size: calc(1.265625rem + .1875vw)!important
}

.fs-5 {
    font-size: 1.171875rem!important
}

.fs-6 {
    font-size: .9375rem!important
}

.fst-italic {
    font-style: italic!important
}

.fst-normal {
    font-style: normal!important
}

.fw-lighter {
    font-weight: lighter!important
}

.fw-light {
    font-weight: 400!important
}

.fw-normal {
    font-weight: 500!important
}

.fw-medium {
    font-weight: 500!important
}

.fw-semibold {
    font-weight: 600!important
}

.fw-bold {
    font-weight: 700!important
}

.fw-bolder {
    font-weight: bolder!important
}

.lh-1 {
    line-height: 1!important
}

.lh-sm {
    line-height: 1.25!important
}

.lh-base {
    line-height: 1.5!important
}

.lh-lg {
    line-height: 2!important
}

.text-start {
    text-align: left!important
}

.text-end {
    text-align: right!important
}

.text-center {
    text-align: center!important
}

.text-decoration-none {
    text-decoration: none!important
}

.text-decoration-underline {
    text-decoration: underline!important
}

.text-decoration-line-through {
    text-decoration: line-through!important
}

.text-lowercase {
    text-transform: lowercase!important
}

.text-uppercase {
    text-transform: uppercase!important
}

.text-capitalize {
    text-transform: capitalize!important
}

.text-wrap {
    white-space: normal!important
}

.text-nowrap {
    white-space: nowrap!important
}

.text-break {
    word-wrap: break-word!important;
    word-break: break-word!important
}

.text-primary {
    --ri-text-opacity: 1;
    color: rgba(var(--ri-primary-rgb),var(--ri-text-opacity))!important
}

.text-secondary {
    --ri-text-opacity: 1;
    color: rgba(var(--ri-secondary-rgb),var(--ri-text-opacity))!important
}

.text-success {
    --ri-text-opacity: 1;
    color: rgba(var(--ri-success-rgb),var(--ri-text-opacity))!important
}

.text-info {
    --ri-text-opacity: 1;
    color: rgba(var(--ri-info-rgb),var(--ri-text-opacity))!important
}

.text-warning {
    --ri-text-opacity: 1;
    color: rgba(var(--ri-warning-rgb),var(--ri-text-opacity))!important
}

.text-danger {
    --ri-text-opacity: 1;
    color: rgba(var(--ri-danger-rgb),var(--ri-text-opacity))!important
}

.text-white {
    --ri-text-opacity: 1;
    color: rgba(var(--ri-white-rgb),var(--ri-text-opacity))!important
}

.text-light {
    --ri-text-opacity: 1;
    color: rgba(var(--ri-light-rgb),var(--ri-text-opacity))!important
}

.text-dark {
    --ri-text-opacity: 1;
    color: rgba(var(--ri-dark-rgb),var(--ri-text-opacity))!important
}

.text-black {
    --ri-text-opacity: 1;
    color: rgba(var(--ri-black-rgb),var(--ri-text-opacity))!important
}

.text-body {
    --ri-text-opacity: 1;
    color: rgba(var(--ri-body-color-rgb),var(--ri-text-opacity))!important
}

.text-muted {
    --ri-text-opacity: 1;
    color: var(--ri-secondary-color)!important
}

.text-black-50 {
    --ri-text-opacity: 1;
    color: rgba(0,0,0,.5)!important
}

.text-white-50 {
    --ri-text-opacity: 1;
    color: rgba(255,255,255,.5)!important
}

.text-body-secondary {
    --ri-text-opacity: 1;
    color: var(--ri-secondary-color)!important
}

.text-body-tertiary {
    --ri-text-opacity: 1;
    color: var(--ri-tertiary-color)!important
}

.text-body-emphasis {
    --ri-text-opacity: 1;
    color: var(--ri-emphasis-color)!important
}

.text-reset {
    --ri-text-opacity: 1;
    color: inherit!important
}

.text-opacity-25 {
    --ri-text-opacity: 0.25
}

.text-opacity-50 {
    --ri-text-opacity: 0.5
}

.text-opacity-75 {
    --ri-text-opacity: 0.75
}

.text-opacity-100 {
    --ri-text-opacity: 1
}

.text-primary-emphasis {
    color: var(--ri-primary-text)!important
}

.text-secondary-emphasis {
    color: var(--ri-secondary-text)!important
}

.text-success-emphasis {
    color: var(--ri-success-text)!important
}

.text-info-emphasis {
    color: var(--ri-info-text)!important
}

.text-warning-emphasis {
    color: var(--ri-warning-text)!important
}

.text-danger-emphasis {
    color: var(--ri-danger-text)!important
}

.text-light-emphasis {
    color: var(--ri-light-text)!important
}

.text-dark-emphasis {
    color: var(--ri-dark-text)!important
}

.bg-primary {
    --ri-bg-opacity: 1;
    background-color: rgba(var(--ri-primary-rgb),var(--ri-bg-opacity))!important
}

.bg-secondary {
    --ri-bg-opacity: 1;
    background-color: rgba(var(--ri-secondary-rgb),var(--ri-bg-opacity))!important
}

.bg-success {
    --ri-bg-opacity: 1;
    background-color: rgba(var(--ri-success-rgb),var(--ri-bg-opacity))!important
}

.bg-info {
    --ri-bg-opacity: 1;
    background-color: rgba(var(--ri-info-rgb),var(--ri-bg-opacity))!important
}

.bg-warning {
    --ri-bg-opacity: 1;
    background-color: rgba(var(--ri-warning-rgb),var(--ri-bg-opacity))!important
}

.bg-danger {
    --ri-bg-opacity: 1;
    background-color: rgba(var(--ri-danger-rgb),var(--ri-bg-opacity))!important
}

.bg-white {
    --ri-bg-opacity: 1;
    background-color: rgba(var(--ri-white-rgb),var(--ri-bg-opacity))!important
}

.bg-light {
    --ri-bg-opacity: 1;
    background-color: rgba(var(--ri-light-rgb),var(--ri-bg-opacity))!important
}

.bg-dark {
    --ri-bg-opacity: 1;
    background-color: rgba(var(--ri-dark-rgb),var(--ri-bg-opacity))!important
}

.bg-black {
    --ri-bg-opacity: 1;
    background-color: rgba(var(--ri-black-rgb),var(--ri-bg-opacity))!important
}

.bg-body {
    --ri-bg-opacity: 1;
    background-color: rgba(var(--ri-body-bg-rgb),var(--ri-bg-opacity))!important
}

.bg-transparent {
    --ri-bg-opacity: 1;
    background-color: transparent!important
}

.bg-body-secondary {
    --ri-bg-opacity: 1;
    background-color: rgba(var(--ri-secondary-bg-rgb),var(--ri-bg-opacity))!important
}

.bg-body-tertiary {
    --ri-bg-opacity: 1;
    background-color: rgba(var(--ri-tertiary-bg-rgb),var(--ri-bg-opacity))!important
}

.bg-body-emphasis {
    --ri-bg-opacity: 1;
    background-color: rgba(var(--ri-emphasis-bg-rgb),var(--ri-bg-opacity))!important
}

.bg-opacity-10 {
    --ri-bg-opacity: 0.1
}

.bg-opacity-25 {
    --ri-bg-opacity: 0.25
}

.bg-opacity-50 {
    --ri-bg-opacity: 0.5
}

.bg-opacity-75 {
    --ri-bg-opacity: 0.75
}

.bg-opacity-100 {
    --ri-bg-opacity: 1
}

.bg-primary-subtle {
    background-color: var(--ri-primary-bg-subtle)!important
}

.bg-secondary-subtle {
    background-color: var(--ri-secondary-bg-subtle)!important
}

.bg-success-subtle {
    background-color: var(--ri-success-bg-subtle)!important
}

.bg-info-subtle {
    background-color: var(--ri-info-bg-subtle)!important
}

.bg-warning-subtle {
    background-color: var(--ri-warning-bg-subtle)!important
}

.bg-danger-subtle {
    background-color: var(--ri-danger-bg-subtle)!important
}

.bg-light-subtle {
    background-color: var(--ri-light-bg-subtle)!important
}

.bg-dark-subtle {
    background-color: var(--ri-dark-bg-subtle)!important
}

.bg-gradient {
    background-image: var(--ri-gradient)!important
}

.user-select-all {
    -webkit-user-select: all!important;
    -moz-user-select: all!important;
    -ms-user-select: all!important;
    user-select: all!important
}

.user-select-auto {
    -webkit-user-select: auto!important;
    -moz-user-select: auto!important;
    -ms-user-select: auto!important;
    user-select: auto!important
}

.user-select-none {
    -webkit-user-select: none!important;
    -moz-user-select: none!important;
    -ms-user-select: none!important;
    user-select: none!important
}

.pe-none {
    pointer-events: none!important
}

.pe-auto {
    pointer-events: auto!important
}

.rounded {
    border-radius: var(--ri-border-radius)!important
}

.rounded-0 {
    border-radius: 0!important
}

.rounded-1 {
    border-radius: var(--ri-border-radius-sm)!important
}

.rounded-2 {
    border-radius: var(--ri-border-radius)!important
}

.rounded-3 {
    border-radius: var(--ri-border-radius-lg)!important
}

.rounded-4 {
    border-radius: var(--ri-border-radius-xl)!important
}

.rounded-5 {
    border-radius: var(--ri-border-radius-2xl)!important
}

.rounded-circle {
    border-radius: 50%!important
}

.rounded-pill {
    border-radius: var(--ri-border-radius-pill)!important
}

.rounded-top {
    border-top-left-radius: var(--ri-border-radius)!important;
    border-top-right-radius: var(--ri-border-radius)!important
}

.rounded-top-0 {
    border-top-left-radius: 0!important;
    border-top-right-radius: 0!important
}

.rounded-top-1 {
    border-top-left-radius: var(--ri-border-radius-sm)!important;
    border-top-right-radius: var(--ri-border-radius-sm)!important
}

.rounded-top-2 {
    border-top-left-radius: var(--ri-border-radius)!important;
    border-top-right-radius: var(--ri-border-radius)!important
}

.rounded-top-3 {
    border-top-left-radius: var(--ri-border-radius-lg)!important;
    border-top-right-radius: var(--ri-border-radius-lg)!important
}

.rounded-top-4 {
    border-top-left-radius: var(--ri-border-radius-xl)!important;
    border-top-right-radius: var(--ri-border-radius-xl)!important
}

.rounded-top-5 {
    border-top-left-radius: var(--ri-border-radius-2xl)!important;
    border-top-right-radius: var(--ri-border-radius-2xl)!important
}

.rounded-top-circle {
    border-top-left-radius: 50%!important;
    border-top-right-radius: 50%!important
}

.rounded-top-pill {
    border-top-left-radius: var(--ri-border-radius-pill)!important;
    border-top-right-radius: var(--ri-border-radius-pill)!important
}

.rounded-end {
    border-top-right-radius: var(--ri-border-radius)!important;
    border-bottom-right-radius: var(--ri-border-radius)!important
}

.rounded-end-0 {
    border-top-right-radius: 0!important;
    border-bottom-right-radius: 0!important
}

.rounded-end-1 {
    border-top-right-radius: var(--ri-border-radius-sm)!important;
    border-bottom-right-radius: var(--ri-border-radius-sm)!important
}

.rounded-end-2 {
    border-top-right-radius: var(--ri-border-radius)!important;
    border-bottom-right-radius: var(--ri-border-radius)!important
}

.rounded-end-3 {
    border-top-right-radius: var(--ri-border-radius-lg)!important;
    border-bottom-right-radius: var(--ri-border-radius-lg)!important
}

.rounded-end-4 {
    border-top-right-radius: var(--ri-border-radius-xl)!important;
    border-bottom-right-radius: var(--ri-border-radius-xl)!important
}

.rounded-end-5 {
    border-top-right-radius: var(--ri-border-radius-2xl)!important;
    border-bottom-right-radius: var(--ri-border-radius-2xl)!important
}

.rounded-end-circle {
    border-top-right-radius: 50%!important;
    border-bottom-right-radius: 50%!important
}

.rounded-end-pill {
    border-top-right-radius: var(--ri-border-radius-pill)!important;
    border-bottom-right-radius: var(--ri-border-radius-pill)!important
}

.rounded-bottom {
    border-bottom-right-radius: var(--ri-border-radius)!important;
    border-bottom-left-radius: var(--ri-border-radius)!important
}

.rounded-bottom-0 {
    border-bottom-right-radius: 0!important;
    border-bottom-left-radius: 0!important
}

.rounded-bottom-1 {
    border-bottom-right-radius: var(--ri-border-radius-sm)!important;
    border-bottom-left-radius: var(--ri-border-radius-sm)!important
}

.rounded-bottom-2 {
    border-bottom-right-radius: var(--ri-border-radius)!important;
    border-bottom-left-radius: var(--ri-border-radius)!important
}

.rounded-bottom-3 {
    border-bottom-right-radius: var(--ri-border-radius-lg)!important;
    border-bottom-left-radius: var(--ri-border-radius-lg)!important
}

.rounded-bottom-4 {
    border-bottom-right-radius: var(--ri-border-radius-xl)!important;
    border-bottom-left-radius: var(--ri-border-radius-xl)!important
}

.rounded-bottom-5 {
    border-bottom-right-radius: var(--ri-border-radius-2xl)!important;
    border-bottom-left-radius: var(--ri-border-radius-2xl)!important
}

.rounded-bottom-circle {
    border-bottom-right-radius: 50%!important;
    border-bottom-left-radius: 50%!important
}

.rounded-bottom-pill {
    border-bottom-right-radius: var(--ri-border-radius-pill)!important;
    border-bottom-left-radius: var(--ri-border-radius-pill)!important
}

.rounded-start {
    border-bottom-left-radius: var(--ri-border-radius)!important;
    border-top-left-radius: var(--ri-border-radius)!important
}

.rounded-start-0 {
    border-bottom-left-radius: 0!important;
    border-top-left-radius: 0!important
}

.rounded-start-1 {
    border-bottom-left-radius: var(--ri-border-radius-sm)!important;
    border-top-left-radius: var(--ri-border-radius-sm)!important
}

.rounded-start-2 {
    border-bottom-left-radius: var(--ri-border-radius)!important;
    border-top-left-radius: var(--ri-border-radius)!important
}

.rounded-start-3 {
    border-bottom-left-radius: var(--ri-border-radius-lg)!important;
    border-top-left-radius: var(--ri-border-radius-lg)!important
}

.rounded-start-4 {
    border-bottom-left-radius: var(--ri-border-radius-xl)!important;
    border-top-left-radius: var(--ri-border-radius-xl)!important
}

.rounded-start-5 {
    border-bottom-left-radius: var(--ri-border-radius-2xl)!important;
    border-top-left-radius: var(--ri-border-radius-2xl)!important
}

.rounded-start-circle {
    border-bottom-left-radius: 50%!important;
    border-top-left-radius: 50%!important
}

.rounded-start-pill {
    border-bottom-left-radius: var(--ri-border-radius-pill)!important;
    border-top-left-radius: var(--ri-border-radius-pill)!important
}

.visible {
    visibility: visible!important
}

.invisible {
    visibility: hidden!important
}

.z-n1 {
    z-index: -1!important
}

.z-0 {
    z-index: 0!important
}

.z-1 {
    z-index: 1!important
}

.z-2 {
    z-index: 2!important
}

.z-3 {
    z-index: 3!important
}

.btn-round {
    height: 40px;
    width: 40px;
    line-height: 40px;
    border-radius: 50%;
    padding: 0;
    text-align: center
}

.btn-round i {
    margin: 0
}

.btn-round .fa-play {
    padding-left: 3px
}

.btn-group-lg>.btn-round.btn,.btn-round.btn-lg {
    height: 55px;
    width: 55px;
    line-height: 55px
}

.btn-group-sm>.btn-round.btn,.btn-round.btn-sm {
    height: 32px;
    width: 32px;
    line-height: 30px
}

.btn-group-sm>.btn-round.btn i,.btn-round.btn-sm i {
    font-size: .6rem
}

.btn-primary-soft {
    color: #2163e8;
    background-color: rgba(33,99,232,.1)
}

.btn-primary-soft:active,.btn-primary-soft:hover {
    color: #fff;
    background-color: #2163e8;
    border-color: #2163e8
}

.btn-primary-soft:focus {
    color: #2163e8;
    background-color: rgba(33,99,232,.1);
    border-color: transparent
}

.btn-secondary-soft {
    color: #595d69;
    background-color: rgba(89,93,105,.1)
}

.btn-secondary-soft:active,.btn-secondary-soft:hover {
    color: #fff;
    background-color: #595d69;
    border-color: #595d69
}

.btn-secondary-soft:focus {
    color: #595d69;
    background-color: rgba(89,93,105,.1);
    border-color: transparent
}

.btn-success-soft {
    color: #0cbc87;
    background-color: rgba(12,188,135,.1)
}

.btn-success-soft:active,.btn-success-soft:hover {
    color: #fff;
    background-color: #0cbc87;
    border-color: #0cbc87
}

.btn-success-soft:focus {
    color: #0cbc87;
    background-color: rgba(12,188,135,.1);
    border-color: transparent
}

.btn-info-soft {
    color: #4f9ef8;
    background-color: rgba(79,158,248,.1)
}

.btn-info-soft:active,.btn-info-soft:hover {
    color: #fff;
    background-color: #4f9ef8;
    border-color: #4f9ef8
}

.btn-info-soft:focus {
    color: #4f9ef8;
    background-color: rgba(79,158,248,.1);
    border-color: transparent
}

.btn-warning-soft {
    color: #f7c32e;
    background-color: rgba(247,195,46,.1)
}

.btn-warning-soft:active,.btn-warning-soft:hover {
    color: #000;
    background-color: #f7c32e;
    border-color: #f7c32e
}

.btn-warning-soft:focus {
    color: #f7c32e;
    background-color: rgba(247,195,46,.1);
    border-color: transparent
}

.btn-danger-soft {
    color: #d6293e;
    background-color: rgba(214,41,62,.1)
}

.btn-danger-soft:active,.btn-danger-soft:hover {
    color: #fff;
    background-color: #d6293e;
    border-color: #d6293e
}

.btn-danger-soft:focus {
    color: #d6293e;
    background-color: rgba(214,41,62,.1);
    border-color: transparent
}

.btn-white-soft {
    color: #fff;
    background-color: rgba(255,255,255,.1)
}

.btn-white-soft:active,.btn-white-soft:hover {
    color: #000;
    background-color: #fff;
    border-color: #fff
}

.btn-white-soft:focus {
    color: #fff;
    background-color: rgba(255,255,255,.1);
    border-color: transparent
}

.btn-light-soft {
    color: #f7f8f9;
    background-color: rgba(247,248,249,.1)
}

.btn-light-soft:active,.btn-light-soft:hover {
    color: #000;
    background-color: #f7f8f9;
    border-color: #f7f8f9
}

.btn-light-soft:focus {
    color: #f7f8f9;
    background-color: rgba(247,248,249,.1);
    border-color: transparent
}

.btn-dark-soft {
    color: #191a1f;
    background-color: rgba(25,26,31,.1)
}

.btn-dark-soft:active,.btn-dark-soft:hover {
    color: #fff;
    background-color: #191a1f;
    border-color: #191a1f
}

.btn-dark-soft:focus {
    color: #191a1f;
    background-color: rgba(25,26,31,.1);
    border-color: transparent
}

.btn-more i {
    -webkit-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out
}

.btn-more[aria-expanded=true] i {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    -webkit-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out;
    -webkit-transform-origin: center;
    transform-origin: center
}

.btn-more .see-more {
    display: block
}

.btn-more .see-less {
    display: none
}

.btn-more[aria-expanded=true] .see-more {
    display: none
}

.btn-more[aria-expanded=true] .see-less {
    display: block
}

a.badge:hover {
    color: var(--ri-white)
}

body.wpdie {
    background-color: #3a3a3a
}

body.wpdie .dimmer {
    background: 0 0
}

.ri-notice {
    position: fixed;
    border-radius: 4px;
    padding: 16px;
    max-width: 400px;
    top: 160px;
    left: 50%;
    -webkit-transform: translate(-50%,-100%);
    transform: translate(-50%,-100%);
    background: rgba(0,0,0,.8);
    color: #fff;
    text-align: center;
    z-index: 10001
}

.ri-popup {
    opacity: 0;
    visibility: hidden;
    position: fixed;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    z-index: 999;
    overflow-x: hidden;
    overflow-y: auto
}

.ri-popup-body {
    opacity: 0;
    visibility: hidden;
    position: relative;
    width: 100%;
    max-width: 420px;
    padding-top: 2rem;
    padding-bottom: 2rem;
    -webkit-transform: translateX(0) translateY(-40px) scale(.98);
    transform: translateX(0) translateY(-40px) scale(.98);
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ri-popup-close {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 99;
    text-align: center;
    -webkit-transform: translateY(25%);
    transform: translateY(25%)
}

.ri-popup-close .svg-close {
    display: inline-block;
    cursor: pointer;
    background-image: url(data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCI+PGRlZnM+PHN0eWxlLz48L2RlZnM+PHBhdGggZD0iTTUxMiAwQzIyOS4yIDAgMCAyMjkuMTk5OTk5IDAgNTEyYzAgMjgyLjggMjI5LjIgNTEyIDUxMiA1MTIgMjgyLjgwMDAwMSAwIDUxMi0yMjkuMiA1MTItNTEyQzEwMjQgMjI5LjE5OTk5OSA3OTQuODAwMDAxIDAgNTEyIDB6bTIxMy40NjA1MjIgNzI1Ljk3NTY1OWMtNi4wNzcyNDkgNi4wNzcyNDgtMTQuMDY0NDg4IDkuMTE1ODcyLTIyLjA1MTcyNyA5LjExNTg3Mi03Ljk4NzI0MiAwLTE2LjA2MTI5OC0zLjAzODYyNC0yMi4xMzg1NDgtOS4yMDI2OUw1MTIgNTU2LjI3MTMyMyAzNDIuNzI5NzUyIDcyNS44ODg4NDFjLTYuMDc3MjQ4IDYuMTY0MDY2LTE0LjE1MTMwNiA5LjIwMjY5LTIyLjEzODU0NiA5LjIwMjY5LTcuOTg3MjQgMC0xNS45NzQ0NzktMy4wMzg2MjQtMjIuMDUxNzI3LTkuMTE1ODcyLTEyLjI0MTMxMy0xMi4xNTQ0OTYtMTIuMjQxMzEzLTMxLjk0ODk2LS4wODY4MTgtNDQuMTkwMjczTDQ2Ny44OTY1NDYgNTExLjk5NDIzIDI5OC40NTI2NjEgMzQyLjIwMzA3NGMtMTIuMTU0NDk1LTEyLjI0MTMxMi0xMi4xNTQ0OTUtMzIuMDM1Nzc2LjA4NjgxOC00NC4xOTAyNzEgMTIuMjQxMzEzLTEyLjE1NDQ5NSAzMi4wMzU3NzctMTIuMTU0NDk1IDQ0LjE5MDI3My4wODY4MTdMNTEyIDQ2Ny43MTcxNDFsMTY5LjI3MDI0Ny0xNjkuNzA0MzM4YzEyLjE1NDQ5NS0xMi4yNDEzMTMgMzEuOTQ4OTU5LTEyLjI0MTMxMyA0NC4xOTAyNzUtLjA4NjgxOCAxMi4yNDEzMTMgMTIuMTU0NDk1IDEyLjI0MTMxMyAzMS45NDg5NTkuMDg2ODE4IDQ0LjE5MDI3Mkw1NTYuMTAzNDU0IDUxMS45OTQyMyA3MjUuNTQ3MzQgNjgxLjc4NTM4NmMxMi4xNTQ0OTUgMTIuMjQxMzEzIDEyLjE1NDQ5NSAzMi4wMzU3NzctLjA4NjgxOCA0NC4xOTAyNzN6IiBmaWxsPSIjZmZmIi8+PC9zdmc+);
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    vertical-align: middle;
    width: 28px;
    height: 28px;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ri-popup-close .svg-close:hover {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

.ri-popup-content {
    background: #fff;
    padding: 1.25rem;
    position: relative;
    border-radius: 6px;
    border-radius: .5rem;
    overflow: hidden
}

.ri-popup-open {
    overflow: hidden
}

.ri-popup-open .ri-popup {
    opacity: 1;
    visibility: visible
}

.ri-popup-open .ri-popup-body {
    -webkit-transform: translateX(0) translateY(0) scale(1);
    transform: translateX(0) translateY(0) scale(1);
    pointer-events: auto;
    opacity: 1;
    visibility: visible
}

.pay-select-head {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: relative;
    margin-bottom: 1rem;
    font-weight: 600;
    color: #ff9800;
    font-size: 1rem;
    padding: 5px 15px;
    background: rgba(247,195,46,.15);
    border-radius: .3rem
}

.pay-select-head .pay-cdk-action {
    color: #ef7269;
    cursor: pointer;
    font-size: 13px;
    font-weight: 400
}

.pay-select-cdk {
    margin-bottom: 1rem
}

.pay-select-cdk>input {
    border: 1px dashed #ff9800;
    text-align: center;
    color: #2196f3;
    border-radius: 20px
}

.pay-select-cdk>input:focus {
    -webkit-box-shadow: none;
    box-shadow: none
}

.pay-select-box {
    position: relative
}

.pay-select-box .pay-item {
    width: 100%;
    cursor: pointer;
    font-size: 1rem;
    display: inline-block;
    text-align: center;
    position: relative;
    -webkit-transition: all .3s ease;
    transition: all .3s ease;
    border-radius: .5rem;
    border: solid 2px #dfdfe3;
    padding: .4rem
}

.pay-select-box .pay-item:hover {
    color: #0cbc87;
    border-color: #0cbc87
}

.pay-select-box .pay-item+.pay-item {
    margin-top: .5rem
}

.pay-select-box .pay-item>span {
    display: inline-block;
    text-align: center;
    max-width: 120px;
    vertical-align: middle;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-left: 10px
}

.pay-select-box .pay-item>i {
    display: inline-block;
    width: 30px;
    height: 30px;
    line-height: 30px;
    vertical-align: middle;
    background-size: contain!important
}

.pay-select-box .pay-item>i.alipay,.pay-select-box .pay-item>i.epay_alipay,.pay-select-box .pay-item>i.hupijiao_alipay,.pay-select-box .pay-item>i.xunhu_alipay {
    background: url(../img/alipay-i.png) center no-repeat
}

.pay-select-box .pay-item>i.epay_weixin,.pay-select-box .pay-item>i.hupijiao_weixin,.pay-select-box .pay-item>i.weixinpay,.pay-select-box .pay-item>i.xunhu_weixin {
    background: url(../img/weixinpay-i.png) center no-repeat
}

.pay-select-box .pay-item>i.paypal {
    background: url(../img/paypal-i.png) center no-repeat
}

.pay-select-box .pay-item>i.site_coin_pay {
    background: url(../img/coinpay-i.png) center no-repeat
}

.sale-amount {
    background: #ff5722;
    color: #fff;
    text-align: center;
    font-size: 13px;
    margin-top: -20px;
    display: block;
    width: 100%;
    border-radius: 0 0 20px 20px;
    padding: 2px;
    margin-bottom: 10px
}

.pay-body-html {
    position: relative;
    text-align: center
}

.pay-body-html .pay-icon {
    max-width: 120px;
    margin-bottom: 20px
}

.pay-body-html .title {
    margin-bottom: 10px
}

.pay-body-html .qrcode {
    margin-bottom: 10px
}

.pay-body-html .qrcode>img {
    width: 200px
}

.pay-body-html .desc {
    font-size: 12px;
    color: #000;
    border-top: dashed 2px #fff;
    margin: -1.25rem;
    margin-top: 0;
    padding: 1.25rem;
    background: rgba(112,112,112,.15)
}

.site-notify-body {
    position: relative
}

.site-notify-body .notify-title {
    text-align: center;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: .5rem
}

.site-notify-body .notify-desc {
    padding: 0
}

.navbar2-full .site-header {
    padding: 0 30px
}

.header-transparent .site-header:not(.navbar2-sticky) {
    background-color: rgba(0 0 0 /100%);
    -webkit-box-shadow: none!important;
    box-shadow: none!important;
    color: #FFF!important
}

.header-transparent .site-header:not(.navbar2-sticky) .navbar2 .action-hover-menu .avatar-warp,.header-transparent .site-header:not(.navbar2-sticky) .navbar2 .actions .action-btn,.header-transparent .site-header:not(.navbar2-sticky) .navbar2 .nav-list>.menu-item>a {
    color: #fff!important
}

.header-transparent .header-gap {
    height: 0
}

.header-transparent main>.home-widget:first-child.home-search-box .search-bg {
    padding-top: 60px
}

.site-header {
    -webkit-backface-visibility: hidden;
    background-color: #000;
    color: #4d4d4d;
    -webkit-box-shadow: 0 0 30px rgba(0,0,0,.07);
    box-shadow: 0 0 30px rgba(0,0,0,.07);
  
    -webkit-transition: background-color .3s cubic-bezier(.77,0,.175,1),-webkit-box-shadow .3s cubic-bezier(.77,0,.175,1),-webkit-transform .3s cubic-bezier(.77,0,.175,1);
    transition: background-color .3s cubic-bezier(.77,0,.175,1),-webkit-box-shadow .3s cubic-bezier(.77,0,.175,1),-webkit-transform .3s cubic-bezier(.77,0,.175,1);
    transition: background-color .3s cubic-bezier(.77,0,.175,1),box-shadow .3s cubic-bezier(.77,0,.175,1),transform .3s cubic-bezier(.77,0,.175,1);
    transition: background-color .3s cubic-bezier(.77,0,.175,1),box-shadow .3s cubic-bezier(.77,0,.175,1),transform .3s cubic-bezier(.77,0,.175,1),-webkit-box-shadow .3s cubic-bezier(.77,0,.175,1),-webkit-transform .3s cubic-bezier(.77,0,.175,1);
    z-index: 80
}

.site-header.navbar2-now {
    -webkit-box-shadow: none;
    box-shadow: none;
    -webkit-transform: translateY(-80px);
    transform: translateY(-80px);
    -webkit-transition: background-color .3s ease-out,-webkit-transform .3s;
    transition: background-color .3s ease-out,-webkit-transform .3s;
    transition: transform .3s,background-color .3s ease-out;
    transition: transform .3s,background-color .3s ease-out,-webkit-transform .3s
}

.site-header.navbar2-sticky {
    position: fixed!important
}

.header-gap {
    height: 60px
}

.navbar2 {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 66px;
    position: relative
}

.navbar2 .logo {
    margin-right: 12px
}

.navbar2 .sep {
    display: none;
    height: 100%;
    margin: 0 20px;
    width: 1px
}

.navbar2 .sep:after {
    background-color: rgba(25,26,31,.1);
    content: "";
    height: 50%;
    margin: auto;
    -webkit-transition: background-color .5s cubic-bezier(.77,0,.175,1);
    transition: background-color .5s cubic-bezier(.77,0,.175,1);
    width: 100%
}

.navbar2 .nav-list {
    margin: 0;
    padding: 0;
    white-space: nowrap
}

.navbar2 .nav-list a {
    color: #4d4d4d
}

.navbar2 .nav-list a:hover {
    color: rgba(77,77,77,.85)
}

.navbar2 .nav-list .menu-item {
    display: inline-block;
    position: relative
}

.navbar2 .nav-list .menu-item>a {
    display: block
}

.navbar2 .nav-list .menu-item>a .emoji {
    margin-left: 0!important;
    margin-right: 5px!important
}

.navbar2 .nav-list .menu-item.menu-item-has-children>a:after {
    content: "\f107";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    margin-left: 4px
}

.navbar2 .nav-list .menu-item:hover>.sub-menu {
    opacity: 1;
    pointer-events: auto;
    -webkit-transform: translate(-50%,0) scale(1);
    transform: translate(-50%,0) scale(1);
    visibility: visible;
    -webkit-transition: all .25s ease-in-out;
    transition: all .25s ease-in-out
}

.navbar2 .nav-list>.menu-item>a {
    font-size: 1rem;
    line-height: 61px;
    margin: 0 10px
}

.navbar2 .nav-list>.menu-item.current-menu-item>a {
    color: #2163e8
}

.navbar2 .nav-list .sub-menu {
    position: absolute;
    left: 50%;
    top: 100%;
    opacity: 0;
    -webkit-transform: translate(-50%,-10px) scale(.97);
    transform: translate(-50%,-10px) scale(.97);
    list-style-type: none;
    visibility: hidden;
    width: auto;
    padding: .5rem;
    margin: -1px 0 0 0;
    border-radius: .3rem;
    -webkit-box-shadow: 0 0 30px rgba(0,0,0,.07);
    box-shadow: 0 0 30px rgba(0,0,0,.07);
    background-color: #fff;
    -webkit-transition: all .1s ease-in-out;
    transition: all .1s ease-in-out;
    z-index: 80
}

.navbar2 .nav-list .sub-menu:before {
    content: "";
    position: absolute;
    top: -19px;
    left: 50%;
    margin-left: -10px;
    border-width: 10px;
    border-style: solid;
    border-color: transparent;
    border-bottom-color: #fff
}

.navbar2 .nav-list .sub-menu .menu-item {
    display: block;
    padding: 0
}

.navbar2 .nav-list .sub-menu .menu-item>a {
    font-size: initial;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 10px 20px;
    -webkit-transition: -webkit-transform .3s cubic-bezier(.77,0,.175,1);
    transition: -webkit-transform .3s cubic-bezier(.77,0,.175,1);
    transition: transform .3s cubic-bezier(.77,0,.175,1);
    transition: transform .3s cubic-bezier(.77,0,.175,1),-webkit-transform .3s cubic-bezier(.77,0,.175,1)
}

.navbar2 .nav-list .sub-menu .menu-item>a:hover {
    opacity: 1
}

.navbar2 .nav-list .sub-menu .menu-item.menu-item-has-children>a {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.navbar2 .nav-list .sub-menu .menu-item.menu-item-has-children>a:after {
    content: "\f105";
    line-height: 1;
    margin-right: -4px
}

.navbar2 .nav-list .sub-menu .sub-menu {
    left: 100%;
    top: 0;
    margin: 0;
    -webkit-transform: translate(0,0) scale(1)!important;
    transform: translate(0,0) scale(1)!important
}

.navbar2 .nav-list .sub-menu .sub-menu:before {
    display: none
}

.navbar2 .navbar2-search {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    z-index: 1;
    visibility: hidden;
    opacity: 0;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.navbar2 .navbar2-search.show {
    visibility: visible;
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0)
}

.navbar2 .navbar2-search .search-form {
    color: #595d69
}

.navbar2 .actions {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    white-space: nowrap
}

.navbar2 .actions>*+* {
    margin-left: .5rem
}

.navbar2 .actions .action-btn {
  
    padding: .25rem;
    font-family: none;
    
    line-height: 1.5;
    cursor: pointer;
    text-align: center;
    vertical-align: middle;
    color: #4d4d4d;
    -webkit-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out
}

.navbar2 .actions .action-btn.login-btn2 {
    background-color: rgba(255,255,255,.25);
    color: #fff;
    padding: .2rem .75rem;
    font-size: .875rem;
    border-radius: 20px
}

.navbar2 .actions .action-btn:hover {
    opacity: .75
}

.navbar2 .actions .action-btn:active {
    border-color: transparent
}

.navbar2 .actions .action-btn.toggle-color {
    position: relative
}

.navbar2 .actions .action-btn.toggle-color>span {
    display: none
}

.navbar2 .actions .action-btn.toggle-color>span.show {
    display: inline-block
}

.burger {
    z-index: 1;
    position: relative;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: .2rem;
    cursor: pointer
}

.off-canvas .canvas-close {
    z-index: 1;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    border-radius: .2rem;
    color: #595d69;
    cursor: pointer;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-size: 1rem;
    height: 30px;
    width: 30px
}

.navbar2-hidden .navbar2 .logo {
    margin-right: 0
}

.navbar2-hidden .navbar2 .sep {
    display: none
}

.logo-wrapper {
    position: relative
}

.logo:not(.text) {
    -webkit-transition: opacity .5s cubic-bezier(.77,0,.175,1);
    transition: opacity .5s cubic-bezier(.77,0,.175,1)
}

.logo.contrary {
    left: 0;
    opacity: 0;
    position: absolute;
    top: 0
}

.logo.text {
    font-size: 1.5rem;
    font-weight: 900;
    letter-spacing: 2px;
    text-transform: uppercase;
    -webkit-transition: color .5s cubic-bezier(.77,0,.175,1);
    transition: color .5s cubic-bezier(.77,0,.175,1)
}

.logo.regular {
    max-height: 60px;
    width: auto
}

.navbar2 .action-hover-menu {
    min-width: 30px;
    list-style: none
}

.navbar2 .action-hover-menu .avatar-warp {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.navbar2 .action-hover-menu .avatar-warp .avatar-img {
    display: inline-block;
    width: 30px;
    height: 30px;
    border-radius: 4px
}

.navbar2 .action-hover-menu .hover-warp {
    display: block;
    visibility: hidden;
    opacity: 0;
    position: absolute;
    overflow: hidden;
    z-index: 1000;
    top: 100%;
    right: 0;
    font-size: .875rem;
    background-color: #fff;
    color: #595d69;
    width: 340px;
    -webkit-box-shadow: 0 0 30px rgba(0,0,0,.15);
    box-shadow: 0 0 30px rgba(0,0,0,.15);
    border-radius: .3rem;
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
    -webkit-transition: all .3s cubic-bezier(.77,0,.175,1);
    transition: all .3s cubic-bezier(.77,0,.175,1)
}

.navbar2 .action-hover-menu .hover-warp a {
    color: #595d69
}

.navbar2 .action-hover-menu .hover-warp a:hover {
    color: rgba(89,93,105,.85)
}

.navbar2 .action-hover-menu .hover-warp .hover-info {
    position: relative
}

.navbar2 .action-hover-menu .hover-warp .balance-qiandao {
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 1
}

.navbar2 .action-hover-menu .hover-warp .balance-qiandao>a {
    display: inline-block;
    line-height: 1;
    z-index: 1;
    padding: .3rem .65rem;
    font-size: .75rem;
    border-radius: 4px 0 0 0;
    background-color: rgba(132,189,255,.37)
}

.navbar2 .action-hover-menu .hover-info {
    padding: 1rem;
    background: linear-gradient(40deg,#eff7ff,#dfeeff,#f7fdff)
}

.navbar2 .action-hover-menu .abstop-item {
    position: absolute;
    right: 0;
    top: 0;
    padding: 1rem;
    font-size: .75rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.navbar2 .action-hover-menu .hover-item {
    display: block;
    position: relative;
    width: 100%
}

.navbar2 .action-hover-menu .hover-item .hover-link {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.navbar2 .action-hover-menu .hover-item .hover-link>a {
    text-align: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-size: .75rem
}

.navbar2 .action-hover-menu .hover-item .hover-link>a>i {
    display: block;
    font-size: 1rem;
    background: #eee;
    width: 40px;
    height: 40px;
    line-height: 40px;
    overflow: hidden;
    border-radius: 50%;
    color: #999;
    margin: 0
}

.navbar2 .action-hover-menu .hover-item .hover-link>a:hover {
    opacity: .85
}

.navbar2 .action-hover-menu hr {
    margin: 8px 0;
    opacity: .1
}

.slicknav_menu {
    position: relative
}

.slicknav_menu li,.slicknav_menuul {
    list-style: none;
    list-style-type: none;
    margin: 0;
    padding: 0
}

.slicknav_menu .slicknav_nav {
    padding: 10px;
    width: 100%;
    position: relative;
    overflow: hidden;
    display: -ms-flexbox;
    display: -webkit-box!important;
    display: flex!important;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.slicknav_menu .slicknav_nav li.slicknav_parent {
    -ms-flex: 0 0 100%;
    -webkit-box-flex: 0;
    flex: 0 0 100%;
    max-width: 100%
}

.slicknav_menu .slicknav_nav li.current-menu-item a {
    color: #007bff
}

.slicknav_menu .slicknav_nav>li {
    width: 100%;
    padding: .25rem;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis
}

.slicknav_menu .slicknav_nav .menu-item>a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    line-height: 1;
    outline: 0;
    padding: 12px 15px;
    border-radius: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
     background: rgba(255 255 255 /25%);
}

.slicknav_menu .slicknav_nav .menu-item>a>i {
    margin-left: 0!important;
    margin-right: 5px!important
}

.slicknav_menu .slicknav_nav .menu-item>a .slicknav_item a {
    margin-left: 10px;
    outline: 0
}

.slicknav_menu .slicknav_nav .menu-item>a .slicknav_item a i {
    margin-left: 0!important;
    margin-right: 5px!important
}

.slicknav_menu .slicknav_nav .sub-menu {
    border-radius: 0 0 2px 2px;
    overflow: hidden;
    background: rgba(0 0 0 /20%);
    padding: 5px;
    position: relative
}

.slicknav_menu .slicknav_nav .sub-menu>li {
    width: 100%;
    float: none
}

.slicknav_menu .slicknav_nav .sub-menu>li>a {
    border: none;
    border-radius: 0;
    line-height: 1;
    background: #rgba(0 0 0 /10%);
    margin: 10px 5px;
    border-radius: 4px;
    padding: 10px 5px
}

.slicknav_menu .slicknav_nav .sub-menu .sub-menu {
    width: 100%
}

.slicknav_menu .slicknav_nav .sub-menu .sub-menu li {
    width: 100%;
    float: none
}

.slicknav_menu .slicknav_nav hr {
    padding: 0;
    margin: 0
}

.slicknav_menu .slicknav_nav .slicknav_arrow {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    text-align: right
}

.ri-popup-content .logo-wrapper {
    position: relative;
    text-align: center
}

.off-canvas {
     background-color: rgba(0 0 0 /20%);
    height: 100vh;
    overflow-y: scroll;
    padding: 20px 0;
    position: fixed;
    right: 0;
    top: 0;
    -webkit-transform: translateX(320px);
    transform: translateX(320px);
    -webkit-transition: -webkit-transform .3s cubic-bezier(.77,0,.175,1);
    transition: -webkit-transform .3s cubic-bezier(.77,0,.175,1);
    transition: transform .3s cubic-bezier(.77,0,.175,1);
    transition: transform .3s cubic-bezier(.77,0,.175,1),-webkit-transform .3s cubic-bezier(.77,0,.175,1);
    width: 320px;
    z-index: 90;
    -webkit-overflow-scrolling: touch
}

.off-canvas .logo-wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin-bottom: 1rem
}

.off-canvas .canvas-close {
    position: absolute;
    right: 15px;
    top: 15px
}

.off-canvas .canvas-close .fas {
    -webkit-transition: -webkit-transform .5s cubic-bezier(.77,0,.175,1);
    transition: -webkit-transform .5s cubic-bezier(.77,0,.175,1);
    transition: transform .5s cubic-bezier(.77,0,.175,1);
    transition: transform .5s cubic-bezier(.77,0,.175,1),-webkit-transform .5s cubic-bezier(.77,0,.175,1)
}

.off-canvas .canvas-close:hover .fas {
    -webkit-transform: rotate(-180deg);
    transform: rotate(-180deg)
}

.off-canvas .mega-menu {
    display: none
}

.off-canvas .widget-area .widget {
    padding: 20px
}

.off-canvas .widget-area .widget+.widget {
    margin-top: 30px
}

.off-canvas .widget-area .widget.widget_magsy_promo_widget+.widget_magsy_promo_widget {
    margin-top: -20px
}

.off-canvas .widget-area .widget .widget-title {
    border-bottom: 1px solid #e6e6e6;
    margin: -20px -20px 20px;
    padding: 15px 20px
}

body:not(.canvas-visible) .off-canvas {
    visibility: hidden
}

.canvas-opened {
    overflow-y: hidden
}

.canvas-opened .off-canvas {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    visibility: visible
}

.dimmer {
    bottom: 0;
    display: none;
    left: -15px;
    position: fixed;
    right: 0;
    top: 0;
    background: rgba(0 0 0 /30%);
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    z-index: 90
}

.post-buy-widget {
    position: relative
}

.ri-down-warp {
    position: static
}

.ri-down-warp .down-msg {
    position: absolute;
    right: 1rem;
    top: 1rem;
    height: 1.5rem;
    line-height: 1.5rem;
    width: 5.656rem;
    -webkit-transform: translate(38px,-8px) rotate(45deg);
    transform: translate(38px,-8px) rotate(45deg);
    text-align: center;
    font-size: .75rem;
    background-color: #0cbc87;
    color: #fff
}

.ri-down-warp .down-buy-warp {
    position: relative;
    width: 100%;
    text-align: center;
    -webkit-transform: scale(1);
    transform: scale(1);
    margin: 0 auto;
    z-index: 1
}

.ri-down-warp .down-buy-warp .copy-pwd {
    cursor: pointer;
    text-align: center
}

.ri-down-warp .down-buy-warp .buy-title {
    color: #0cbc87;
    font-size: 1rem;
    margin-bottom: 1rem
}

.ri-down-warp .down-buy-warp .buy-btns {
    margin-bottom: 1rem
}

.ri-down-warp .down-buy-warp .buy-desc {
    color: #999;
    font-size: 1rem;
    margin-bottom: 1rem
}

.ri-down-warp .down-buy-warp .prices-info {
    padding: 5px 10px;
    border-radius: 4px;
    position: relative;
    color: #666;
    width: 100%;
    border: 1px dashed #ffdd65;
    margin-top: 10px
}

.ri-down-warp .down-buy-warp .prices-info .vip-rete-tips {
    display: block;
    font-size: .75rem;
    color: rgba(214,41,62,.85);
    letter-spacing: 1px;
    text-align: center
}

.ri-down-warp .down-buy-warp .prices-info .price-item {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 0 0 10px 15px;
    font-size: .75rem
}

.ri-down-warp .down-buy-warp .prices-info .price-item span i {
    display: none
}

.ri-down-warp .down-buy-warp .prices-info .price-item sup {
    color: #ff9800
}

.ri-down-warp .down-buy-warp .prices-info .price-item.default {
    color: #d6293e
}

.ri-down-warp .down-buy-warp .prices-info .price-item.no {
    color: #595d69
}

.ri-down-warp .down-buy-warp .prices-info .price-item.vip {
    color: #0cbc87
}

.ri-down-warp .down-buy-warp .prices-info .price-item.boosvip {
    color: #f7c32e
}

.ri-down-warp .down-buy-warp .prices-info .price-item:after,.ri-down-warp .down-buy-warp .prices-info .price-item:before {
    content: "";
    position: absolute;
    top: 4px
}

.ri-down-warp .down-buy-warp .prices-info .price-item:before {
    height: 100%;
    width: 1px;
    left: 5px;
    background: rgba(0,0,0,.19)
}

.ri-down-warp .down-buy-warp .prices-info .price-item:after {
    left: 0;
    display: inline-block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 11px;
    height: 11px;
    background-color: #ff8f8f;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    border-width: 2px;
    border-style: solid;
    border-color: #fff;
    -o-border-image: initial;
    border-image: initial;
    border-radius: 50%
}

.ri-down-warp .down-buy-warp .prices-info .price-item.default:after {
    background-color: #d6293e
}

.ri-down-warp .down-buy-warp .prices-info .price-item.no:after {
    background-color: #595d69
}

.ri-down-warp .down-buy-warp .prices-info .price-item.vip:after {
    background-color: #0cbc87
}

.ri-down-warp .down-buy-warp .prices-info .price-item.boosvip:after {
    background-color: #f7c32e
}

.ri-down-warp .down-buy-warp .prices-info .price-item:last-child::before {
    display: none
}

.ri-down-warp .down-buy-warp .prices-desc {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: baseline;
    -ms-flex-align: baseline;
    align-items: baseline;
    font-size: .875rem;
    padding: .5rem 1rem;
    background-color: #fff9e4;
    border-radius: .35rem
}

.ri-down-warp .down-buy-warp .prices-desc:after {
    content: "";
    position: absolute;
    bottom: -19px;
    left: 50%;
    margin-left: -10px;
    border-width: 10px;
    border-style: solid;
    border-color: #fff9e4 transparent transparent transparent
}

.ri-down-warp .down-buy-warp .prices-desc .prices-default {
    color: #d6293e
}

.ri-down-warp .down-buy-warp .prices-desc .prices-default>span {
    font-size: 1.5rem;
    font-weight: 600
}

.ri-down-warp .down-buy-warp .buy-count {
    margin-top: 1rem;
    color: #8d9da9;
    font-size: .875rem
}

.ri-down-warp .down-buy-warp .buy-count span {
    color: #03a9f4;
    margin: 0 2px
}

.sidebar {
    position: relative
}

.sidebar .widget {
    position: relative;
    border-radius: .25rem;
    background-color: #fff;
    overflow: hidden;
    padding: 1.25rem;
    margin-bottom: 1.5rem
}

.sidebar .widget .widget-title {
    font-size: 1rem;
    margin-bottom: 1rem;
    font-weight: 600
}

.sidebar .widget dl,.sidebar .widget ol,.sidebar .widget ul {
    list-style: none;
    margin: 0;
    padding-left: 0
}

.sidebar .widget ul>li {
    padding: 5px 0
}

.sidebar .widget_search .screen-reader-text,.sidebar .widget_search .search-submit {
    display: none
}

.sidebar .widget_search label {
    width: 100%
}

.sidebar .widget_search input {
    display: block;
    width: 100%;
    padding: .5rem .75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #f0f2f5;
    background-clip: padding-box;
    border: 1px solid #eaeff3;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 4px;
    -webkit-transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out,-webkit-box-shadow .15s ease-in-out
}

.sidebar .post-down-widget {
    position: relative
}

.sidebar .post-down-widget .down-thumb {
    max-height: 220px;
    width: 100%;
    -o-object-fit: cover;
    object-fit: cover
}

.sidebar .post-down-widget .down-title {
    padding: 10px;
    background: #eee;
    margin-bottom: 20px;
    word-wrap: break-word
}

.sidebar .post-down-widget .down-btn {
    border-radius: 0
}

.sidebar .post-down-widget .download-info {
    font-size: 15px
}

.sidebar .post-down-widget .download-info>li {
    padding: 5px 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.sidebar .sidebar-posts-list .post-item.item-list {
    padding: 0
}

.sidebar .sidebar-posts-list .post-item.item-list:hover {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    -webkit-box-shadow: unset;
    box-shadow: unset
}

.sidebar .sidebar-posts-list .post-item.item-list .entry-media {
    max-width: 80px;
    margin-right: .5rem
}

.sidebar .sidebar-posts-list .post-item.item-list .entry-title {
    font-size: .875rem;
    font-weight: 500;
    -webkit-line-clamp: 3
}

.sidebar .sidebar-ranking-list {
    position: relative
}

.sidebar .sidebar-ranking-list .ranking-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.sidebar .sidebar-ranking-list .ranking-item .ranking-num {
    font-size: .85em;
    margin-right: .3rem;
    padding: 5px 8px;
    border-radius: 50%
}

.sidebar .sidebar-ranking-list .ranking-item .ranking-title {
    font-size: 1rem;
    margin-bottom: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.home-search-box .search-bg {
    text-align: center;
    position: relative;
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: cover;
    z-index: 0
}

.home-search-box .search-bg-overlay {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: -webkit-gradient(linear,left bottom,left top,from(rgba(25,27,38,.32)),to(rgba(25,27,38,.72)));
    background: linear-gradient(0deg,rgba(25,27,38,.32),rgba(25,27,38,.72));
    z-index: -1
}

.home-search-box .bg-type-waves .search-bg-overlay {
    z-index: 1
}

.home-search-box .search-warp {
    padding-top: 120px;
    padding-bottom: 120px;
    max-width: 50%
}

.home-search-box .search-title {
    color: #fff;
    margin-bottom: .875rem;
    font-size: 1.75rem
}

.home-search-box .search-desc {
    color: rgba(255,255,255,.85);
    margin-bottom: 1.5rem;
    font-size: 1rem
}

.home-search-box .search-hots a,.home-search-box .search-hots span {
    color: rgba(255,255,255,.65)
}

.home-search-box .search-hots a:hover {
    color: #fff
}

.home-search-box .search-hots>span:not(:first-child)::after {
    content: ","
}

.home-search-box .search-hots>span:last-child::after {
    content: ""
}

.home-owl-slider {
    position: relative
}

.home-owl-slider>.container {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem
}

.home-owl-slider>.container .widget-slider {
    border-radius: .25rem;
    overflow: hidden
}

.home-owl-slider .widget-slider {
    position: relative
}

.home-owl-slider .widget-slider-item {
    position: relative;
    text-align: center
}

.home-owl-slider .widget-slider-item .slider-img {
    display: block;
    width: 100%;
    height: 100%
}

.home-owl-slider .widget-slider-item .slider-warp {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%)
}

.home-owl-slider .u-permalink {
    position: absolute;
    bottom: 0;
    left: 0;
    outline: 0;
    right: 0;
    top: 0;
    z-index: 1
}

.home-cat-box {
    position: relative
}

.home-cat-box .widget-catbox-item {
    position: relative;
    text-align: center;
    height: 120px;
    border-radius: .3rem;
    overflow: hidden;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    -webkit-transition: all .3s;
    transition: all .3s;
    z-index: 0
}

.home-cat-box .widget-catbox-item::before {
    z-index: -1;
    content: "";
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background: rgba(0,0,0,.5)
}

.home-cat-box .widget-catbox-item:hover {
    background-position: top right
}

.home-cat-box .widget-catbox-item:hover .catbox-content {
    opacity: .65
}

.home-cat-box .catbox-content {
    width: 100%;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    opacity: 1;
    -webkit-transition: all .3s;
    transition: all .3s
}

.home-cat-box .catbox-title {
    color: #fff;
    font-weight: 600;
    letter-spacing: 1px;
    font-size: 1rem;
    margin-bottom: 0
}

.home-last-post {
    position: relative
}

.home-last-post .section-cat-navbtn .btn {
    background-color: #fff;
    color: #191a1f;
    -webkit-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out
}

.home-last-post .section-cat-navbtn .btn.active,.home-last-post .section-cat-navbtn .btn:hover {
    color: #ff9800
}

.home-cat-post {
    position: relative
}

.home-cms-post {
    position: relative
}

.home-cms-post .cms-post-warp.grid-overlay .cms-left-itme .entry-media {
    --ri-aspect-ratio: calc(66.6666666667% + 0.3rem)
}

.home-cms-post .cms-post-warp.list .cms-left-itme .entry-media {
    --ri-aspect-ratio: calc(66.6666666667% + 14px)
}

.home-cms-post .cms-post-warp.list .cms-right-itme .entry-media {
    max-width: calc(20% - 2px)
}

.home-cms-post .cms-post-warp.list .cms-right-itme .post-format-icon {
    display: none
}

.home-cms-post .cms-post-warp.list .cms-right-itme .entry-title {
    -webkit-line-clamp: 1
}

.home-division .division-item {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background-color: #fff;
    padding: .5rem;
    border-radius: .35rem;
    height: 100%
}

.home-division .division-item:hover .division-icon>i {
    -webkit-transform: rotate(360deg) scale(1.2);
    transform: rotate(360deg) scale(1.2);
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.home-division .division-icon {
    display: block;
    line-height: 1;
    font-size: 1.5rem;
    padding: 1.25rem;
    color: #fff;
    background-color: #8399ff;
    text-align: center
}

.home-division .division-icon>i {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 1.5rem
}

.home-division .division-warp {
    margin-left: 1rem
}

.home-division .division-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 2px
}

.home-division .division-desc {
    margin-bottom: 0;
    font-size: .875rem;
    color: #a1a1a8
}

.home-background {
    position: relative
}

.home-background .bg-warp {
    text-align: center;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    background-attachment: scroll
}

.home-background .bg-warp.fixed {
    background-attachment: fixed
}

.home-background .bg-title {
    font-size: 1.5rem;
    color: #fff
}

.home-background .bg-desc {
    color: #afafaf;
    margin-bottom: .5rem
}

.sidebar-author-info .author-header {
    margin-bottom: 1rem
}

.sidebar-author-info .author-body {
    margin-bottom: 1rem
}

.sidebar-author-info .author-footer {
    margin-bottom: 0
}

.home-dynamic {
    font-size: .875rem
}

.home-dynamic .dynamic-warp {
    position: relative;
    overflow: hidden;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.home-dynamic .dynamic-slider-item .name {
    margin-left: .2rem
}

.home-dynamic .dynamic-slider-item .info {
    display: inline-block;
    margin-left: .5rem;
    margin-bottom: 0;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.home-dynamic .dynamic-slider-item .times {
    margin-left: .5rem
}

.home-dynamic .dynamic-slider {
    position: relative;
    overflow: hidden
}

.home-dynamic .dynamic-slider .owl-nav {
    width: auto;
    left: auto;
    -webkit-transform: translate(0,-50%);
    transform: translate(0,-50%);
    right: 0;
    padding: 0
}

.home-dynamic .dynamic-slider .owl-nav [class*=owl-] {
    margin: 0 2px;
    background: unset;
    color: rgba(0,0,0,.65)
}

.home-dynamic .dynamic-slider .owl-nav [class*=owl-]:hover {
    background: unset
}

.home-scratch-card {
    position: relative
}

.home-scratch-card .scratch-item .scratch-code {
    text-align: center
}

.home-scratch-card .scratch-item .code-content {
    padding: 1.25rem;
    background-color: #fff;
    border-radius: .35rem
}

.home-scratch-card .scratch-item .code-area {
    text-align: center
}

.home-scratch-card .scratch-item .code-title {
    font-size: 1.15rem;
    font-weight: 600;
    margin-bottom: .5rem
}

.home-scratch-card .scratch-item .txt-code {
    margin: 0 auto;
    text-align: center;
    padding: .5rem;
    border: 2px dotted #ffdb57;
    border-radius: .5rem;
    position: relative;
    margin-top: .5rem
}

.home-scratch-card .scratch-item .gift {
    width: 60px
}

.home-title-post .list-posts {
    position: relative;
    border-radius: .3rem;
    overflow: hidden
}

.home-title-post .list-posts .category-bg {
    position: relative;
    background: rgba(51,51,51,.8);
    padding: 1rem;
    background-size: 100%;
    background-position: center center;
    z-index: 0
}

.home-title-post .list-posts .category-bg:after {
    content: "";
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background: rgba(0,0,0,.55);
    z-index: -1;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px)
}

.home-title-post .list-posts .category-title {
    font-size: 1.15rem;
    text-align: center;
    margin-bottom: .5rem;
    -webkit-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out
}

.home-title-post .list-posts .category-title a,.home-title-post .list-posts .category-title i,.home-title-post .list-posts .category-title span {
    color: #fff
}

.home-title-post .list-posts .category-title:hover {
    opacity: .8
}

.home-title-post .list-posts .category-desc {
    display: block;
    text-align: center;
    line-height: 1.5;
    font-size: .875rem;
    color: rgba(255,255,255,.6)
}

.home-title-post .list-posts .category-desc p {
    margin: 0;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.home-title-post .list-posts ul {
    list-style: none;
    padding: 0;
    margin: 0;
    padding: 1rem;
    background-color: #fff
}

.home-title-post .list-posts ul li {
    position: relative;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    width: 100%;
    margin-bottom: .25rem
}

.home-title-post .list-posts ul li .title {
    display: inline-block;
    padding-right: 5px;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-left: 0
}

.home-title-post .list-posts ul li .post-num {
    display: inline-block;
    color: #fff;
    background-color: #999;
    font-size: 14px;
    width: 18px;
    height: 18px;
    line-height: 18px;
    margin: 0 8px 0 0;
    text-align: center;
    border-radius: 4px
}

.home-title-post .list-posts ul li .post-views {
    font-size: .75rem;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-left: auto;
    color: #efa9b2
}

.home-title-post .list-posts ul li:nth-child(1) .post-num {
    background-color: #f44336
}

.home-title-post .list-posts ul li:nth-child(2) .post-num {
    background-color: #4caf50
}

.home-title-post .list-posts ul li:nth-child(3) .post-num {
    background-color: #ffc107
}

.home-title-post .list-posts ul li:nth-child(4) .post-num {
    background-color: #00bcd4
}

.home-title-post .list-posts ul li:nth-child(5) .post-num {
    background-color: #2196f3
}

.home-title-post .list-posts ul li:nth-child(6) .post-num {
    background-color: #e91e63
}

.home-title-post .list-posts ul li:nth-child(7) .post-num {
    background-color: #34495e
}

.home-title-post .list-posts ul li:nth-child(8) .post-num {
    background-color: #f4e3ab
}

.home-overview {
    position: relative
}

.home-overview .bg-warp {
    text-align: center;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    background-attachment: scroll
}

.home-overview .bg-warp.fixed {
    background-attachment: fixed
}

.home-overview .bg-title {
    font-size: 1rem;
    color: #fff
}

.home-overview .count-item {
    text-align: center
}

.home-overview .count-item span {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.5;
    color: #fff
}

.home-overview .count-item strong {
    display: block;
    font-size: .75rem;
    font-weight: 500;
    color: rgba(166,193,246,.85)
}

.h-navList {
    list-style: none;
    position: relative;
    border-radius: .25rem;
    background-color: #fff;
    overflow: hidden;
    padding: 1.25rem;
    margin-bottom: 1.5rem
}

.h-navList li {
    font-weight: 500;
    line-height: 1.8
}

.h-navList .nav-h1 {
    font-size: 1.25rem
}

.h-navList .nav-h2 {
    font-size: 1rem
}

.h-navList .nav-h3 {
    font-size: .95rem;
    padding-left: .5rem
}

.posts-warp {
    position: relative
}

.post-item {
    position: relative;
    -webkit-transition: all .3s;
    transition: all .3s;
    overflow: hidden;
    background-color: #fff;
    border-radius: .3rem
}

.post-item:hover {
    -webkit-transform: translateY(-3px);
    transform: translateY(-3px);
    -webkit-box-shadow: 0 1rem 3rem rgba(31,45,61,.13);
    box-shadow: 0 1rem 3rem rgba(31,45,61,.13)
}

.post-item .tips-badge {
    position: relative
}

.post-item .tips-badge .badge {
    font-size: .65rem;
    color: #950000
}

.post-item .entry-media {
    display: block;
    position: relative;
    overflow: hidden
}

.post-item .entry-media .media-img {
    border-radius: inherit;
    background-color: #e7e7e7;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out
}

.post-item .entry-media .media-img.lazy.loading {
    opacity: 0;
    background-color: #cce4ff;
    -webkit-animation: fadeInAnimation .2s ease-in forwards;
    animation: fadeInAnimation .2s ease-in forwards
}

.post-item .entry-media .media-img.lazy.loading:before {
    display: block;
    position: absolute;
    content: "";
    width: 30px;
    height: 30px;
    background: 0 0;
    border: 3px solid;
    border-radius: 100%;
    border-color: #4f9ef8 transparent #0cbc87 transparent;
    -webkit-animation: rotate 1s 0s cubic-bezier(.09,.57,.49,.9) infinite;
    animation: rotate 1s 0s cubic-bezier(.09,.57,.49,.9) infinite;
    -webkit-animation-duration: 1s;
    animation-duration: 1s
}

.post-item .entry-media .media-preview {
    width: 100%;
    height: 100%;
    position: absolute;
    overflow: hidden
}

.post-item .entry-media .media-preview.audio:before {
    content: "";
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background: rgba(0,0,0,.5)
}

.post-item .entry-media .media-preview>video {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    -webkit-transform: scale(1.1);
    transform: scale(1.1)
}

.post-item .entry-media .media-preview .progress-bar {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 3px;
    background-color: transparent
}

.post-item .entry-media .media-preview .progress {
    width: 0%;
    height: 100%;
    background-color: rgba(255,255,255,.5);
    -webkit-transition: width .3s ease;
    transition: width .3s ease
}

.post-item .entry-media .media-preview .centered-html-cd {
    -webkit-transform: translate(-50%,-50%) scale(.4);
    transform: translate(-50%,-50%) scale(.4)
}

.post-item .entry-media .post-format-icon {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #fff;
    font-size: 1rem;
    border-radius: 50%;
    background: rgba(0,0,0,.45);
    border: 2px solid transparent;
    width: 2.5rem;
    height: 2.5rem;
    -webkit-transition: all .1s ease-in-out;
    transition: all .1s ease-in-out
}

.post-item:hover .entry-media .post-format-icon {
    background: rgba(0,0,0,.2);
    border: 2px solid #fff
}

.post-item .entry-desc,.post-item .entry-title {
    margin: 0;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    -webkit-line-clamp: 1
}

.post-item .entry-title {
    font-size: .95rem;
    font-weight: 600
}

.post-item .entry-desc {
    color: #a1a1a8;
    margin-top: 4px;
    font-size: .75rem;
    line-height: 1.25;
    white-space: normal
}

.post-item .entry-cat-dot {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 4px
}

.post-item .entry-cat-dot>a {
    color: #a1a1a8;
    font-size: .75rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.post-item .entry-cat-dot>a:first-child::before {
    background-color: rgba(33,99,232,.75)
}

.post-item .entry-cat-dot>a:nth-child(2)::before {
    background-color: rgba(247,195,46,.75)
}

.post-item .entry-cat-dot>a:nth-child(3)::before {
    background-color: rgba(12,188,135,.75)
}

.post-item .entry-cat-dot>a:nth-child(4)::before {
    background-color: rgba(79,158,248,.75)
}

.post-item .entry-cat-dot>a::before {
    display: block;
    content: "";
    border-radius: 50%;
    height: 6px;
    width: 6px;
    margin-right: 5px
}

.post-item .entry-cat-dot>a:hover {
    opacity: .75
}

.post-item .entry-cat-dot>a+a {
    margin-left: 6px
}

.post-item .entry-meta {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #a1a1a8;
    font-size: .75rem
}

.post-item .entry-meta a {
    color: #a1a1a8
}

.post-item .entry-meta>span {
    margin-right: 10px
}

.post-item .entry-meta>span:last-child {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    text-align: right;
    margin-right: 0
}

.post-item.item-grid .entry-wrapper {
    padding: 10px
}

.post-item.item-grid .entry-meta {
    margin-top: 8px;
    white-space: nowrap;
    letter-spacing: -.2px
}

.post-item.item-grid .entry-meta>span {
    margin-right: 10px
}

.post-item.item-grid .entry-meta>span:last-child {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    text-align: right;
    margin-right: 0
}

.post-item.item-grid.grid-overlay {
    position: relative;
    overflow: hidden
}

.post-item.item-grid.grid-overlay .entry-wrapper {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: .5rem;
    -webkit-transition: all .3s ease-out;
    transition: all .3s ease-out;
    background-image: -webkit-gradient(linear,left top,left bottom,from(transparent),to(rgba(0,0,0,.85)));
    background-image: linear-gradient(180deg,transparent,rgba(0,0,0,.85));
    opacity: 1
}

.post-item.item-grid.grid-overlay .entry-wrapper a {
    color: #fff
}

.post-item.item-grid.grid-overlay:hover .entry-wrapper {
    opacity: 0;
    bottom: -50%
}

.post-item.item-grid.grid-overlay:hover .entry-title {
    display: block
}

.post-item.item-grid.grid-overlay:hover .entry-title a {
    color: #ffa301
}

.post-item.item-list {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    min-width: 0;
    word-wrap: break-word;
    padding: .5rem
}

.post-item.item-list .entry-media {
    border-radius: .25rem;
    max-width: 200px;
    margin-right: 1rem
}

.post-item.item-list .entry-wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    min-width: 0
}

.post-item.item-list .entry-body {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto
}

.post-item.item-list .entry-title {
    font-size: 1rem;
    font-weight: 600;
    -webkit-line-clamp: 2
}

.post-item.item-list .entry-desc {
    -webkit-line-clamp: 2
}

.post-item.item-list .entry-footer {
    margin-top: 8px
}

.login-and-register {
    position: relative;
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: cover;
    height: 100vh
}

.login-and-register.bg-type-img .bg-overlay {
    z-index: -1
}

.login-and-register .account-from {
    max-width: 300px
}

.tags-page-warp .tag-item {
    position: relative;
    display: block;
    border-radius: .5rem;
    background: #fff
}

.tags-page-warp .tag-item .tag-substr {
    text-transform: uppercase;
    text-align: center;
    line-height: 3.5rem;
    padding: 0;
    width: 3.5rem;
    height: 3.5rem;
    font-size: 1.8rem;
    border-radius: 50%;
    background: #eee;
    color: #ccc
}

.tags-page-warp .tag-item:hover .tag-substr {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
    -webkit-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out
}

.tags-page-warp .tag-item:hover .text-dark {
    text-decoration: underline
}

.links-page-warp .link-item {
    position: relative;
    display: block;
    border-radius: .5rem;
    background: #fff
}

.links-page-warp .link-item .link-img {
    text-transform: uppercase;
    text-align: center;
    line-height: 3.5rem;
    padding: 0;
    width: 3.5rem;
    height: 3.5rem;
    font-size: 1.8rem;
    border-radius: .3rem;
    background: #eee;
    color: #ccc;
    background-size: cover;
    background-repeat: no-repeat;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.links-page-warp .link-item:hover .link-img {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
    border-radius: 50%
}

.links-page-warp .link-item:hover .text-dark {
    text-decoration: underline
}

.uc-page .uc-menu-warp {
    position: relative;
    padding: 1rem;
    margin: 0;
    list-style: none
}

.uc-page .uc-menu-warp .menu-item {
    text-align: center;
    letter-spacing: 1px
}

.uc-page .uc-menu-warp .menu-item a {
    color: #595d69;
    padding: 8px;
    width: 100%;
    display: inline-block;
    line-height: 1.5
}

.uc-page .uc-menu-warp .menu-item a:hover,.uc-page .uc-menu-warp .menu-item.current-menu-item a {
    color: #2163e8
}

.uc-page .coin-balance-body {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background: linear-gradient(50deg,#fff,#fffde9)
}

.uc-page .coin-balance-body .balance-qiandao {
    position: absolute;
    right: .3rem;
    top: .3rem
}

.uc-page .coin-balance-body .balance-info {
    text-align: center;
    color: #ff9800
}

.uc-page .coin-balance-body .balance-info hr {
    margin: .5rem 0
}

.uc-page .coin-pay-card {
    position: relative;
    cursor: pointer;
    border: 2px solid rgba(0,0,0,.1);
    border-radius: .5rem;
    overflow: hidden;
    background: rgba(0,0,0,.03);
    padding: 1rem .3rem;
    -webkit-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out
}

.uc-page .coin-pay-card.active,.uc-page .coin-pay-card:hover {
    border: 2px solid #ffc107;
    background: unset
}

.uc-page .vip-cdk-body form {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    max-width: 300px;
    margin: 0 auto
}

div.video-js .vjs-poster:before {
    content: "";
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background: rgba(0,0,0,.75)
}

div.video-js .vjs-poster img {
    -o-object-fit: cover;
    object-fit: cover
}

div.video-js .vjs-control-bar {
    background-color: transparent;
    background: 0 0;
    background-image: -webkit-gradient(linear,left bottom,left top,from(#000),to(rgba(0,0,0,0)));
    background-image: linear-gradient(0deg,#000,rgba(0,0,0,0))
}

.video-hero .video-hero-container {
    padding-top: calc(var(--ri-gutter-x) * .5);
    padding-bottom: calc(var(--ri-gutter-x) * .5)
}

.centered-html-cd {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%,-50%) scale(1);
    transform: translate(-50%,-50%) scale(1);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    text-align: center;
    border-radius: 50%
}

.centered-html-cd .souse-img {
    width: 500px;
    height: 500px;
    background: url(../img/img-new-bg.png) center no-repeat;
    background-size: cover;
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.centered-html-cd .icon-cd {
    background: url(../img/img-new-cd.png) no-repeat center;
    background-size: cover;
    width: 280px;
    height: 280px
}

.centered-html-cd .icon-left {
    position: absolute;
    top: 0;
    right: 170px;
    width: 88px;
    height: 241px;
    z-index: 2;
    background: url(../img/img-new-left.png) no-repeat center;
    background-size: cover;
    -webkit-transform: rotate(-36deg);
    transform: rotate(-36deg);
    -webkit-transition: all .6s ease-in-out;
    transition: all .6s ease-in-out;
    -webkit-transform-origin: right top;
    transform-origin: right top
}

.centered-html-cd .rotate {
    -webkit-animation: rotate 2s linear infinite;
    animation: rotate 2s linear infinite
}

.centered-html-cd .skewing {
    -webkit-transform: rotate(0);
    transform: rotate(0)
}

.ri-video-warp {
    position: relative;
    overflow: hidden
}

.ri-video-warp .ri-video-view {
    position: relative
}

.ri-video-warp .video-buy-warp {
    position: absolute;
    z-index: 1;
    text-align: center;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%,-50%) scale(1);
    transform: translate(-50%,-50%) scale(1);
    width: 100%
}

.ri-video-warp .video-buy-warp .buy-title {
    color: #fff;
    font-size: 1.25rem;
    margin-bottom: 1rem
}

.ri-video-warp .video-buy-warp .buy-btns {
    margin-bottom: 1rem
}

.ri-video-warp .video-buy-warp .buy-desc {
    color: #999;
    font-size: 1rem;
    margin-top: 10px
}

.ri-video-warp .video-buy-warp .prices-info {
    list-style: none;
    padding: 0;
    margin: 0;
    position: relative;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding: .5rem;
    background: #fff;
    border-radius: .25rem
}

.ri-video-warp .video-buy-warp .prices-info:before {
    content: "";
    position: absolute;
    top: -19px;
    left: 50%;
    margin-left: -10px;
    border-width: 10px;
    border-style: solid;
    border-color: transparent transparent #fff transparent
}

.ri-video-warp .video-buy-warp .prices-info .price-item {
    padding: 6px;
    display: inline-block;
    line-height: 1.2;
    border-radius: .25rem;
    font-size: .875rem;
    text-shadow: 0 1px 2px #fff
}

.ri-video-warp .video-buy-warp .prices-info .price-item.default {
    color: #d6293e
}

.ri-video-warp .video-buy-warp .prices-info .price-item.no {
    color: #595d69
}

.ri-video-warp .video-buy-warp .prices-info .price-item.vip {
    color: #0cbc87
}

.ri-video-warp .video-buy-warp .prices-info .price-item.boosvip {
    color: #f7c32e
}

.ri-video-warp .video-buy-warp .buy-count {
    margin-top: 1rem;
    color: #8d9da9;
    font-size: .875rem
}

.ri-video-warp .video-buy-warp .buy-count span {
    color: #03a9f4;
    margin: 0 2px
}

.ri-video-list {
    position: relative;
    background: rgba(22,22,31,.8);
    padding: 1rem;
    height: 100%
}

.ri-video-list .video-title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 4px;
    font-size: 1rem
}

.ri-video-list .video-title .title-span {
    color: #fff
}

.ri-video-list .video-title .title-count {
    color: #7c7e8d;
    margin: 0;
    margin-left: .5rem
}

.ri-video-list .video-nav {
    list-style: none;
    padding: 0;
    margin: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.ri-video-list .video-nav .switch-video {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 50px;
    height: 30px;
    margin: 4px;
    border-radius: 4px;
    background: rgba(0,0,0,.1);
    font-size: 1rem;
    background: #3b3b3b;
    color: #fff
}

.ri-video-list .video-nav .switch-video.active>span {
    display: none
}

.ri-video-list .video-nav .switch-video.active:before {
    content: "";
    background: url(../img/playing.gif) no-repeat;
    width: 16px;
    height: 16px;
    background-size: cover
}

.ri-hide-warp {
    position: relative;
    overflow: hidden;
    border-radius: .5rem;
    padding: 1.5rem 1rem;
    background-color: #eaf6ff;
    border: dashed 2px #ffb1cb;
    margin-bottom: 1rem
}

.ri-hide-warp .hide-msg {
    position: absolute;
    right: 0;
    top: 0;
    font-size: .75rem;
    padding: .35rem .5rem;
    line-height: 1;
    border-radius: 0 0 0 .5rem;
    color: #fff;
    background: #ffb1cb
}

.ri-hide-warp .hide-buy-warp {
    position: relative;
    width: 100%;
    text-align: center;
    -webkit-transform: scale(1);
    transform: scale(1);
    margin: 0 auto
}

.ri-hide-warp .hide-buy-warp .buy-title {
    color: #ff5722;
    font-size: 1.25rem;
    margin-bottom: 1rem
}

.ri-hide-warp .hide-buy-warp .buy-btns {
    margin-bottom: 1rem
}

.ri-hide-warp .hide-buy-warp .buy-desc {
    color: #999;
    font-size: 1rem;
    margin-top: 10px
}

.ri-hide-warp .hide-buy-warp .prices-info {
    list-style: none;
    padding: 0;
    margin: 0;
    position: relative;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding: .5rem;
    background: rgba(33,150,243,.1);
    border-radius: .25rem
}

.ri-hide-warp .hide-buy-warp .prices-info:before {
    content: "";
    position: absolute;
    top: -19px;
    left: 50%;
    margin-left: -10px;
    border-width: 10px;
    border-style: solid;
    border-color: transparent transparent rgba(33,150,243,.25) transparent
}

.ri-hide-warp .hide-buy-warp .prices-info .price-item {
    padding: 6px;
    display: inline-block;
    line-height: 1.2;
    border-radius: .25rem;
    font-size: .875rem
}

.ri-hide-warp .hide-buy-warp .prices-info .price-item.default {
    color: #d6293e
}

.ri-hide-warp .hide-buy-warp .prices-info .price-item.no {
    color: #595d69
}

.ri-hide-warp .hide-buy-warp .prices-info .price-item.vip {
    color: #0cbc87
}

.ri-hide-warp .hide-buy-warp .prices-info .price-item.boosvip {
    color: #f7c32e
}

.ri-hide-warp .hide-buy-warp .buy-count {
    margin-top: 1rem;
    color: #8d9da9;
    font-size: .875rem
}

.ri-hide-warp .hide-buy-warp .buy-count span {
    color: #03a9f4;
    margin: 0 2px
}

.ri-hide-warp.ri-reply-hide {
    border-color: #cfcfcf
}

.ri-hide-warp.ri-reply-hide .hide-msg {
    background: #cfcfcf
}

.ri-hide-warp.ri-reply-hide .buy-title {
    color: #545975
}

.ri-hide-warp.ri-login-hide {
    border-color: #84d9ff
}

.ri-hide-warp.ri-login-hide .hide-msg {
    background: #84d9ff
}

.ri-hide-warp.ri-login-hide .buy-title {
    color: #16629f
}

.ri-video-shortcode {
    margin-bottom: 1rem
}

.ri-alerts-shortcode {
    margin-bottom: 1rem
}

.ri-accordions-shortcode {
    margin-bottom: 1rem
}

.ri-accordions-shortcode .alert hr {
    margin-top: .5rem;
    margin-bottom: .5rem;
    border-top: 1px solid rgba(0,0,0,.11)
}

.article-header {
    margin-bottom: 1rem
}

.article-header .post-title {
    font-size: 1.5rem;
    font-weight: 600
}

.article-header .article-meta {
    font-size: .875rem
}

.article-header .article-meta a,.article-header .article-meta span {
    color: #a1a1a8
}

.article-header .article-meta>span+span {
    margin-left: 8px
}

.archive-media-preview {
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
    border-radius: .35rem
}

.archive-media-preview .preview-text {
    position: absolute;
    left: 0;
    top: 0;
    line-height: 1;
    font-size: .875rem;
    color: #fff;
    padding: .5rem 1rem;
    background: rgba(0,0,0,.8);
    z-index: 1
}

.archive-media-preview .vjs-16-2 {
    height: 0;
    width: 100%;
    max-width: 100%;
    padding-top: 28%
}

.archive-media-preview .centered-html-cd {
    -webkit-transform: translate(-50%,-50%) scale(.4);
    transform: translate(-50%,-50%) scale(.4)
}

.archive-shop .img-box {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: .25rem
}

.archive-shop .img-box .views-rounded-dot {
    content: " ";
    position: absolute;
    border-radius: 50%;
    background: #fc625d;
    width: 10px;
    height: 10px;
    top: 10px;
    left: 10px;
    -webkit-box-shadow: 20px 0 #fdbc40,40px 0 #35cd4b;
    box-shadow: 20px 0 #fdbc40,40px 0 #35cd4b
}

.archive-shop .img-box img {
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%;
    height: 100%
}

.archive-shop .info-box {
    position: relative
}

.archive-shop .article-meta {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding: .5rem;
    border-radius: .25rem;
    background-color: rgba(87,165,255,.15)
}

.archive-shop .article-meta a,.archive-shop .article-meta li {
    width: 50%;
    font-size: .875rem;
    color: #999;
    line-height: 1.8;
    list-style: none
}

.archive-shop .ri-down-warp {
    position: relative
}

.archive-shop .ri-down-warp .down-buy-warp {
    text-align: initial
}

.archive-shop .ri-down-warp .down-buy-warp .buy-title {
    margin-bottom: .5rem;
    font-size: .875rem
}

.archive-shop .ri-down-warp .down-buy-warp .buy-desc {
    margin-bottom: .5rem
}

.archive-shop .ri-down-warp .down-buy-warp .prices-desc {
    width: -webkit-max-content;
    width: -moz-max-content;
    width: max-content;
    padding: .25rem 1rem
}

.archive-shop .ri-down-warp .down-buy-warp .prices-info {
    width: 100%;
    padding: 0;
    margin: 0;
    position: relative;
    padding: .25rem;
    background: rgba(253,126,20,.1);
    border-radius: .25rem;
    border: unset
}

.archive-shop .ri-down-warp .down-buy-warp .prices-info:before {
    display: none
}

.archive-shop .ri-down-warp .down-buy-warp .prices-info .price-item {
    padding: 6px;
    display: inline-block;
    line-height: 1.2;
    border-radius: .25rem;
    font-size: .875rem;
    padding-left: 15px
}

.archive-shop .ri-down-warp .down-buy-warp .prices-info .price-item.default {
    color: #d6293e
}

.archive-shop .ri-down-warp .down-buy-warp .prices-info .price-item.no {
    color: #595d69
}

.archive-shop .ri-down-warp .down-buy-warp .prices-info .price-item.vip {
    color: #0cbc87
}

.archive-shop .ri-down-warp .down-buy-warp .prices-info .price-item.boosvip {
    color: #f7c32e
}

.archive-shop .ri-down-warp .down-buy-warp .prices-info .price-item:before {
    display: none
}

.archive-shop .ri-down-warp .down-buy-warp .prices-info .price-item:after {
    top: 8px
}

.archive-shop .ri-down-warp .down-buy-warp .buy-btns {
    margin-bottom: 0
}

.post-content {
    position: relative;
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 1.8
}

.post-content .ri-down-warp {
    margin: 1rem 0;
    position: relative;
    overflow: hidden;
    border-radius: .5rem;
    padding: 1.5rem 1rem;
    background-color: #fff;
    border: dashed 2px #03a9f4
}

.post-content a:not(.btn) {
    color: #03a9f4
}

.post-content a:not(.btn):hover {
    color: #d6293e
}

.post-content p {
    margin-bottom: 20px;
    line-height: 1.8;
    word-wrap: break-word
}

.post-content blockquote {
    position: relative;
    padding: 10px 10px 10px 55px;
    background: #f3f3f3;
    border-left: 0;
    font-size: 16px;
    border-radius: 3px;
    color: #444
}

.post-content blockquote:before {
    position: absolute;
    width: 30px;
    height: 30px;
    left: 10px;
    top: 15px;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg class='icon' viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='180' height='180'%3E%3Cpath d='M195.541 739.03C151.595 692.351 128 640 128 555.135c0-149.333 104.832-283.179 257.28-349.355l38.101 58.795c-142.293 76.97-170.112 176.853-181.205 239.83 22.912-11.862 52.907-16 82.304-13.27 76.97 7.125 137.643 70.315 137.643 148.864a149.333 149.333 0 01-149.334 149.333 165.163 165.163 0 01-117.248-50.304zm426.667 0c-43.947-46.678-67.541-99.03-67.541-183.894 0-149.333 104.832-283.179 257.28-349.355l38.101 58.795c-142.293 76.97-170.112 176.853-181.205 239.83 22.912-11.862 52.906-16 82.304-13.27 76.97 7.125 137.642 70.315 137.642 148.864a149.333 149.333 0 01-149.333 149.333 165.163 165.163 0 01-117.248-50.304z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: 100% auto;
    content: "";
    opacity: .15
}

.post-content blockquote ol:last-child,.post-content blockquote p:last-child,.post-content blockquote ul:last-child {
    margin-bottom: 0
}

.post-content figure {
    margin-bottom: 20px
}

.post-content audio,.post-content embed,.post-content iframe,.post-content img,.post-content video {
    max-width: 100%;
    vertical-align: top;
    height: auto
}

.post-content iframe {
    overflow: hidden;
    border-radius: .3rem
}

.post-content>.h1,.post-content>h1 {
    margin: 32px 0 16px;
    line-height: 1.4;
    font-weight: 600;
    font-size: 23px
}

.post-content>.h2,.post-content>h2 {
    padding-bottom: 8px;
    font-size: 20px;
    border-bottom: 1px solid #efefef;
    margin: 32px 0 16px;
    line-height: 1.4;
    font-weight: 600
}

.post-content>.h3,.post-content>h3 {
    position: relative;
    padding-left: 18px;
    line-height: 1.46;
    border: 0;
    font-size: 17px;
    font-weight: 600
}

.post-content>.h3::before,.post-content>h3::before {
    position: absolute;
    top: 3px;
    left: 0;
    content: "";
    width: 3px;
    height: calc(100% - 8px);
    border-radius: 3px;
    background-color: #2163e8;
    background-image: -webkit-gradient(linear,left top,left bottom,from(rgba(255,255,255,.15)),to(transparent));
    background-image: linear-gradient(180deg,rgba(255,255,255,.15),transparent)
}

.post-content>.h4,.post-content>h4 {
    font-weight: 500;
    font-size: 16px
}

.post-content>.h5,.post-content>h5 {
    font-weight: 500;
    font-size: 15px
}

.post-content>.h6,.post-content>h6 {
    font-weight: 500;
    font-size: 13px
}

.post-content hr {
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    height: 0;
    overflow: visible;
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #efefef;
    opacity: 1
}

.post-content .gallery {
    margin: 0 -5px;
    font-size: 0;
    margin-bottom: 20px
}

.post-content .gallery .gallery-item {
    display: inline-block;
    padding: 5px;
    text-align: center;
    vertical-align: top;
    margin-bottom: 0
}

.post-content .gallery .gallery-item .gallery-icon {
    position: relative
}

.post-content .gallery .gallery-item img {
    border-radius: .25rem;
    max-width: 100%;
    height: auto;
    vertical-align: top;
    border: 0;
    -webkit-transition: -webkit-transform .2s ease-in-out;
    transition: -webkit-transform .2s ease-in-out;
    transition: transform .2s ease-in-out;
    transition: transform .2s ease-in-out,-webkit-transform .2s ease-in-out
}

.post-content .gallery .gallery-item a>img:hover {
    -webkit-transform: scale(1.05);
    transform: scale(1.05)
}

.post-content .gallery-columns-1 .gallery-item {
    width: 100%
}

.post-content .gallery-columns-2 .gallery-item {
    width: 50%
}

.post-content .gallery-columns-3 .gallery-item {
    width: 33.33333%
}

.post-content .gallery-columns-4 .gallery-item {
    width: 25%
}

.post-content .gallery-columns-5 .gallery-item {
    width: 20%
}

.post-content .gallery-columns-6 .gallery-item {
    width: 16.66667%
}

.post-content .gallery-columns-7 .gallery-item {
    width: 14.28571%
}

.post-content .gallery-columns-8 .gallery-item {
    width: 12.5%
}

.post-content .gallery-columns-9 .gallery-item {
    width: 11.11111%
}

.post-content .wp-caption {
    max-width: 100%
}

.post-content .wp-caption-text {
    padding-top: 5px;
    text-align: center;
    text-indent: 0;
    font-size: 14px;
    line-height: 1.5;
    color: #666
}

.post-content .alignleft {
    float: left;
    margin: 10px 20px 10px 0
}

.post-content .alignright {
    float: right;
    margin: 10px 0 10px 20px
}

.post-content .aligncenter {
    display: block;
    clear: both;
    margin: 0 auto
}

.post-content .alignnone {
    margin: 0
}

.post-content .wp-video {
    margin-bottom: 20px
}

.post-content table {
    width: 100%
}

.post-content table th {
    font-weight: 500
}

.post-content table td,.post-content table th {
    padding: 6px 13px;
    border: 1px solid var(--ri-border-color)
}

.post-content table td>:last-child {
    margin-bottom: 0
}

.post-content table tr {
    background-color: transparent;
    border-top: 1px solid var(--ri-border-color)
}

.post-content table tr:nth-child(2n) {
    background-color: rgba(0,0,0,.05)
}

.post-content table img {
    background-color: transparent
}

.entry-copyright {
    padding: .5rem;
    border-radius: .25rem;
    background-color: rgba(79,158,248,.1);
    font-size: .875rem;
    color: #a1a1a8
}

.entry-tags {
    display: block;
    position: relative
}

.entry-tags>a {
    display: inline-block;
    padding: .35em .65em;
    margin: 2px 2px 2px 0;
    font-size: .85em;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25rem;
    background-color: #f0f1f3;
    color: #a1a1a8
}

.entry-tags>a:hover {
    opacity: .85
}

.entry-navigation {
    position: relative;
    margin-top: 1.5rem
}

.entry-navigation .entry-page-next,.entry-navigation .entry-page-prev {
    overflow: hidden;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    min-height: 60px;
    border-radius: .25rem;
    background: #fff;
    -webkit-transition: all .3s;
    transition: all .3s
}

.entry-navigation .entry-page-next:hover,.entry-navigation .entry-page-prev:hover {
    -webkit-transform: translateY(-3px);
    transform: translateY(-3px);
    -webkit-box-shadow: 0 1rem 3rem rgba(31,45,61,.13);
    box-shadow: 0 1rem 3rem rgba(31,45,61,.13)
}

.entry-navigation .entry-page-icon {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    margin: 0;
    display: block;
    padding: 1rem;
    font-size: 1rem
}

.entry-navigation .entry-page-info {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    margin: 0;
    display: block;
    padding: 1rem;
    z-index: 1
}

.entry-navigation .entry-page-info .rnav {
    font-size: .875rem;
    color: #ccc
}

.entry-navigation .entry-page-info .title {
    font-size: 1rem;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    -webkit-line-clamp: 1
}

.entry-navigation .entry-page-prev .entry-page-icon {
    padding-right: 0
}

.entry-navigation .entry-page-prev .entry-page-info {
    text-align: right
}

.entry-navigation .entry-page-next .entry-page-icon {
    padding-left: 0
}

.entry-navigation .entry-page-next .entry-page-info {
    text-align: left
}

.related-posts {
    position: relative;
    margin-top: 1.5rem
}

.related-posts .related-posts-title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem
}

.share-body {
    position: relative;
    margin: -1.25rem
}

.share-body img {
    width: 100%
}

.share-body .share-qrcode {
    width: 100%;
    padding: 1rem
}

.share-body .share-url {
    -webkit-user-select: all!important;
    -moz-user-select: all!important;
    -ms-user-select: all!important;
    user-select: all!important;
    word-break: break-word;
    padding: .3rem 1rem;
    background: #31bfff;
    display: block;
    color: #fff;
    font-size: .875rem
}

.share-body .share-desc {
    font-size: .875rem;
    text-align: center;
    margin: 1rem 0
}

.share-author {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.share-author .avatar {
    height: 2rem;
    width: 2rem
}

#comments {
    position: relative;
    padding: 1.5rem;
    border-radius: .25rem;
    background-color: #fff;
    margin-top: 1.5rem
}

#comments .comments-title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem
}

#comments .comment-respond {
    position: relative
}

#comments .comment-reply-title {
    display: inline-block;
    margin: 0;
    font-size: .875rem;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 1;
    padding: .5rem;
    background-color: rgba(214,41,62,.1);
    border-radius: .3rem;
    line-height: 1
}

#comments .comment-reply-title #cancel-comment-reply-link {
    margin-left: .5rem;
    font-size: .875rem;
    text-decoration: underline
}

#comments .comment-form {
    display: block;
    position: relative;
    margin-top: 1rem;
    clear: both;
    content: ""
}

#comments .comment-form input[type=text],#comments .comment-form textarea {
    display: block;
    width: 100%;
    padding: .375rem .75rem;
    margin-bottom: .5rem;
    font-family: none;
    font-size: .9375rem;
    color: var(--ri-body-color);
    background-color: var(--ri-form-control-bg);
    background-clip: padding-box;
    border: var(--ri-border-width) solid var(--ri-border-color);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: .25rem;
    -webkit-transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out,-webkit-box-shadow .15s ease-in-out
}

#comments .comment-form input[type=text]:focus,#comments .comment-form textarea:focus {
    color: var(--ri-body-color);
    background-color: var(--ri-form-control-bg);
    border-color: #90b1f4;
    outline: 0;
    -webkit-box-shadow: 0 0 0 0 rgba(33,99,232,.25);
    box-shadow: 0 0 0 0 rgba(33,99,232,.25)
}

#comments .comment-form .comment-notes {
    font-size: .875rem;
    color: #595d69;
    margin-bottom: .5rem
}

#comments .comment-form .comment-form-author,#comments .comment-form .comment-form-email,#comments .comment-form .comment-form-url {
    width: calc(33.3333% - 10px);
    float: left;
    margin: 0 5px
}

#comments .comment-form .comment-form-author {
    margin-right: 10px;
    margin-left: 0
}

#comments .comment-form .comment-form-url {
    margin-right: 0;
    margin-left: 10px
}

#comments .form-submit {
    padding: 0;
    text-align: right;
    margin: 0
}

#comments .form-submit input[type=submit] {
    background-color: #191a1f;
    border: none;
    border-radius: 4px;
    color: #fff;
    cursor: pointer;
    font-size: .9375rem;
    padding: .25rem 1rem;
    text-transform: uppercase;
    -webkit-transition: all .3s ease;
    transition: all .3s ease
}

#comments .form-submit input[type=submit]:hover {
    background-color: rgba(25,26,31,.75)
}

#comments .form-submit input[type=submit]:disabled {
    opacity: .5;
    cursor: not-allowed
}

#comments .comments-list li,#comments .comments-list ul {
    margin: 0;
    list-style: none
}

#comments .comments-list>li+li {
    border-top: 1px solid rgba(89,93,105,.1)
}

#comments .comments-list .comment {
    padding: 1rem 0
}

#comments .comments-list .comment .comment-author img.avatar {
    border-radius: 50%;
    border: 3px solid rgba(0,0,0,.1);
    -webkit-box-shadow: 0 .125rem 1rem rgba(89,93,105,.075);
    box-shadow: 0 .125rem 1rem rgba(89,93,105,.075)
}

#comments .comments-list .comment .comment-inner {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

#comments .comments-list .comment .comment-meta {
    color: #a1a1a8;
    font-size: .875rem
}

#comments .comments-list .comment .comment-meta span {
    margin-right: 5px;
    font-size: 12px
}

#comments .comments-list .comment .comment-body {
    width: 100%;
    margin-left: 15px
}

#comments .comments-list .comment .comment-body .nickname {
    margin-bottom: .5rem
}

#comments .comments-list .comment .comment-body .nickname span {
    margin-left: 6px;
    font-size: 12px
}

#comments .comments-list .comment .comment-content {
    margin-bottom: 0
}

#comments .comments-list .comment .comment-reply-link {
    color: #a1a1a8;
    display: inline-block;
    text-decoration: underline;
    -webkit-transition: all .3s ease;
    transition: all .3s ease
}

#comments .comments-list .comment .comment-reply-link:hover {
    color: #333
}

#comments .comments-list .comment .children {
    margin-left: 3rem;
    background-color: rgba(161,161,168,.1);
    border-radius: .3rem;
    padding: .5rem;
    margin-top: 1rem
}

#comments .comments-list .comment .children li {
    padding: 0
}

#comments .comments-list .comment .children li+li {
    margin-top: 1rem
}

#comments .comments-list .comment .children .children {
    margin-left: 0
}

.single-content-nav {
    margin-bottom: 1.5rem
}

.single-content-nav .nav-pills {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
    border-bottom: solid 1px rgba(171,171,171,.35);
    margin: 0
}

.single-content-nav .nav-item {
    margin-bottom: .35rem
}

.single-content-nav .nav-item:not(:last-child) {
    margin-right: 1rem
}

.single-content-nav .nav-item .nav-link {
    font-weight: 600;
    padding: .4rem .5rem;
    border-radius: 0;
    border-bottom: solid 2px;
    border-color: transparent
}

.single-content-nav .nav-item .nav-link:hover {
    color: #ff9800
}

.single-content-nav .nav-item .nav-link.active {
    color: #ff9800;
    border-color: #ff9800;
    background-color: unset
}

.tab-content>.tab-pane {
    display: none
}

.tab-content>.active {
    display: block
}

.tab-content #comments {
    padding: 0
}

.vip-prices-page {
    background: #fff
}

.vip-prices-page .bg-shape {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    overflow: hidden;
    z-index: -1
}

.vip-prices-page .bg-shape svg {
    display: block;
    fill: #fff;
    pointer-events: none;
    vertical-align: baseline
}

.price-card {
    position: relative;
    background: rgba(112,183,255,.1);
    border-radius: .3rem;
    overflow: hidden;
    -webkit-box-shadow: 0 0 1.25rem rgba(31,45,61,.05);
    box-shadow: 0 0 1.25rem rgba(31,45,61,.05);
    border: solid 2px #eee;
    -webkit-transition: all .2s ease;
    transition: all .2s ease
}

.price-card:hover {
    -webkit-transform: translateY(-8px)!important;
    transform: translateY(-8px)!important
}

.price-card .price-header {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    padding: 1rem 0
}

.price-card .price-header .price-plan {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    color: #fff;
    font-size: 1.5rem;
    font-weight: 700;
    text-transform: capitalize;
    margin-bottom: .5rem;
    letter-spacing: 1.5px
}

.price-card .price-header .price-sub {
    font-size: .875rem;
    color: #fff
}

.price-card .price-body {
    padding: 1.5rem
}

.price-card .price-body .price-ammount {
    color: #000;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem
}

.price-card .price-body .price-ammount>sup {
    color: #737373;
    font-size: .875rem;
    font-weight: 500;
    position: relative;
    left: .125em;
    top: -.6em
}

.price-card .price-body .price-day {
    color: #ff6f00;
    margin-bottom: .5rem
}

.price-card .price-body .price-desc {
    list-style: none;
    padding: 0;
    margin: 0
}

.price-card .price-body .price-desc>li {
    margin-bottom: .5rem;
    color: #666
}

.price-card .price-footer {
    padding-bottom: 1.5rem;
    position: relative;
    z-index: 1
}

#price-input-warp {
    padding: .5rem;
    border: 1px dashed #ff9800;
    border-radius: .3rem
}

#down-input-warp,#video-input-warp {
    padding: .5rem;
    border: 1px dashed #cdcdcd;
    border-radius: .3rem
}

.down-input-item-add,.down-input-item-remove {
    cursor: pointer
}

.tougao_thumbnail,.video-input-item-add,.video-input-item-remove {
    cursor: pointer
}

.tougao_thumbnail {
    border: dashed 1px #c2c2c2;
    border-radius: .3rem;
    padding: .5rem;
    text-align: center
}

#post-form .wp-editor-tools .csf-shortcode-button {
    display: none
}

.media-modal {
    border-radius: .3rem;
    overflow: hidden
}

.media-modal .screen-reader-text,.media-modal .screen-reader-text span,.media-modal .ui-helper-hidden-accessible {
    border: 0;
    clip: rect(1px,1px,1px,1px);
    -webkit-clip-path: inset(50%);
    clip-path: inset(50%);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    word-wrap: normal!important
}

.media-modal .button .screen-reader-text {
    height: auto
}

.media-modal .search-form {
    width: auto;
    margin: 0 auto;
    margin: 0;
    border-radius: .3rem;
    background-color: unset
}

.media-modal .search-form:hover {
    -webkit-box-shadow: none;
    box-shadow: none
}

.media-modal #menu-item-gallery,.media-modal #menu-item-playlist,.media-modal #menu-item-video-playlist {
    display: none
}

.user-tougao-warp .post-item {
    border: solid 1px #eee
}

.user-tougao-warp .post-item:hover {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    -webkit-box-shadow: none;
    box-shadow: none
}

.user-tougao-warp .post-item.item-list .entry-media {
    max-width: 100px
}

.user-tougao-warp .post-item.item-list .entry-title {
    font-size: .875rem
}

.avatar-img {
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%;
    height: 100%
}

.avatar {
    height: 3rem;
    width: 3rem;
    position: relative;
    display: inline-block!important
}

.avatar-group {
    padding: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.avatar-group>li {
    position: relative
}

.avatar-group>li:not(:last-child) {
    margin-right: -.8rem
}

.avatar-group .avatar-img {
    border: 2px solid #fff
}

.avatar .avatar-name {
    margin-left: 7px
}

.avatar-xs {
    height: 1.5rem;
    width: 1.5rem
}

.avatar-sm {
    height: 2.5rem;
    width: 2.5rem
}

.avatar-lg {
    height: 4rem;
    width: 4rem
}

.avatar-xl {
    height: 5.125rem;
    width: 5.125rem
}

.avatar-xxl {
    height: 5.125rem;
    width: 5.125rem
}

.avatar-xxxl {
    height: 8rem;
    width: 8rem
}

::-webkit-scrollbar {
    background-color: rgba(25,25,25,.1);
    width: 8px;
    height: 8px
}

::-webkit-scrollbar-thumb {
    background-color: rgba(25,25,25,.8);
    border-radius: 6px;
    -webkit-transition: background .2s ease;
    transition: background .2s ease
}

.search-select ::-webkit-scrollbar {
    background-color: rgba(25,25,25,.1);
    width: 4px;
    height: 4px
}

.search-select ::-webkit-scrollbar-thumb {
    background-color: rgba(189,189,189,.8);
    border-radius: 6px;
    -webkit-transition: background .2s ease;
    transition: background .2s ease
}

.jarallax {
    position: relative;
    z-index: 0
}

.jarallax .jarallax-img {
    position: absolute;
    -o-object-fit: cover;
    object-fit: cover;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1
}

section {
    padding-top: 2rem;
    padding-bottom: 2rem
}

section+section {
    padding-top: 0
}

a {
    -webkit-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out
}

img,svg {
    vertical-align: middle;
    max-width: 100%
}



.bg-cover {
    background-size: cover!important;
    background-repeat: no-repeat!important
}

.bg-auto {
    background-size: auto!important;
    background-repeat: no-repeat!important
}

.bg-contain {
    background-size: contain!important;
    background-repeat: no-repeat!important
}

.bg-left-top {
    background-position: left top!important
}

.bg-left-bottom {
    background-position: left bottom!important
}

.bg-right-top {
    background-position: right top!important
}

.bg-right-bottom {
    background-position: right bottom!important
}

.bg-center {
    background-position: center!important
}

.bg-center-top {
    background-position: center top!important
}

.bg-center-bottom {
    background-position: center bottom!important
}

.search-form {
    width: 100%;
    margin: 0 auto;
    margin: 1rem auto;
    border-radius: .3rem;
     background-color: rgba(255 255 255 /30%);
    -webkit-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out;
    -webkit-box-shadow: 0 0 0 5px rgba(0,0,0,.1);
    box-shadow: 0 0 0 5px rgba(0,0,0,.1);
    border-radius: 60px
}

.search-form:hover {
    -webkit-box-shadow: 0 0 0 5px rgba(0,0,0,.3);
    box-shadow: 0 0 0 5px rgba(0,0,0,.3)
}

.search-form>form {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 100%
}

.search-form .search-select {
    max-width: 160px
}

.search-form .search-select .form-select {
    border: 0;
    border-radius: 0;
    font-size: 1rem;
    color: inherit;
    background-color: transparent;
    padding: .5rem 2.25rem .5rem 1rem
}

.search-form .search-select .form-select>.level-0,.search-form .search-select .form-select>option {
    font-size: 1rem
}

.search-form .search-select .form-select>.level-1,.search-form .search-select .form-select>.level-2 {
    font-size: .875rem
}

.search-form .search-fields {
    position: relative;
    overflow: hidden;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    font-size: 1rem
}

.search-form .search-fields input {
    position: relative;
    display: block;
    line-height: 1.5;
    padding: .5rem 2.25rem .5rem 1rem;
    width: 100%;
    border: 0;
    margin: 0;
    outline: 0;
    color: #ffffff;
    background-color: transparent;
    -webkit-appearance: none
}

.search-form .search-fields input::-webkit-input-placeholder {
    color: #a1a1a8
}

.search-form .search-fields button {
    position: absolute;
    right: 0;
    top: 0;
    border: none!important;
    background: 0 0!important;
    line-height: 1.5;
    height: 100%;
    padding: 0 1rem;
    opacity: .65
}

.search-form .search-fields button:hover {
    opacity: 1
}

.archive-hero {
    position: relative;
    z-index: 1
}

.archive-hero .archive-hero-bg {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 100%;
    width: 100%;
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: 120%;
    overflow: hidden;
    z-index: -1
}

.archive-hero .archive-hero-bg::before {
    content: "";
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background: rgba(0,0,0,.65);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px)
}

.archive-hero .archive-title {
    font-size: 1.25rem;
    font-weight: 900;
    margin: 0 auto;
    position: relative;
    text-align: center;
    color: #fff
}

.archive-hero .archive-desc {
    color: #dfe0e5
}

.archive-hero .archive-desc p {
    margin-bottom: 0
}

.archive-hero.post-hero .archive-hero-bg::before {
    background-image: -webkit-gradient(linear,left top,left bottom,from(rgba(0,0,0,.55)),to(rgba(255,255,255,.25)));
    background-image: linear-gradient(180deg,rgba(0,0,0,.55),rgba(255,255,255,.25))
}

.archive-hero.post-hero .post-title {
    color: #fff
}

.archive-hero.post-hero .article-meta a,.archive-hero.post-hero .article-meta span {
    color: rgba(255,255,255,.7)
}

.breadcrumb {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin: 0;
    padding: 0
}

.breadcrumb>li {
    margin-right: .8rem;
    position: relative;
    list-style: none
}

.breadcrumb>li:not(:last-child)::after {
    position: absolute;
    right: 0;
    top: 0;
    -webkit-transform: translate(.6rem,0);
    transform: translate(.6rem,0);
    content: "/"
}

.filter-warp {
    position: relative;
    background: #fff
}

.filter-warp ::-webkit-scrollbar,.filter-warp ::-webkit-scrollbar-thumb,.filter-warp ::-webkit-scrollbar-track {
    display: none
}

.filter-warp .filter-item {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    list-style: none;
    margin: 0;
    padding: 8px 0;
    border-bottom: 1px dashed rgba(25,26,31,.1);
    white-space: nowrap;
    overflow: hidden
}

.filter-warp .filter-item:last-child {
    border-bottom: none
}

.filter-warp .filter-item .filter-link,.filter-warp .filter-item .filter-name {
    margin-right: 4px;
    margin-top: 4px
}

.filter-warp .filter-item .filter-name {
    margin-right: 10px;
    color: #a1a1a8;
    min-width: auto;
    padding: 8px;
    line-height: 1;
    display: inline-block
}

.filter-warp .filter-item .filter-link {
    position: relative
}

.filter-warp .filter-item .filter-link>a {
    position: relative;
    padding: 8px;
    display: inline-block;
    line-height: 1;
    color: #595d69
}

.filter-warp .filter-item .filter-link.active>a,.filter-warp .filter-item .filter-link:hover>a {
    color: #ff6022
}

.filter-warp .filter-item .filter-link.active>a::after,.filter-warp .filter-item .filter-link:hover>a::after {
    content: "";
    display: block;
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 12px;
    margin-left: -6px;
    border-bottom: 2px solid #ff6022
}

.site-addswarp.pc.top {
    margin-bottom: 1.5rem
}

.site-addswarp.mobile.top {
    margin-bottom: 1rem
}

.site-addswarp.pc.bottum {
    margin-top: 1.5rem
}

.site-addswarp.mobile.bottum {
    margin-top: 1rem
}

#captcha-img {
    max-height: 40px;
    margin-left: 10px
}

.rollbar {
    position: fixed;
    right: 30px;
    bottom: 90px;
    z-index: 80;
    visibility: visible;
    opacity: 1;
    -webkit-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out
}

.rollbar .actions {
    position: relative;
    width: 100%;
    -webkit-box-shadow: 0 0 10px rgba(0,0,0,.1);
    box-shadow: 0 0 10px rgba(0,0,0,.1);
    border-radius: 5px;
    list-style-type: none;
    margin: 0;
    padding: 0;
    overflow: hidden
}

.rollbar .actions li {
    position: relative;
    background: #fff;
    -webkit-transition: all .2s;
    transition: all .2s
}

.rollbar .actions li>a {
    display: block;
    padding: 8px 0;
    cursor: pointer;
    position: relative;
    border: 0;
    opacity: 1;
    width: 45px;
    text-align: center;
    height: auto;
    color: #595d69;
    border-bottom: 1px solid #eee
}

.rollbar .actions li>a>i {
    font-size: 1.25rem;
    display: block;
    margin-bottom: 10px
}

.rollbar .actions li>a>span {
    font-size: .75rem;
    padding: 0 5px;
    letter-spacing: 3px;
    display: block;
    margin-top: 2px;
    text-align: center;
    line-height: 1.2;
    position: relative;
    color: #595d69
}

.rollbar .actions li:hover a {
    opacity: .75
}

.back-top {
    cursor: pointer;
    position: fixed!important;
    bottom: 30px;
    right: 30px;
    display: block;
    z-index: 80;
    visibility: hidden;
    opacity: 0;
    -webkit-transform: translateY(50%);
    transform: translateY(50%);
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
    background: #fff;
    color: #595d69;
    border-radius: 5px;
    width: 45px;
    height: 45px;
    line-height: 40px;
    text-align: center;
    -webkit-box-shadow: 0 0 10px rgba(0,0,0,.1);
    box-shadow: 0 0 10px rgba(0,0,0,.1)
}

.back-top i {
    font-size: 1.5rem;
    vertical-align: middle
}

.back-top:hover {
    color: #fff;
    background: rgba(33,99,232,.85)
}

.back-top.back-top-show {
    visibility: visible;
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0)
}

.site-footer {
    background: #fff;
    color: #595d69;
    font-size: .875rem
}

.site-footer a {
    color: #595d69
}

.site-footer a:hover {
    color: #a1a1a8
}

.site-footer .widget-title {
    font-size: .875rem;
    font-weight: 600
}

.site-footer .widget-links {
    margin-bottom: 0
}

.site-footer .footer-links {
    text-align: center
}

.site-footer .footer-links .h6,.site-footer .footer-links h6 {
    display: inline;
    white-space: nowrap;
    margin: 0 8px;
    margin-left: 0;
    color: #435686
}

.site-footer .footer-links ul {
    display: inline;
    margin: 0;
    padding: 0;
    list-style-type: none
}

.site-footer .footer-links li {
    display: inline;
    white-space: nowrap;
    margin: 0 8px;
    margin-left: 0
}

.m-navbar2 {
    display: none;
    z-index: 1040;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    padding: 5px 0
}

.m-navbar2:before {
    content: "1";
    color: transparent;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 1px;
    background-color: rgba(0,0,0,.1);
    display: block;
    overflow: hidden;
    -webkit-transform-origin: 50% 0;
    transform-origin: 50% 0;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.m-navbar2 ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    text-align: center;
    padding: 0;
    margin: 0;
    list-style: none;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.m-navbar2 li {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    text-align: center;
    font-size: 12px
}

.m-navbar2 li a {
    display: block;
    margin-top: 5px
}

.m-navbar2 li a>i {
    margin-bottom: 0;
    display: block;
    font-size: 20px
}

.site-zb-dump {
    position: relative;
    bottom: 0;
    width: 100%;
    z-index: 9999;
    margin: 0;
    border: dashed 2px #ff9189
}

.site-zb-dump .zb-dump-code {
    padding: 20px
}

.site-zb-dump .zb-dump-info {
    background: #000;
    width: 100%;
    color: #919191;
    padding: 0 10px;
    position: sticky;
    bottom: 0
}

.comments-nav,.page-nav {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.comments-nav .infinite-scroll-action,.page-nav .infinite-scroll-action {
    display: none;
    text-align: center
}

.comments-nav .infinite-scroll-msg,.page-nav .infinite-scroll-msg {
    display: none;
    margin: 0;
    padding: .375rem
}

.comments-nav .infinite-scroll-button,.page-nav .infinite-scroll-button {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.comments-nav .infinite-scroll-status,.page-nav .infinite-scroll-status {
    display: none;
    margin-right: 8px
}

.comments-nav.infinite-scroll .pagination,.page-nav.infinite-scroll .pagination {
    display: none
}

.comments-nav.infinite-scroll .infinite-scroll-action,.page-nav.infinite-scroll .infinite-scroll-action {
    display: block
}

.custom-nav .pagination a,.custom-nav .pagination>span {
    position: relative;
    display: block;
    line-height: 1.5;
    margin: .25rem;
    padding: var(--ri-pagination-padding-y) var(--ri-pagination-padding-x);
    font-size: var(--ri-pagination-font-size);
    color: var(--ri-pagination-color);
    background-color: var(--ri-pagination-bg);
    -webkit-transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    border-radius: var(--ri-pagination-border-radius)
}

.custom-nav .pagination a:hover,.custom-nav .pagination>span:hover {
    z-index: 2;
    color: var(--ri-pagination-hover-color);
    background-color: var(--ri-pagination-hover-bg)
}

.custom-nav .pagination a.current,.custom-nav .pagination>span.current {
    color: var(--ri-pagination-active-color);
    background-color: var(--ri-pagination-active-bg)
}

.custom-nav .pagination a.disabled,.custom-nav .pagination>span.disabled,.disabled>.custom-nav .pagination a,.disabled>.custom-nav .pagination>span {
    color: var(--ri-pagination-disabled-color);
    pointer-events: none;
    background-color: var(--ri-pagination-bg)
}

.pagination {
    --ri-pagination-padding-x: 0.75rem;
    --ri-pagination-padding-y: 0.375rem;
    --ri-pagination-font-size: 0.9375rem;
    --ri-pagination-color: var(--ri-link-color);
    --ri-pagination-bg: var(--ri-body-bg);
    --ri-pagination-border-width: 0;
    --ri-pagination-border-color: var(--ri-border-color);
    --ri-pagination-border-radius: var(--ri-border-radius);
    --ri-pagination-hover-color: var(--ri-link-hover-color);
    --ri-pagination-hover-bg: #dfe0e5;
    --ri-pagination-hover-border-color: var(--ri-border-color);
    --ri-pagination-focus-color: var(--ri-link-hover-color);
    --ri-pagination-focus-bg: var(--ri-secondary-bg);
    --ri-pagination-focus-box-shadow: 0 0 0 0 rgba(33, 99, 232, 0.25);
    --ri-pagination-active-color: #fff;
    --ri-pagination-active-bg: #191a1f;
    --ri-pagination-active-border-color: #2163e8;
    --ri-pagination-disabled-color: var(--ri-secondary-color);
    --ri-pagination-disabled-bg: var(--ri-secondary-bg);
    --ri-pagination-disabled-border-color: var(--ri-border-color);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin: 0;
    padding-left: 0;
    list-style: none
}

.page-link {
    position: relative;
    display: block;
    padding: var(--ri-pagination-padding-y) var(--ri-pagination-padding-x);
    font-size: var(--ri-pagination-font-size);
    color: var(--ri-pagination-color);
    background-color: var(--ri-pagination-bg);
    -webkit-transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out,-webkit-box-shadow .15s ease-in-out
}

.page-link:hover {
    z-index: 2;
    color: var(--ri-pagination-hover-color);
    background-color: var(--ri-pagination-hover-bg)
}

.page-link:focus {
    z-index: 3;
    color: var(--ri-pagination-focus-color);
    background-color: var(--ri-pagination-focus-bg);
    outline: 0;
    -webkit-box-shadow: var(--ri-pagination-focus-box-shadow);
    box-shadow: var(--ri-pagination-focus-box-shadow)
}

.active>.page-link,.page-link.active {
    z-index: 3;
    color: var(--ri-pagination-active-color);
    background-color: var(--ri-pagination-active-bg)
}

.disabled>.page-link,.page-link.disabled {
    color: var(--ri-pagination-disabled-color);
    pointer-events: none;
    background-color: var(--ri-pagination-bg)
}

.page-item:not(:first-child) .page-link {
    margin-left: 0
}

.page-item:first-child .page-link {
    border-top-left-radius: var(--ri-pagination-border-radius);
    border-bottom-left-radius: var(--ri-pagination-border-radius)
}

.page-item:last-child .page-link {
    border-top-right-radius: var(--ri-pagination-border-radius);
    border-bottom-right-radius: var(--ri-pagination-border-radius)
}

.pagination-lg {
    --ri-pagination-padding-x: 1.5rem;
    --ri-pagination-padding-y: 0.75rem;
    --ri-pagination-font-size: 1.171875rem;
    --ri-pagination-border-radius: 0.7rem
}

.pagination-sm {
    --ri-pagination-padding-x: 0.5rem;
    --ri-pagination-padding-y: 0.25rem;
    --ri-pagination-font-size: 0.8203125rem;
    --ri-pagination-border-radius: 0.2rem
}

pre code.hljs,pre.hljs {
    position: relative;
    display: block;
    overflow-x: auto;
    padding: 1.5rem 1rem;
    border-radius: .3rem;
    line-height: 1.5
}

.hljs-block-header {
    position: absolute;
    right: 0;
    top: 0;
    white-space: initial;
    font-family: initial
}

.hljs-block-header .hljs-block-copy {
    font-size: 13px;
    cursor: pointer;
    padding: 5px 10px;
    line-height: 1;
    background: #000;
    display: inline-block;
    border-radius: 0 0 0 5px;
    opacity: .5
}

.hljs-block-header .hljs-block-copy:hover {
    opacity: 1
}

code.hljs {
    padding: 3px 5px
}

.hljs {
    color: #e5e9f0;
    background: #2e3440
}

.hljs ::-moz-selection,.hljs::-moz-selection {
    background-color: #434c5e;
    color: #e5e9f0
}

.hljs ::selection,.hljs::selection {
    background-color: #434c5e;
    color: #e5e9f0
}

.hljs-comment {
    color: #4c566a
}

.hljs-tag {
    color: #d8dee9
}

.hljs-operator,.hljs-punctuation,.hljs-subst {
    color: #e5e9f0
}

.hljs-operator {
    opacity: .7
}

.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable {
    color: #bf616a
}

.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_ {
    color: #d08770
}

.hljs-class .hljs-title,.hljs-title,.hljs-title.class_ {
    color: #ebcb8b
}

.hljs-strong {
    font-weight: 700;
    color: #ebcb8b
}

.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__ {
    color: #a3be8c
}

.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp {
    color: #88c0d0
}

.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property {
    color: #81a1c1
}

.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type {
    color: #b48ead
}

.hljs-emphasis {
    color: #b48ead;
    font-style: italic
}

.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string {
    color: #5e81ac
}

.hljs-meta .hljs-keyword,.hljs-meta-keyword {
    font-weight: 700
}

.owl-carousel {
    display: none;
    width: 100%;
    -webkit-tap-highlight-color: transparent;
    position: relative;
    z-index: 1
}

.owl-carousel .owl-stage {
    position: relative;
    -ms-touch-action: pan-Y;
    touch-action: manipulation;
    -moz-backface-visibility: hidden
}

.owl-carousel .owl-stage:after {
    content: ".";
    display: block;
    clear: both;
    visibility: hidden;
    line-height: 0;
    height: 0
}

.owl-carousel .owl-stage-outer {
    position: relative;
    overflow: hidden;
    -webkit-transform: translate3d(0,0,0)
}

.owl-carousel .owl-item,.owl-carousel .owl-wrapper {
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -ms-backface-visibility: hidden;
    -webkit-transform: translate3d(0,0,0);
    -moz-transform: translate3d(0,0,0);
    -ms-transform: translate3d(0,0,0)
}

.owl-carousel .owl-item {
    position: relative;
    min-height: 1px;
    float: left;
    -webkit-backface-visibility: hidden;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none
}

.owl-carousel .owl-item img {
    display: block;
    width: 100%
}

.owl-carousel .owl-dots.disabled,.owl-carousel .owl-nav.disabled {
    display: none
}

.owl-carousel .owl-dot,.owl-carousel .owl-nav .owl-next,.owl-carousel .owl-nav .owl-prev {
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.owl-carousel .owl-nav button.owl-next,.owl-carousel .owl-nav button.owl-prev,.owl-carousel button.owl-dot {
    background: 0 0;
    color: inherit;
    border: none;
    padding: 0!important;
    font: inherit
}

.owl-carousel.owl-loaded {
    display: block
}

.owl-carousel.owl-loading {
    opacity: 0;
    display: block
}

.owl-carousel.owl-hidden {
    opacity: 0
}

.owl-carousel.owl-refresh .owl-item {
    visibility: hidden
}

.owl-carousel.owl-drag .owl-item {
    -ms-touch-action: pan-y;
    touch-action: pan-y;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.owl-carousel.owl-grab {
    cursor: move;
    cursor: -webkit-grab;
    cursor: grab
}

.owl-carousel.owl-rtl {
    direction: rtl
}

.owl-carousel.owl-rtl .owl-item {
    float: right
}

.no-js .owl-carousel {
    display: block
}

.owl-carousel .animated {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.owl-carousel .owl-animated-in {
    z-index: 0
}

.owl-carousel .owl-animated-out {
    z-index: 1
}

.owl-carousel .fadeOut {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut
}

.owl-height {
    -webkit-transition: height .5s ease-in-out;
    transition: height .5s ease-in-out
}

.owl-carousel .owl-item .owl-lazy {
    opacity: 0;
    -webkit-transition: opacity .4s ease;
    transition: opacity .4s ease
}

.owl-carousel .owl-item .owl-lazy:not([src]),.owl-carousel .owl-item .owl-lazy[src^=""] {
    max-height: 0
}

.owl-carousel .owl-item img.owl-lazy {
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d
}

.owl-carousel .owl-video-wrapper {
    position: relative;
    height: 100%;
    background: #000
}

.owl-carousel .owl-video-play-icon {
    position: absolute;
    height: 80px;
    width: 80px;
    left: 50%;
    top: 50%;
    margin-left: -40px;
    margin-top: -40px;
    background: url(owl.video.play.png) no-repeat;
    cursor: pointer;
    z-index: 1;
    -webkit-backface-visibility: hidden;
    -webkit-transition: -webkit-transform .1s ease;
    transition: -webkit-transform .1s ease;
    transition: transform .1s ease;
    transition: transform .1s ease,-webkit-transform .1s ease
}

.owl-carousel .owl-video-play-icon:hover {
    -webkit-transform: scale(1.3,1.3);
    transform: scale(1.3,1.3)
}

.owl-carousel .owl-video-playing .owl-video-play-icon,.owl-carousel .owl-video-playing .owl-video-tn {
    display: none
}

.owl-carousel .owl-video-tn {
    opacity: 0;
    height: 100%;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: contain;
    -webkit-transition: opacity .4s ease;
    transition: opacity .4s ease
}

.owl-carousel .owl-video-frame {
    position: relative;
    z-index: 1;
    height: 100%;
    width: 100%
}

.owl-theme .owl-nav {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: 100%;
    padding: 0 1rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-tap-highlight-color: transparent;
    pointer-events: none
}

.owl-theme .owl-nav [class*=owl-] {
    color: #fff;
    font-size: 14px;
    margin: 5px;
    padding: 4px 7px;
    background: rgba(0,0,0,.25);
    display: inline-block;
    cursor: pointer;
    border-radius: 3px;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
    pointer-events: auto
}

.owl-theme .owl-nav [class*=owl-]:hover {
    background: #03a9f4;
    text-decoration: none
}

.owl-theme .owl-nav .disabled {
    opacity: .5;
    cursor: default
}

.owl-theme .owl-nav.disabled+.owl-dots {
    margin-top: 10px
}

.owl-theme .owl-dots {
    position: absolute;
    bottom: .5rem;
    left: 50%;
    -webkit-transform: translate(-50%,-10%);
    transform: translate(-50%,-10%);
    text-align: center;
    background-color: rgba(0,0,0,.15);
    padding: .1rem .6rem;
    border-radius: 3rem;
    line-height: 0;
    -webkit-tap-highlight-color: transparent
}

.owl-theme .owl-dots .owl-dot {
    display: inline-block;
    zoom:1}

.owl-theme .owl-dots .owl-dot span {
    width: 8px;
    height: 8px;
    margin: 4px;
    background: #c4c4c4;
    display: block;
    -webkit-backface-visibility: visible;
    -webkit-transition: opacity .2s ease;
    transition: opacity .2s ease;
    border-radius: 30px;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.owl-theme .owl-dots .owl-dot.active span {
    background: #03a9f4;
    width: 30px
}

.owl-theme .owl-dots .owl-dot:hover span {
    background: #ff9800
}

[data-bs-theme=dark] ::-webkit-scrollbar-thumb {
    background-color: rgba(255,255,255,.8)
}

[data-bs-theme=dark] .site-header {
    background-color: #000;
    color: #a1a1a8
}

[data-bs-theme=dark] .navbar2 .nav-list a {
    color: #a1a1a8
}

[data-bs-theme=dark] .navbar2 .nav-list>.menu-item.current-menu-item>a {
    color: #7fa4f1
}

[data-bs-theme=dark] .navbar2 .actions .action-btn {
    color: #a1a1a8
}

[data-bs-theme=dark] .navbar2 .actions .action-btn.login-btn {
    background-color: #222529;
    color: #a1a1a8
}

[data-bs-theme=dark] .navbar2 .nav-list .sub-menu {
    background-color: #0f0f10
}

[data-bs-theme=dark] .navbar2 .nav-list .sub-menu:before {
    border-bottom-color: #0f0f10
}

[data-bs-theme=dark] .navbar2 .action-hover-menu .hover-info {
    background: linear-gradient(40deg,#0f0f10,#29292d,#1f1f24)
}

[data-bs-theme=dark] .navbar2 .action-hover-menu .hover-warp {
    background-color: #26262b
}

[data-bs-theme=dark] .navbar2 .action-hover-menu .hover-item .hover-link>a>i {
    background: #313131;
    color: #a1a1a8
}

[data-bs-theme=dark] .navbar2 .action-hover-menu a {
    color: #a1a1a8
}

[data-bs-theme=dark] .ri-popup-content {
    background: #191b1d
}

[data-bs-theme=dark] .search-form {
    background-color: #191b1d
}

[data-bs-theme=dark] .navbar2 .navbar2-search .search-form {
    color: #a1a1a8
}

[data-bs-theme=dark] .navbar2 .action-hover-menu .hover-warp .balance-qiandao>a {
    background-color: #191b1d
}

[data-bs-theme=dark] .search-form .search-fields button {
    color: #a1a1a8
}

[data-bs-theme=dark] .home-division .division-item {
    background-color: #191b1d
}

[data-bs-theme=dark] .home-title-post .list-posts ul {
    background-color: #191b1d
}

[data-bs-theme=dark] .post-item {
    background-color: #191b1d
}

[data-bs-theme=dark] .rollbar .actions li {
    background: #191b1d
}

[data-bs-theme=dark] .rollbar .actions li>a {
    color: #a1a1a8;
    border-color: #222529
}

[data-bs-theme=dark] .rollbar .actions li>a>span {
    color: #a1a1a8
}

[data-bs-theme=dark] .back-top {
    color: #a1a1a8;
    background: #191b1d
}

[data-bs-theme=dark] .site-footer {
    background: #191b1d
}

[data-bs-theme=dark] .site-footer,[data-bs-theme=dark] .site-footer a {
    color: #a1a1a8
}

[data-bs-theme=dark] .card {
    background-color: #191b1d
}

[data-bs-theme=dark] .sidebar .widget {
    background-color: #191b1d
}

[data-bs-theme=dark] .entry-tags>a {
    background-color: #222529;
    color: #a1a1a8
}

[data-bs-theme=dark] .entry-navigation .entry-page-next,[data-bs-theme=dark] .entry-navigation .entry-page-prev {
    background: #191b1d
}

[data-bs-theme=dark] #comments {
    background-color: #191b1d
}

[data-bs-theme=dark] #comments .form-submit input[type=submit] {
    background-color: #222529;
    color: #a1a1a8
}

[data-bs-theme=dark] .archive-hero.post-hero .archive-hero-bg::before {
    background: rgba(0,0,0,.6)
}

[data-bs-theme=dark] .archive-hero .archive-hero-bg::before {
    background: rgba(0,0,0,.6)
}

[data-bs-theme=dark] .home-search-box .search-bg-overlay {
    background: linear-gradient(10deg,rgba(25,27,38,.56),rgba(25,27,38,.75))
}

[data-bs-theme=dark] .bg-white {
    background-color: #191b1d!important
}

[data-bs-theme=dark] .text-dark {
    color: #a1a1a8!important
}

[data-bs-theme=dark] .uc-page .coin-balance-body {
    background: linear-gradient(50deg,#191b1d,#272b2f)
}

[data-bs-theme=dark] .uc-page .coin-pay-card {
    background: rgba(0,0,0,.5)
}

[data-bs-theme=dark] .price-card {
    background: rgba(25,27,29,.1);
    border-color: #222529
}

[data-bs-theme=dark] .user-tougao-warp .post-item {
    border-color: #222529
}

[data-bs-theme=dark] .filter-warp {
    background: #191b1d
}

[data-bs-theme=dark] .pay-select-box .pay-item {
    border-color: #222529
}

[data-bs-theme=dark] .post-content blockquote {
    background: #2a2c31;
    color: #a1a1a8
}

[data-bs-theme=dark] .ri-hide-warp {
    background-color: #2a2c31;
    border-color: #222529
}

[data-bs-theme=dark] .links-page-warp .link-item {
    background: #191b1d
}

[data-bs-theme=dark] .tags-page-warp .tag-item {
    background: #191b1d
}

[data-bs-theme=dark] .pay-body-html .desc {
    border-color: #191b1d;
    color: #a1a1a8
}



[data-bs-theme=dark] .slicknav_menu .slicknav_nav .menu-item>a {
    background: #191b1d
}

[data-bs-theme=dark] .slicknav_menu .slicknav_nav .sub-menu {
    background: rgba(25,27,29,.8)
}

[data-bs-theme=dark] .post-content .ri-down-warp {
    background-color: #222529;
    border-color: #2a2c31
}

[data-bs-theme=dark] .ri-down-warp .down-buy-warp .prices-info .price-item:before {
    background: rgba(255,255,255,.19)
}

[data-bs-theme=dark] .ri-down-warp .down-buy-warp .prices-info:before {
    border-color: transparent transparent #191b1d transparent
}

[data-bs-theme=dark] .ri-hide-warp.ri-reply-hide .hide-msg {
    background: #191b1d
}

[data-bs-theme=dark] .ri-hide-warp.ri-login-hide .hide-msg {
    background: #191b1d
}

[data-bs-theme=dark] .home-scratch-card .scratch-item .code-content {
    background-color: #191b1d
}

[data-bs-theme=dark] .price-card .price-body .price-ammount {
    color: #a1a1a8
}

[data-bs-theme=dark] .h-navList {
    background-color: #191b1d
}

[data-bs-theme=dark] .m-navbar2 {
    background-color: #191b1d
}

[data-bs-theme=dark] .home-last-post .section-cat-navbtn .btn {
    background-color: #191b1d;
    color: #a1a1a8
}

@media (prefers-reduced-motion:no-preference) {
    :root {
        scroll-behavior: smooth
    }
}

@media (prefers-reduced-motion:reduce) {
    .form-control {
        -webkit-transition: none;
        transition: none
    }

    .form-control::file-selector-button {
        -webkit-transition: none;
        transition: none
    }

    .form-select {
        -webkit-transition: none;
        transition: none
    }

    .form-switch .form-check-input {
        -webkit-transition: none;
        transition: none
    }

    .form-range::-webkit-slider-thumb {
        -webkit-transition: none;
        transition: none
    }

    .form-range::-moz-range-thumb {
        -moz-transition: none;
        transition: none
    }

    .form-floating>label {
        -webkit-transition: none;
        transition: none
    }

    .btn {
        -webkit-transition: none;
        transition: none
    }

    .fade {
        -webkit-transition: none;
        transition: none
    }

    .collapsing {
        -webkit-transition: none;
        transition: none
    }

    .collapsing.collapse-horizontal {
        -webkit-transition: none;
        transition: none
    }

    .spinner-border,.spinner-grow {
        --ri-spinner-animation-speed: 1.5s
    }

    #comments .comment-form input[type=text],#comments .comment-form textarea {
        -webkit-transition: none;
        transition: none
    }

    .custom-nav .pagination a,.custom-nav .pagination>span {
        -webkit-transition: none;
        transition: none
    }

    .page-link {
        -webkit-transition: none;
        transition: none
    }
}

@media (min-width: 576px) {
    .container,.container-sm {
        max-width:540px
    }

    .col-sm {
        -webkit-box-flex: 1;
        -ms-flex: 1 0 0%;
        flex: 1 0 0%
    }

    .row-cols-sm-auto>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }

    .row-cols-sm-1>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 100%
    }

    .row-cols-sm-2>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 50%
    }

    .row-cols-sm-3>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 33.3333333333%
    }

    .row-cols-sm-4>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 25%
    }

    .row-cols-sm-5>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 20%
    }

    .row-cols-sm-6>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 16.6666666667%
    }

    .col-sm-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }

    .col-sm-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 8.33333333%
    }

    .col-sm-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 16.66666667%
    }

    .col-sm-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 25%
    }

    .col-sm-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 33.33333333%
    }

    .col-sm-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 41.66666667%
    }

    .col-sm-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 50%
    }

    .col-sm-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 58.33333333%
    }

    .col-sm-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 66.66666667%
    }

    .col-sm-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 75%
    }

    .col-sm-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 83.33333333%
    }

    .col-sm-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 91.66666667%
    }

    .col-sm-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 100%
    }

    .offset-sm-0 {
        margin-left: 0
    }

    .offset-sm-1 {
        margin-left: 8.33333333%
    }

    .offset-sm-2 {
        margin-left: 16.66666667%
    }

    .offset-sm-3 {
        margin-left: 25%
    }

    .offset-sm-4 {
        margin-left: 33.33333333%
    }

    .offset-sm-5 {
        margin-left: 41.66666667%
    }

    .offset-sm-6 {
        margin-left: 50%
    }

    .offset-sm-7 {
        margin-left: 58.33333333%
    }

    .offset-sm-8 {
        margin-left: 66.66666667%
    }

    .offset-sm-9 {
        margin-left: 75%
    }

    .offset-sm-10 {
        margin-left: 83.33333333%
    }

    .offset-sm-11 {
        margin-left: 91.66666667%
    }

    .g-sm-0,.gx-sm-0 {
        --ri-gutter-x: 0
    }

    .g-sm-0,.gy-sm-0 {
        --ri-gutter-y: 0
    }

    .g-sm-1,.gx-sm-1 {
        --ri-gutter-x: 0.25rem
    }

    .g-sm-1,.gy-sm-1 {
        --ri-gutter-y: 0.25rem
    }

    .g-sm-2,.gx-sm-2 {
        --ri-gutter-x: 0.5rem
    }

    .g-sm-2,.gy-sm-2 {
        --ri-gutter-y: 0.5rem
    }

    .g-sm-3,.gx-sm-3 {
        --ri-gutter-x: 1rem
    }

    .g-sm-3,.gy-sm-3 {
        --ri-gutter-y: 1rem
    }

    .g-sm-4,.gx-sm-4 {
        --ri-gutter-x: 1.5rem
    }

    .g-sm-4,.gy-sm-4 {
        --ri-gutter-y: 1.5rem
    }

    .g-sm-5,.gx-sm-5 {
        --ri-gutter-x: 3rem
    }

    .g-sm-5,.gy-sm-5 {
        --ri-gutter-y: 3rem
    }

    .g-sm-6,.gx-sm-6 {
        --ri-gutter-x: 4.5rem
    }

    .g-sm-6,.gy-sm-6 {
        --ri-gutter-y: 4.5rem
    }

    .g-sm-7,.gx-sm-7 {
        --ri-gutter-x: 6rem
    }

    .g-sm-7,.gy-sm-7 {
        --ri-gutter-y: 6rem
    }

    .g-sm-8,.gx-sm-8 {
        --ri-gutter-x: 7.5rem
    }

    .g-sm-8,.gy-sm-8 {
        --ri-gutter-y: 7.5rem
    }

    .g-sm-9,.gx-sm-9 {
        --ri-gutter-x: 9rem
    }

    .g-sm-9,.gy-sm-9 {
        --ri-gutter-y: 9rem
    }

    .g-sm-10,.gx-sm-10 {
        --ri-gutter-x: 10.5rem
    }

    .g-sm-10,.gy-sm-10 {
        --ri-gutter-y: 10.5rem
    }

    .list-group-horizontal-sm {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row
    }

    .list-group-horizontal-sm>.list-group-item:first-child:not(:last-child) {
        border-bottom-left-radius: var(--ri-list-group-border-radius);
        border-top-right-radius: 0
    }

    .list-group-horizontal-sm>.list-group-item:last-child:not(:first-child) {
        border-top-right-radius: var(--ri-list-group-border-radius);
        border-bottom-left-radius: 0
    }

    .list-group-horizontal-sm>.list-group-item.active {
        margin-top: 0
    }

    .list-group-horizontal-sm>.list-group-item+.list-group-item {
        border-top-width: var(--ri-list-group-border-width);
        border-left-width: 0
    }

    .list-group-horizontal-sm>.list-group-item+.list-group-item.active {
        margin-left: calc(-1 * var(--ri-list-group-border-width));
        border-left-width: var(--ri-list-group-border-width)
    }

    .sticky-sm-top {
        position: sticky;
        top: 0;
        z-index: 1020
    }

    .sticky-sm-bottom {
        position: sticky;
        bottom: 0;
        z-index: 1020
    }

    .float-sm-start {
        float: left!important
    }

    .float-sm-end {
        float: right!important
    }

    .float-sm-none {
        float: none!important
    }

    .object-fit-sm-contain {
        -o-object-fit: contain!important;
        object-fit: contain!important
    }

    .object-fit-sm-cover {
        -o-object-fit: cover!important;
        object-fit: cover!important
    }

    .object-fit-sm-fill {
        -o-object-fit: fill!important;
        object-fit: fill!important
    }

    .object-fit-sm-scale {
        -o-object-fit: scale-down!important;
        object-fit: scale-down!important
    }

    .object-fit-sm-none {
        -o-object-fit: none!important;
        object-fit: none!important
    }

    .d-sm-inline {
        display: inline!important
    }

    .d-sm-inline-block {
        display: inline-block!important
    }

    .d-sm-block {
        display: block!important
    }

    .d-sm-grid {
        display: grid!important
    }

    .d-sm-table {
        display: table!important
    }

    .d-sm-table-row {
        display: table-row!important
    }

    .d-sm-table-cell {
        display: table-cell!important
    }

    .d-sm-flex {
        display: -webkit-box!important;
        display: -ms-flexbox!important;
        display: flex!important
    }

    .d-sm-inline-flex {
        display: -webkit-inline-box!important;
        display: -ms-inline-flexbox!important;
        display: inline-flex!important
    }

    .d-sm-none {
        display: none!important
    }

    .flex-sm-fill {
        -webkit-box-flex: 1!important;
        -ms-flex: 1 1 auto!important;
        flex: 1 1 auto!important
    }

    .flex-sm-row {
        -webkit-box-orient: horizontal!important;
        -webkit-box-direction: normal!important;
        -ms-flex-direction: row!important;
        flex-direction: row!important
    }

    .flex-sm-column {
        -webkit-box-orient: vertical!important;
        -webkit-box-direction: normal!important;
        -ms-flex-direction: column!important;
        flex-direction: column!important
    }

    .flex-sm-row-reverse {
        -webkit-box-orient: horizontal!important;
        -webkit-box-direction: reverse!important;
        -ms-flex-direction: row-reverse!important;
        flex-direction: row-reverse!important
    }

    .flex-sm-column-reverse {
        -webkit-box-orient: vertical!important;
        -webkit-box-direction: reverse!important;
        -ms-flex-direction: column-reverse!important;
        flex-direction: column-reverse!important
    }

    .flex-sm-grow-0 {
        -webkit-box-flex: 0!important;
        -ms-flex-positive: 0!important;
        flex-grow: 0!important
    }

    .flex-sm-grow-1 {
        -webkit-box-flex: 1!important;
        -ms-flex-positive: 1!important;
        flex-grow: 1!important
    }

    .flex-sm-shrink-0 {
        -ms-flex-negative: 0!important;
        flex-shrink: 0!important
    }

    .flex-sm-shrink-1 {
        -ms-flex-negative: 1!important;
        flex-shrink: 1!important
    }

    .flex-sm-wrap {
        -ms-flex-wrap: wrap!important;
        flex-wrap: wrap!important
    }

    .flex-sm-nowrap {
        -ms-flex-wrap: nowrap!important;
        flex-wrap: nowrap!important
    }

    .flex-sm-wrap-reverse {
        -ms-flex-wrap: wrap-reverse!important;
        flex-wrap: wrap-reverse!important
    }

    .justify-content-sm-start {
        -webkit-box-pack: start!important;
        -ms-flex-pack: start!important;
        justify-content: flex-start!important
    }

    .justify-content-sm-end {
        -webkit-box-pack: end!important;
        -ms-flex-pack: end!important;
        justify-content: flex-end!important
    }

    .justify-content-sm-center {
        -webkit-box-pack: center!important;
        -ms-flex-pack: center!important;
        justify-content: center!important
    }

    .justify-content-sm-between {
        -webkit-box-pack: justify!important;
        -ms-flex-pack: justify!important;
        justify-content: space-between!important
    }

    .justify-content-sm-around {
        -ms-flex-pack: distribute!important;
        justify-content: space-around!important
    }

    .justify-content-sm-evenly {
        -webkit-box-pack: space-evenly!important;
        -ms-flex-pack: space-evenly!important;
        justify-content: space-evenly!important
    }

    .align-items-sm-start {
        -webkit-box-align: start!important;
        -ms-flex-align: start!important;
        align-items: flex-start!important
    }

    .align-items-sm-end {
        -webkit-box-align: end!important;
        -ms-flex-align: end!important;
        align-items: flex-end!important
    }

    .align-items-sm-center {
        -webkit-box-align: center!important;
        -ms-flex-align: center!important;
        align-items: center!important
    }

    .align-items-sm-baseline {
        -webkit-box-align: baseline!important;
        -ms-flex-align: baseline!important;
        align-items: baseline!important
    }

    .align-items-sm-stretch {
        -webkit-box-align: stretch!important;
        -ms-flex-align: stretch!important;
        align-items: stretch!important
    }

    .align-content-sm-start {
        -ms-flex-line-pack: start!important;
        align-content: flex-start!important
    }

    .align-content-sm-end {
        -ms-flex-line-pack: end!important;
        align-content: flex-end!important
    }

    .align-content-sm-center {
        -ms-flex-line-pack: center!important;
        align-content: center!important
    }

    .align-content-sm-between {
        -ms-flex-line-pack: justify!important;
        align-content: space-between!important
    }

    .align-content-sm-around {
        -ms-flex-line-pack: distribute!important;
        align-content: space-around!important
    }

    .align-content-sm-stretch {
        -ms-flex-line-pack: stretch!important;
        align-content: stretch!important
    }

    .align-self-sm-auto {
        -ms-flex-item-align: auto!important;
        align-self: auto!important
    }

    .align-self-sm-start {
        -ms-flex-item-align: start!important;
        align-self: flex-start!important
    }

    .align-self-sm-end {
        -ms-flex-item-align: end!important;
        align-self: flex-end!important
    }

    .align-self-sm-center {
        -ms-flex-item-align: center!important;
        align-self: center!important
    }

    .align-self-sm-baseline {
        -ms-flex-item-align: baseline!important;
        align-self: baseline!important
    }

    .align-self-sm-stretch {
        -ms-flex-item-align: stretch!important;
        align-self: stretch!important
    }

    .order-sm-first {
        -webkit-box-ordinal-group: 0!important;
        -ms-flex-order: -1!important;
        order: -1!important
    }

    .order-sm-0 {
        -webkit-box-ordinal-group: 1!important;
        -ms-flex-order: 0!important;
        order: 0!important
    }

    .order-sm-1 {
        -webkit-box-ordinal-group: 2!important;
        -ms-flex-order: 1!important;
        order: 1!important
    }

    .order-sm-2 {
        -webkit-box-ordinal-group: 3!important;
        -ms-flex-order: 2!important;
        order: 2!important
    }

    .order-sm-3 {
        -webkit-box-ordinal-group: 4!important;
        -ms-flex-order: 3!important;
        order: 3!important
    }

    .order-sm-4 {
        -webkit-box-ordinal-group: 5!important;
        -ms-flex-order: 4!important;
        order: 4!important
    }

    .order-sm-5 {
        -webkit-box-ordinal-group: 6!important;
        -ms-flex-order: 5!important;
        order: 5!important
    }

    .order-sm-last {
        -webkit-box-ordinal-group: 7!important;
        -ms-flex-order: 6!important;
        order: 6!important
    }

    .m-sm-0 {
        margin: 0!important
    }

    .m-sm-1 {
        margin: .25rem!important
    }

    .m-sm-2 {
        margin: .5rem!important
    }

    .m-sm-3 {
        margin: 1rem!important
    }

    .m-sm-4 {
        margin: 1.5rem!important
    }

    .m-sm-5 {
        margin: 3rem!important
    }

    .m-sm-6 {
        margin: 4.5rem!important
    }

    .m-sm-7 {
        margin: 6rem!important
    }

    .m-sm-8 {
        margin: 7.5rem!important
    }

    .m-sm-9 {
        margin: 9rem!important
    }

    .m-sm-10 {
        margin: 10.5rem!important
    }

    .m-sm-auto {
        margin: auto!important
    }

    .mx-sm-0 {
        margin-right: 0!important;
        margin-left: 0!important
    }

    .mx-sm-1 {
        margin-right: .25rem!important;
        margin-left: .25rem!important
    }

    .mx-sm-2 {
        margin-right: .5rem!important;
        margin-left: .5rem!important
    }

    .mx-sm-3 {
        margin-right: 1rem!important;
        margin-left: 1rem!important
    }

    .mx-sm-4 {
        margin-right: 1.5rem!important;
        margin-left: 1.5rem!important
    }

    .mx-sm-5 {
        margin-right: 3rem!important;
        margin-left: 3rem!important
    }

    .mx-sm-6 {
        margin-right: 4.5rem!important;
        margin-left: 4.5rem!important
    }

    .mx-sm-7 {
        margin-right: 6rem!important;
        margin-left: 6rem!important
    }

    .mx-sm-8 {
        margin-right: 7.5rem!important;
        margin-left: 7.5rem!important
    }

    .mx-sm-9 {
        margin-right: 9rem!important;
        margin-left: 9rem!important
    }

    .mx-sm-10 {
        margin-right: 10.5rem!important;
        margin-left: 10.5rem!important
    }

    .mx-sm-auto {
        margin-right: auto!important;
        margin-left: auto!important
    }

    .my-sm-0 {
        margin-top: 0!important;
        margin-bottom: 0!important
    }

    .my-sm-1 {
        margin-top: .25rem!important;
        margin-bottom: .25rem!important
    }

    .my-sm-2 {
        margin-top: .5rem!important;
        margin-bottom: .5rem!important
    }

    .my-sm-3 {
        margin-top: 1rem!important;
        margin-bottom: 1rem!important
    }

    .my-sm-4 {
        margin-top: 1.5rem!important;
        margin-bottom: 1.5rem!important
    }

    .my-sm-5 {
        margin-top: 3rem!important;
        margin-bottom: 3rem!important
    }

    .my-sm-6 {
        margin-top: 4.5rem!important;
        margin-bottom: 4.5rem!important
    }

    .my-sm-7 {
        margin-top: 6rem!important;
        margin-bottom: 6rem!important
    }

    .my-sm-8 {
        margin-top: 7.5rem!important;
        margin-bottom: 7.5rem!important
    }

    .my-sm-9 {
        margin-top: 9rem!important;
        margin-bottom: 9rem!important
    }

    .my-sm-10 {
        margin-top: 10.5rem!important;
        margin-bottom: 10.5rem!important
    }

    .my-sm-auto {
        margin-top: auto!important;
        margin-bottom: auto!important
    }

    .mt-sm-0 {
        margin-top: 0!important
    }

    .mt-sm-1 {
        margin-top: .25rem!important
    }

    .mt-sm-2 {
        margin-top: .5rem!important
    }

    .mt-sm-3 {
        margin-top: 1rem!important
    }

    .mt-sm-4 {
        margin-top: 1.5rem!important
    }

    .mt-sm-5 {
        margin-top: 3rem!important
    }

    .mt-sm-6 {
        margin-top: 4.5rem!important
    }

    .mt-sm-7 {
        margin-top: 6rem!important
    }

    .mt-sm-8 {
        margin-top: 7.5rem!important
    }

    .mt-sm-9 {
        margin-top: 9rem!important
    }

    .mt-sm-10 {
        margin-top: 10.5rem!important
    }

    .mt-sm-auto {
        margin-top: auto!important
    }

    .me-sm-0 {
        margin-right: 0!important
    }

    .me-sm-1 {
        margin-right: .25rem!important
    }

    .me-sm-2 {
        margin-right: .5rem!important
    }

    .me-sm-3 {
        margin-right: 1rem!important
    }

    .me-sm-4 {
        margin-right: 1.5rem!important
    }

    .me-sm-5 {
        margin-right: 3rem!important
    }

    .me-sm-6 {
        margin-right: 4.5rem!important
    }

    .me-sm-7 {
        margin-right: 6rem!important
    }

    .me-sm-8 {
        margin-right: 7.5rem!important
    }

    .me-sm-9 {
        margin-right: 9rem!important
    }

    .me-sm-10 {
        margin-right: 10.5rem!important
    }

    .me-sm-auto {
        margin-right: auto!important
    }

    .mb-sm-0 {
        margin-bottom: 0!important
    }

    .mb-sm-1 {
        margin-bottom: .25rem!important
    }

    .mb-sm-2 {
        margin-bottom: .5rem!important
    }

    .mb-sm-3 {
        margin-bottom: 1rem!important
    }

    .mb-sm-4 {
        margin-bottom: 1.5rem!important
    }

    .mb-sm-5 {
        margin-bottom: 3rem!important
    }

    .mb-sm-6 {
        margin-bottom: 4.5rem!important
    }

    .mb-sm-7 {
        margin-bottom: 6rem!important
    }

    .mb-sm-8 {
        margin-bottom: 7.5rem!important
    }

    .mb-sm-9 {
        margin-bottom: 9rem!important
    }

    .mb-sm-10 {
        margin-bottom: 10.5rem!important
    }

    .mb-sm-auto {
        margin-bottom: auto!important
    }

    .ms-sm-0 {
        margin-left: 0!important
    }

    .ms-sm-1 {
        margin-left: .25rem!important
    }

    .ms-sm-2 {
        margin-left: .5rem!important
    }

    .ms-sm-3 {
        margin-left: 1rem!important
    }

    .ms-sm-4 {
        margin-left: 1.5rem!important
    }

    .ms-sm-5 {
        margin-left: 3rem!important
    }

    .ms-sm-6 {
        margin-left: 4.5rem!important
    }

    .ms-sm-7 {
        margin-left: 6rem!important
    }

    .ms-sm-8 {
        margin-left: 7.5rem!important
    }

    .ms-sm-9 {
        margin-left: 9rem!important
    }

    .ms-sm-10 {
        margin-left: 10.5rem!important
    }

    .ms-sm-auto {
        margin-left: auto!important
    }

    .p-sm-0 {
        padding: 0!important
    }

    .p-sm-1 {
        padding: .25rem!important
    }

    .p-sm-2 {
        padding: .5rem!important
    }

    .p-sm-3 {
        padding: 1rem!important
    }

    .p-sm-4 {
        padding: 1.5rem!important
    }

    .p-sm-5 {
        padding: 3rem!important
    }

    .p-sm-6 {
        padding: 4.5rem!important
    }

    .p-sm-7 {
        padding: 6rem!important
    }

    .p-sm-8 {
        padding: 7.5rem!important
    }

    .p-sm-9 {
        padding: 9rem!important
    }

    .p-sm-10 {
        padding: 10.5rem!important
    }

    .px-sm-0 {
        padding-right: 0!important;
        padding-left: 0!important
    }

    .px-sm-1 {
        padding-right: .25rem!important;
        padding-left: .25rem!important
    }

    .px-sm-2 {
        padding-right: .5rem!important;
        padding-left: .5rem!important
    }

    .px-sm-3 {
        padding-right: 1rem!important;
        padding-left: 1rem!important
    }

    .px-sm-4 {
        padding-right: 1.5rem!important;
        padding-left: 1.5rem!important
    }

    .px-sm-5 {
        padding-right: 3rem!important;
        padding-left: 3rem!important
    }

    .px-sm-6 {
        padding-right: 4.5rem!important;
        padding-left: 4.5rem!important
    }

    .px-sm-7 {
        padding-right: 6rem!important;
        padding-left: 6rem!important
    }

    .px-sm-8 {
        padding-right: 7.5rem!important;
        padding-left: 7.5rem!important
    }

    .px-sm-9 {
        padding-right: 9rem!important;
        padding-left: 9rem!important
    }

    .px-sm-10 {
        padding-right: 10.5rem!important;
        padding-left: 10.5rem!important
    }

    .py-sm-0 {
        padding-top: 0!important;
        padding-bottom: 0!important
    }

    .py-sm-1 {
        padding-top: .25rem!important;
        padding-bottom: .25rem!important
    }

    .py-sm-2 {
        padding-top: .5rem!important;
        padding-bottom: .5rem!important
    }

    .py-sm-3 {
        padding-top: 1rem!important;
        padding-bottom: 1rem!important
    }

    .py-sm-4 {
        padding-top: 1.5rem!important;
        padding-bottom: 1.5rem!important
    }

    .py-sm-5 {
        padding-top: 3rem!important;
        padding-bottom: 3rem!important
    }

    .py-sm-6 {
        padding-top: 4.5rem!important;
        padding-bottom: 4.5rem!important
    }

    .py-sm-7 {
        padding-top: 6rem!important;
        padding-bottom: 6rem!important
    }

    .py-sm-8 {
        padding-top: 7.5rem!important;
        padding-bottom: 7.5rem!important
    }

    .py-sm-9 {
        padding-top: 9rem!important;
        padding-bottom: 9rem!important
    }

    .py-sm-10 {
        padding-top: 10.5rem!important;
        padding-bottom: 10.5rem!important
    }

    .pt-sm-0 {
        padding-top: 0!important
    }

    .pt-sm-1 {
        padding-top: .25rem!important
    }

    .pt-sm-2 {
        padding-top: .5rem!important
    }

    .pt-sm-3 {
        padding-top: 1rem!important
    }

    .pt-sm-4 {
        padding-top: 1.5rem!important
    }

    .pt-sm-5 {
        padding-top: 3rem!important
    }

    .pt-sm-6 {
        padding-top: 4.5rem!important
    }

    .pt-sm-7 {
        padding-top: 6rem!important
    }

    .pt-sm-8 {
        padding-top: 7.5rem!important
    }

    .pt-sm-9 {
        padding-top: 9rem!important
    }

    .pt-sm-10 {
        padding-top: 10.5rem!important
    }

    .pe-sm-0 {
        padding-right: 0!important
    }

    .pe-sm-1 {
        padding-right: .25rem!important
    }

    .pe-sm-2 {
        padding-right: .5rem!important
    }

    .pe-sm-3 {
        padding-right: 1rem!important
    }

    .pe-sm-4 {
        padding-right: 1.5rem!important
    }

    .pe-sm-5 {
        padding-right: 3rem!important
    }

    .pe-sm-6 {
        padding-right: 4.5rem!important
    }

    .pe-sm-7 {
        padding-right: 6rem!important
    }

    .pe-sm-8 {
        padding-right: 7.5rem!important
    }

    .pe-sm-9 {
        padding-right: 9rem!important
    }

    .pe-sm-10 {
        padding-right: 10.5rem!important
    }

    .pb-sm-0 {
        padding-bottom: 0!important
    }

    .pb-sm-1 {
        padding-bottom: .25rem!important
    }

    .pb-sm-2 {
        padding-bottom: .5rem!important
    }

    .pb-sm-3 {
        padding-bottom: 1rem!important
    }

    .pb-sm-4 {
        padding-bottom: 1.5rem!important
    }

    .pb-sm-5 {
        padding-bottom: 3rem!important
    }

    .pb-sm-6 {
        padding-bottom: 4.5rem!important
    }

    .pb-sm-7 {
        padding-bottom: 6rem!important
    }

    .pb-sm-8 {
        padding-bottom: 7.5rem!important
    }

    .pb-sm-9 {
        padding-bottom: 9rem!important
    }

    .pb-sm-10 {
        padding-bottom: 10.5rem!important
    }

    .ps-sm-0 {
        padding-left: 0!important
    }

    .ps-sm-1 {
        padding-left: .25rem!important
    }

    .ps-sm-2 {
        padding-left: .5rem!important
    }

    .ps-sm-3 {
        padding-left: 1rem!important
    }

    .ps-sm-4 {
        padding-left: 1.5rem!important
    }

    .ps-sm-5 {
        padding-left: 3rem!important
    }

    .ps-sm-6 {
        padding-left: 4.5rem!important
    }

    .ps-sm-7 {
        padding-left: 6rem!important
    }

    .ps-sm-8 {
        padding-left: 7.5rem!important
    }

    .ps-sm-9 {
        padding-left: 9rem!important
    }

    .ps-sm-10 {
        padding-left: 10.5rem!important
    }

    .gap-sm-0 {
        gap: 0!important
    }

    .gap-sm-1 {
        gap: .25rem!important
    }

    .gap-sm-2 {
        gap: .5rem!important
    }

    .gap-sm-3 {
        gap: 1rem!important
    }

    .gap-sm-4 {
        gap: 1.5rem!important
    }

    .gap-sm-5 {
        gap: 3rem!important
    }

    .gap-sm-6 {
        gap: 4.5rem!important
    }

    .gap-sm-7 {
        gap: 6rem!important
    }

    .gap-sm-8 {
        gap: 7.5rem!important
    }

    .gap-sm-9 {
        gap: 9rem!important
    }

    .gap-sm-10 {
        gap: 10.5rem!important
    }

    .row-gap-sm-0 {
        row-gap: 0!important
    }

    .row-gap-sm-1 {
        row-gap: .25rem!important
    }

    .row-gap-sm-2 {
        row-gap: .5rem!important
    }

    .row-gap-sm-3 {
        row-gap: 1rem!important
    }

    .row-gap-sm-4 {
        row-gap: 1.5rem!important
    }

    .row-gap-sm-5 {
        row-gap: 3rem!important
    }

    .row-gap-sm-6 {
        row-gap: 4.5rem!important
    }

    .row-gap-sm-7 {
        row-gap: 6rem!important
    }

    .row-gap-sm-8 {
        row-gap: 7.5rem!important
    }

    .row-gap-sm-9 {
        row-gap: 9rem!important
    }

    .row-gap-sm-10 {
        row-gap: 10.5rem!important
    }

    .column-gap-sm-0 {
        -webkit-column-gap: 0!important;
        -moz-column-gap: 0!important;
        column-gap: 0!important
    }

    .column-gap-sm-1 {
        -webkit-column-gap: .25rem!important;
        -moz-column-gap: .25rem!important;
        column-gap: .25rem!important
    }

    .column-gap-sm-2 {
        -webkit-column-gap: .5rem!important;
        -moz-column-gap: .5rem!important;
        column-gap: .5rem!important
    }

    .column-gap-sm-3 {
        -webkit-column-gap: 1rem!important;
        -moz-column-gap: 1rem!important;
        column-gap: 1rem!important
    }

    .column-gap-sm-4 {
        -webkit-column-gap: 1.5rem!important;
        -moz-column-gap: 1.5rem!important;
        column-gap: 1.5rem!important
    }

    .column-gap-sm-5 {
        -webkit-column-gap: 3rem!important;
        -moz-column-gap: 3rem!important;
        column-gap: 3rem!important
    }

    .column-gap-sm-6 {
        -webkit-column-gap: 4.5rem!important;
        -moz-column-gap: 4.5rem!important;
        column-gap: 4.5rem!important
    }

    .column-gap-sm-7 {
        -webkit-column-gap: 6rem!important;
        -moz-column-gap: 6rem!important;
        column-gap: 6rem!important
    }

    .column-gap-sm-8 {
        -webkit-column-gap: 7.5rem!important;
        -moz-column-gap: 7.5rem!important;
        column-gap: 7.5rem!important
    }

    .column-gap-sm-9 {
        -webkit-column-gap: 9rem!important;
        -moz-column-gap: 9rem!important;
        column-gap: 9rem!important
    }

    .column-gap-sm-10 {
        -webkit-column-gap: 10.5rem!important;
        -moz-column-gap: 10.5rem!important;
        column-gap: 10.5rem!important
    }

    .text-sm-start {
        text-align: left!important
    }

    .text-sm-end {
        text-align: right!important
    }

    .text-sm-center {
        text-align: center!important
    }
}

@media (min-width: 768px) {
    .container,.container-md,.container-sm {
        max-width:720px
    }

    .col-md {
        -webkit-box-flex: 1;
        -ms-flex: 1 0 0%;
        flex: 1 0 0%
    }

    .row-cols-md-auto>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }

    .row-cols-md-1>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 100%
    }

    .row-cols-md-2>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 50%
    }

    .row-cols-md-3>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 33.3333333333%
    }

    .row-cols-md-4>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 25%
    }

    .row-cols-md-5>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 20%
    }

    .row-cols-md-6>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 16.6666666667%
    }

    .col-md-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }

    .col-md-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 8.33333333%
    }

    .col-md-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 16.66666667%
    }

    .col-md-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 25%
    }

    .col-md-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 33.33333333%
    }

    .col-md-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 41.66666667%
    }

    .col-md-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 50%
    }

    .col-md-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 58.33333333%
    }

    .col-md-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 66.66666667%
    }

    .col-md-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 75%
    }

    .col-md-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 83.33333333%
    }

    .col-md-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 91.66666667%
    }

    .col-md-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 100%
    }

    .offset-md-0 {
        margin-left: 0
    }

    .offset-md-1 {
        margin-left: 8.33333333%
    }

    .offset-md-2 {
        margin-left: 16.66666667%
    }

    .offset-md-3 {
        margin-left: 25%
    }

    .offset-md-4 {
        margin-left: 33.33333333%
    }

    .offset-md-5 {
        margin-left: 41.66666667%
    }

    .offset-md-6 {
        margin-left: 50%
    }

    .offset-md-7 {
        margin-left: 58.33333333%
    }

    .offset-md-8 {
        margin-left: 66.66666667%
    }

    .offset-md-9 {
        margin-left: 75%
    }

    .offset-md-10 {
        margin-left: 83.33333333%
    }

    .offset-md-11 {
        margin-left: 91.66666667%
    }

    .g-md-0,.gx-md-0 {
        --ri-gutter-x: 0
    }

    .g-md-0,.gy-md-0 {
        --ri-gutter-y: 0
    }

    .g-md-1,.gx-md-1 {
        --ri-gutter-x: 0.25rem
    }

    .g-md-1,.gy-md-1 {
        --ri-gutter-y: 0.25rem
    }

    .g-md-2,.gx-md-2 {
        --ri-gutter-x: 0.5rem
    }

    .g-md-2,.gy-md-2 {
        --ri-gutter-y: 0.5rem
    }

    .g-md-3,.gx-md-3 {
        --ri-gutter-x: 1rem
    }

    .g-md-3,.gy-md-3 {
        --ri-gutter-y: 1rem
    }

    .g-md-4,.gx-md-4 {
        --ri-gutter-x: 1.5rem
    }

    .g-md-4,.gy-md-4 {
        --ri-gutter-y: 1.5rem
    }

    .g-md-5,.gx-md-5 {
        --ri-gutter-x: 3rem
    }

    .g-md-5,.gy-md-5 {
        --ri-gutter-y: 3rem
    }

    .g-md-6,.gx-md-6 {
        --ri-gutter-x: 4.5rem
    }

    .g-md-6,.gy-md-6 {
        --ri-gutter-y: 4.5rem
    }

    .g-md-7,.gx-md-7 {
        --ri-gutter-x: 6rem
    }

    .g-md-7,.gy-md-7 {
        --ri-gutter-y: 6rem
    }

    .g-md-8,.gx-md-8 {
        --ri-gutter-x: 7.5rem
    }

    .g-md-8,.gy-md-8 {
        --ri-gutter-y: 7.5rem
    }

    .g-md-9,.gx-md-9 {
        --ri-gutter-x: 9rem
    }

    .g-md-9,.gy-md-9 {
        --ri-gutter-y: 9rem
    }

    .g-md-10,.gx-md-10 {
        --ri-gutter-x: 10.5rem
    }

    .g-md-10,.gy-md-10 {
        --ri-gutter-y: 10.5rem
    }

    .list-group-horizontal-md {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row
    }

    .list-group-horizontal-md>.list-group-item:first-child:not(:last-child) {
        border-bottom-left-radius: var(--ri-list-group-border-radius);
        border-top-right-radius: 0
    }

    .list-group-horizontal-md>.list-group-item:last-child:not(:first-child) {
        border-top-right-radius: var(--ri-list-group-border-radius);
        border-bottom-left-radius: 0
    }

    .list-group-horizontal-md>.list-group-item.active {
        margin-top: 0
    }

    .list-group-horizontal-md>.list-group-item+.list-group-item {
        border-top-width: var(--ri-list-group-border-width);
        border-left-width: 0
    }

    .list-group-horizontal-md>.list-group-item+.list-group-item.active {
        margin-left: calc(-1 * var(--ri-list-group-border-width));
        border-left-width: var(--ri-list-group-border-width)
    }

    .sticky-md-top {
        position: sticky;
        top: 0;
        z-index: 1020
    }

    .sticky-md-bottom {
        position: sticky;
        bottom: 0;
        z-index: 1020
    }

    .float-md-start {
        float: left!important
    }

    .float-md-end {
        float: right!important
    }

    .float-md-none {
        float: none!important
    }

    .object-fit-md-contain {
        -o-object-fit: contain!important;
        object-fit: contain!important
    }

    .object-fit-md-cover {
        -o-object-fit: cover!important;
        object-fit: cover!important
    }

    .object-fit-md-fill {
        -o-object-fit: fill!important;
        object-fit: fill!important
    }

    .object-fit-md-scale {
        -o-object-fit: scale-down!important;
        object-fit: scale-down!important
    }

    .object-fit-md-none {
        -o-object-fit: none!important;
        object-fit: none!important
    }

    .d-md-inline {
        display: inline!important
    }

    .d-md-inline-block {
        display: inline-block!important
    }

    .d-md-block {
        display: block!important
    }

    .d-md-grid {
        display: grid!important
    }

    .d-md-table {
        display: table!important
    }

    .d-md-table-row {
        display: table-row!important
    }

    .d-md-table-cell {
        display: table-cell!important
    }

    .d-md-flex {
        display: -webkit-box!important;
        display: -ms-flexbox!important;
        display: flex!important
    }

    .d-md-inline-flex {
        display: -webkit-inline-box!important;
        display: -ms-inline-flexbox!important;
        display: inline-flex!important
    }

    .d-md-none {
        display: none!important
    }

    .flex-md-fill {
        -webkit-box-flex: 1!important;
        -ms-flex: 1 1 auto!important;
        flex: 1 1 auto!important
    }

    .flex-md-row {
        -webkit-box-orient: horizontal!important;
        -webkit-box-direction: normal!important;
        -ms-flex-direction: row!important;
        flex-direction: row!important
    }

    .flex-md-column {
        -webkit-box-orient: vertical!important;
        -webkit-box-direction: normal!important;
        -ms-flex-direction: column!important;
        flex-direction: column!important
    }

    .flex-md-row-reverse {
        -webkit-box-orient: horizontal!important;
        -webkit-box-direction: reverse!important;
        -ms-flex-direction: row-reverse!important;
        flex-direction: row-reverse!important
    }

    .flex-md-column-reverse {
        -webkit-box-orient: vertical!important;
        -webkit-box-direction: reverse!important;
        -ms-flex-direction: column-reverse!important;
        flex-direction: column-reverse!important
    }

    .flex-md-grow-0 {
        -webkit-box-flex: 0!important;
        -ms-flex-positive: 0!important;
        flex-grow: 0!important
    }

    .flex-md-grow-1 {
        -webkit-box-flex: 1!important;
        -ms-flex-positive: 1!important;
        flex-grow: 1!important
    }

    .flex-md-shrink-0 {
        -ms-flex-negative: 0!important;
        flex-shrink: 0!important
    }

    .flex-md-shrink-1 {
        -ms-flex-negative: 1!important;
        flex-shrink: 1!important
    }

    .flex-md-wrap {
        -ms-flex-wrap: wrap!important;
        flex-wrap: wrap!important
    }

    .flex-md-nowrap {
        -ms-flex-wrap: nowrap!important;
        flex-wrap: nowrap!important
    }

    .flex-md-wrap-reverse {
        -ms-flex-wrap: wrap-reverse!important;
        flex-wrap: wrap-reverse!important
    }

    .justify-content-md-start {
        -webkit-box-pack: start!important;
        -ms-flex-pack: start!important;
        justify-content: flex-start!important
    }

    .justify-content-md-end {
        -webkit-box-pack: end!important;
        -ms-flex-pack: end!important;
        justify-content: flex-end!important
    }

    .justify-content-md-center {
        -webkit-box-pack: center!important;
        -ms-flex-pack: center!important;
        justify-content: center!important
    }

    .justify-content-md-between {
        -webkit-box-pack: justify!important;
        -ms-flex-pack: justify!important;
        justify-content: space-between!important
    }

    .justify-content-md-around {
        -ms-flex-pack: distribute!important;
        justify-content: space-around!important
    }

    .justify-content-md-evenly {
        -webkit-box-pack: space-evenly!important;
        -ms-flex-pack: space-evenly!important;
        justify-content: space-evenly!important
    }

    .align-items-md-start {
        -webkit-box-align: start!important;
        -ms-flex-align: start!important;
        align-items: flex-start!important
    }

    .align-items-md-end {
        -webkit-box-align: end!important;
        -ms-flex-align: end!important;
        align-items: flex-end!important
    }

    .align-items-md-center {
        -webkit-box-align: center!important;
        -ms-flex-align: center!important;
        align-items: center!important
    }

    .align-items-md-baseline {
        -webkit-box-align: baseline!important;
        -ms-flex-align: baseline!important;
        align-items: baseline!important
    }

    .align-items-md-stretch {
        -webkit-box-align: stretch!important;
        -ms-flex-align: stretch!important;
        align-items: stretch!important
    }

    .align-content-md-start {
        -ms-flex-line-pack: start!important;
        align-content: flex-start!important
    }

    .align-content-md-end {
        -ms-flex-line-pack: end!important;
        align-content: flex-end!important
    }

    .align-content-md-center {
        -ms-flex-line-pack: center!important;
        align-content: center!important
    }

    .align-content-md-between {
        -ms-flex-line-pack: justify!important;
        align-content: space-between!important
    }

    .align-content-md-around {
        -ms-flex-line-pack: distribute!important;
        align-content: space-around!important
    }

    .align-content-md-stretch {
        -ms-flex-line-pack: stretch!important;
        align-content: stretch!important
    }

    .align-self-md-auto {
        -ms-flex-item-align: auto!important;
        align-self: auto!important
    }

    .align-self-md-start {
        -ms-flex-item-align: start!important;
        align-self: flex-start!important
    }

    .align-self-md-end {
        -ms-flex-item-align: end!important;
        align-self: flex-end!important
    }

    .align-self-md-center {
        -ms-flex-item-align: center!important;
        align-self: center!important
    }

    .align-self-md-baseline {
        -ms-flex-item-align: baseline!important;
        align-self: baseline!important
    }

    .align-self-md-stretch {
        -ms-flex-item-align: stretch!important;
        align-self: stretch!important
    }

    .order-md-first {
        -webkit-box-ordinal-group: 0!important;
        -ms-flex-order: -1!important;
        order: -1!important
    }

    .order-md-0 {
        -webkit-box-ordinal-group: 1!important;
        -ms-flex-order: 0!important;
        order: 0!important
    }

    .order-md-1 {
        -webkit-box-ordinal-group: 2!important;
        -ms-flex-order: 1!important;
        order: 1!important
    }

    .order-md-2 {
        -webkit-box-ordinal-group: 3!important;
        -ms-flex-order: 2!important;
        order: 2!important
    }

    .order-md-3 {
        -webkit-box-ordinal-group: 4!important;
        -ms-flex-order: 3!important;
        order: 3!important
    }

    .order-md-4 {
        -webkit-box-ordinal-group: 5!important;
        -ms-flex-order: 4!important;
        order: 4!important
    }

    .order-md-5 {
        -webkit-box-ordinal-group: 6!important;
        -ms-flex-order: 5!important;
        order: 5!important
    }

    .order-md-last {
        -webkit-box-ordinal-group: 7!important;
        -ms-flex-order: 6!important;
        order: 6!important
    }

    .m-md-0 {
        margin: 0!important
    }

    .m-md-1 {
        margin: .25rem!important
    }

    .m-md-2 {
        margin: .5rem!important
    }

    .m-md-3 {
        margin: 1rem!important
    }

    .m-md-4 {
        margin: 1.5rem!important
    }

    .m-md-5 {
        margin: 3rem!important
    }

    .m-md-6 {
        margin: 4.5rem!important
    }

    .m-md-7 {
        margin: 6rem!important
    }

    .m-md-8 {
        margin: 7.5rem!important
    }

    .m-md-9 {
        margin: 9rem!important
    }

    .m-md-10 {
        margin: 10.5rem!important
    }

    .m-md-auto {
        margin: auto!important
    }

    .mx-md-0 {
        margin-right: 0!important;
        margin-left: 0!important
    }

    .mx-md-1 {
        margin-right: .25rem!important;
        margin-left: .25rem!important
    }

    .mx-md-2 {
        margin-right: .5rem!important;
        margin-left: .5rem!important
    }

    .mx-md-3 {
        margin-right: 1rem!important;
        margin-left: 1rem!important
    }

    .mx-md-4 {
        margin-right: 1.5rem!important;
        margin-left: 1.5rem!important
    }

    .mx-md-5 {
        margin-right: 3rem!important;
        margin-left: 3rem!important
    }

    .mx-md-6 {
        margin-right: 4.5rem!important;
        margin-left: 4.5rem!important
    }

    .mx-md-7 {
        margin-right: 6rem!important;
        margin-left: 6rem!important
    }

    .mx-md-8 {
        margin-right: 7.5rem!important;
        margin-left: 7.5rem!important
    }

    .mx-md-9 {
        margin-right: 9rem!important;
        margin-left: 9rem!important
    }

    .mx-md-10 {
        margin-right: 10.5rem!important;
        margin-left: 10.5rem!important
    }

    .mx-md-auto {
        margin-right: auto!important;
        margin-left: auto!important
    }

    .my-md-0 {
        margin-top: 0!important;
        margin-bottom: 0!important
    }

    .my-md-1 {
        margin-top: .25rem!important;
        margin-bottom: .25rem!important
    }

    .my-md-2 {
        margin-top: .5rem!important;
        margin-bottom: .5rem!important
    }

    .my-md-3 {
        margin-top: 1rem!important;
        margin-bottom: 1rem!important
    }

    .my-md-4 {
        margin-top: 1.5rem!important;
        margin-bottom: 1.5rem!important
    }

    .my-md-5 {
        margin-top: 3rem!important;
        margin-bottom: 3rem!important
    }

    .my-md-6 {
        margin-top: 4.5rem!important;
        margin-bottom: 4.5rem!important
    }

    .my-md-7 {
        margin-top: 6rem!important;
        margin-bottom: 6rem!important
    }

    .my-md-8 {
        margin-top: 7.5rem!important;
        margin-bottom: 7.5rem!important
    }

    .my-md-9 {
        margin-top: 9rem!important;
        margin-bottom: 9rem!important
    }

    .my-md-10 {
        margin-top: 10.5rem!important;
        margin-bottom: 10.5rem!important
    }

    .my-md-auto {
        margin-top: auto!important;
        margin-bottom: auto!important
    }

    .mt-md-0 {
        margin-top: 0!important
    }

    .mt-md-1 {
        margin-top: .25rem!important
    }

    .mt-md-2 {
        margin-top: .5rem!important
    }

    .mt-md-3 {
        margin-top: 1rem!important
    }

    .mt-md-4 {
        margin-top: 1.5rem!important
    }

    .mt-md-5 {
        margin-top: 3rem!important
    }

    .mt-md-6 {
        margin-top: 4.5rem!important
    }

    .mt-md-7 {
        margin-top: 6rem!important
    }

    .mt-md-8 {
        margin-top: 7.5rem!important
    }

    .mt-md-9 {
        margin-top: 9rem!important
    }

    .mt-md-10 {
        margin-top: 10.5rem!important
    }

    .mt-md-auto {
        margin-top: auto!important
    }

    .me-md-0 {
        margin-right: 0!important
    }

    .me-md-1 {
        margin-right: .25rem!important
    }

    .me-md-2 {
        margin-right: .5rem!important
    }

    .me-md-3 {
        margin-right: 1rem!important
    }

    .me-md-4 {
        margin-right: 1.5rem!important
    }

    .me-md-5 {
        margin-right: 3rem!important
    }

    .me-md-6 {
        margin-right: 4.5rem!important
    }

    .me-md-7 {
        margin-right: 6rem!important
    }

    .me-md-8 {
        margin-right: 7.5rem!important
    }

    .me-md-9 {
        margin-right: 9rem!important
    }

    .me-md-10 {
        margin-right: 10.5rem!important
    }

    .me-md-auto {
        margin-right: auto!important
    }

    .mb-md-0 {
        margin-bottom: 0!important
    }

    .mb-md-1 {
        margin-bottom: .25rem!important
    }

    .mb-md-2 {
        margin-bottom: .5rem!important
    }

    .mb-md-3 {
        margin-bottom: 1rem!important
    }

    .mb-md-4 {
        margin-bottom: 1.5rem!important
    }

    .mb-md-5 {
        margin-bottom: 3rem!important
    }

    .mb-md-6 {
        margin-bottom: 4.5rem!important
    }

    .mb-md-7 {
        margin-bottom: 6rem!important
    }

    .mb-md-8 {
        margin-bottom: 7.5rem!important
    }

    .mb-md-9 {
        margin-bottom: 9rem!important
    }

    .mb-md-10 {
        margin-bottom: 10.5rem!important
    }

    .mb-md-auto {
        margin-bottom: auto!important
    }

    .ms-md-0 {
        margin-left: 0!important
    }

    .ms-md-1 {
        margin-left: .25rem!important
    }

    .ms-md-2 {
        margin-left: .5rem!important
    }

    .ms-md-3 {
        margin-left: 1rem!important
    }

    .ms-md-4 {
        margin-left: 1.5rem!important
    }

    .ms-md-5 {
        margin-left: 3rem!important
    }

    .ms-md-6 {
        margin-left: 4.5rem!important
    }

    .ms-md-7 {
        margin-left: 6rem!important
    }

    .ms-md-8 {
        margin-left: 7.5rem!important
    }

    .ms-md-9 {
        margin-left: 9rem!important
    }

    .ms-md-10 {
        margin-left: 10.5rem!important
    }

    .ms-md-auto {
        margin-left: auto!important
    }

    .p-md-0 {
        padding: 0!important
    }

    .p-md-1 {
        padding: .25rem!important
    }

    .p-md-2 {
        padding: .5rem!important
    }

    .p-md-3 {
        padding: 1rem!important
    }

    .p-md-4 {
        padding: 1.5rem!important
    }

    .p-md-5 {
        padding: 3rem!important
    }

    .p-md-6 {
        padding: 4.5rem!important
    }

    .p-md-7 {
        padding: 6rem!important
    }

    .p-md-8 {
        padding: 7.5rem!important
    }

    .p-md-9 {
        padding: 9rem!important
    }

    .p-md-10 {
        padding: 10.5rem!important
    }

    .px-md-0 {
        padding-right: 0!important;
        padding-left: 0!important
    }

    .px-md-1 {
        padding-right: .25rem!important;
        padding-left: .25rem!important
    }

    .px-md-2 {
        padding-right: .5rem!important;
        padding-left: .5rem!important
    }

    .px-md-3 {
        padding-right: 1rem!important;
        padding-left: 1rem!important
    }

    .px-md-4 {
        padding-right: 1.5rem!important;
        padding-left: 1.5rem!important
    }

    .px-md-5 {
        padding-right: 3rem!important;
        padding-left: 3rem!important
    }

    .px-md-6 {
        padding-right: 4.5rem!important;
        padding-left: 4.5rem!important
    }

    .px-md-7 {
        padding-right: 6rem!important;
        padding-left: 6rem!important
    }

    .px-md-8 {
        padding-right: 7.5rem!important;
        padding-left: 7.5rem!important
    }

    .px-md-9 {
        padding-right: 9rem!important;
        padding-left: 9rem!important
    }

    .px-md-10 {
        padding-right: 10.5rem!important;
        padding-left: 10.5rem!important
    }

    .py-md-0 {
        padding-top: 0!important;
        padding-bottom: 0!important
    }

    .py-md-1 {
        padding-top: .25rem!important;
        padding-bottom: .25rem!important
    }

    .py-md-2 {
        padding-top: .5rem!important;
        padding-bottom: .5rem!important
    }

    .py-md-3 {
        padding-top: 1rem!important;
        padding-bottom: 1rem!important
    }

    .py-md-4 {
        padding-top: 1.5rem!important;
        padding-bottom: 1.5rem!important
    }

    .py-md-5 {
        padding-top: 3rem!important;
        padding-bottom: 3rem!important
    }

    .py-md-6 {
        padding-top: 4.5rem!important;
        padding-bottom: 4.5rem!important
    }

    .py-md-7 {
        padding-top: 6rem!important;
        padding-bottom: 6rem!important
    }

    .py-md-8 {
        padding-top: 7.5rem!important;
        padding-bottom: 7.5rem!important
    }

    .py-md-9 {
        padding-top: 9rem!important;
        padding-bottom: 9rem!important
    }

    .py-md-10 {
        padding-top: 10.5rem!important;
        padding-bottom: 10.5rem!important
    }

    .pt-md-0 {
        padding-top: 0!important
    }

    .pt-md-1 {
        padding-top: .25rem!important
    }

    .pt-md-2 {
        padding-top: .5rem!important
    }

    .pt-md-3 {
        padding-top: 1rem!important
    }

    .pt-md-4 {
        padding-top: 1.5rem!important
    }

    .pt-md-5 {
        padding-top: 3rem!important
    }

    .pt-md-6 {
        padding-top: 4.5rem!important
    }

    .pt-md-7 {
        padding-top: 6rem!important
    }

    .pt-md-8 {
        padding-top: 7.5rem!important
    }

    .pt-md-9 {
        padding-top: 9rem!important
    }

    .pt-md-10 {
        padding-top: 10.5rem!important
    }

    .pe-md-0 {
        padding-right: 0!important
    }

    .pe-md-1 {
        padding-right: .25rem!important
    }

    .pe-md-2 {
        padding-right: .5rem!important
    }

    .pe-md-3 {
        padding-right: 1rem!important
    }

    .pe-md-4 {
        padding-right: 1.5rem!important
    }

    .pe-md-5 {
        padding-right: 3rem!important
    }

    .pe-md-6 {
        padding-right: 4.5rem!important
    }

    .pe-md-7 {
        padding-right: 6rem!important
    }

    .pe-md-8 {
        padding-right: 7.5rem!important
    }

    .pe-md-9 {
        padding-right: 9rem!important
    }

    .pe-md-10 {
        padding-right: 10.5rem!important
    }

    .pb-md-0 {
        padding-bottom: 0!important
    }

    .pb-md-1 {
        padding-bottom: .25rem!important
    }

    .pb-md-2 {
        padding-bottom: .5rem!important
    }

    .pb-md-3 {
        padding-bottom: 1rem!important
    }

    .pb-md-4 {
        padding-bottom: 1.5rem!important
    }

    .pb-md-5 {
        padding-bottom: 3rem!important
    }

    .pb-md-6 {
        padding-bottom: 4.5rem!important
    }

    .pb-md-7 {
        padding-bottom: 6rem!important
    }

    .pb-md-8 {
        padding-bottom: 7.5rem!important
    }

    .pb-md-9 {
        padding-bottom: 9rem!important
    }

    .pb-md-10 {
        padding-bottom: 10.5rem!important
    }

    .ps-md-0 {
        padding-left: 0!important
    }

    .ps-md-1 {
        padding-left: .25rem!important
    }

    .ps-md-2 {
        padding-left: .5rem!important
    }

    .ps-md-3 {
        padding-left: 1rem!important
    }

    .ps-md-4 {
        padding-left: 1.5rem!important
    }

    .ps-md-5 {
        padding-left: 3rem!important
    }

    .ps-md-6 {
        padding-left: 4.5rem!important
    }

    .ps-md-7 {
        padding-left: 6rem!important
    }

    .ps-md-8 {
        padding-left: 7.5rem!important
    }

    .ps-md-9 {
        padding-left: 9rem!important
    }

    .ps-md-10 {
        padding-left: 10.5rem!important
    }

    .gap-md-0 {
        gap: 0!important
    }

    .gap-md-1 {
        gap: .25rem!important
    }

    .gap-md-2 {
        gap: .5rem!important
    }

    .gap-md-3 {
        gap: 1rem!important
    }

    .gap-md-4 {
        gap: 1.5rem!important
    }

    .gap-md-5 {
        gap: 3rem!important
    }

    .gap-md-6 {
        gap: 4.5rem!important
    }

    .gap-md-7 {
        gap: 6rem!important
    }

    .gap-md-8 {
        gap: 7.5rem!important
    }

    .gap-md-9 {
        gap: 9rem!important
    }

    .gap-md-10 {
        gap: 10.5rem!important
    }

    .row-gap-md-0 {
        row-gap: 0!important
    }

    .row-gap-md-1 {
        row-gap: .25rem!important
    }

    .row-gap-md-2 {
        row-gap: .5rem!important
    }

    .row-gap-md-3 {
        row-gap: 1rem!important
    }

    .row-gap-md-4 {
        row-gap: 1.5rem!important
    }

    .row-gap-md-5 {
        row-gap: 3rem!important
    }

    .row-gap-md-6 {
        row-gap: 4.5rem!important
    }

    .row-gap-md-7 {
        row-gap: 6rem!important
    }

    .row-gap-md-8 {
        row-gap: 7.5rem!important
    }

    .row-gap-md-9 {
        row-gap: 9rem!important
    }

    .row-gap-md-10 {
        row-gap: 10.5rem!important
    }

    .column-gap-md-0 {
        -webkit-column-gap: 0!important;
        -moz-column-gap: 0!important;
        column-gap: 0!important
    }

    .column-gap-md-1 {
        -webkit-column-gap: .25rem!important;
        -moz-column-gap: .25rem!important;
        column-gap: .25rem!important
    }

    .column-gap-md-2 {
        -webkit-column-gap: .5rem!important;
        -moz-column-gap: .5rem!important;
        column-gap: .5rem!important
    }

    .column-gap-md-3 {
        -webkit-column-gap: 1rem!important;
        -moz-column-gap: 1rem!important;
        column-gap: 1rem!important
    }

    .column-gap-md-4 {
        -webkit-column-gap: 1.5rem!important;
        -moz-column-gap: 1.5rem!important;
        column-gap: 1.5rem!important
    }

    .column-gap-md-5 {
        -webkit-column-gap: 3rem!important;
        -moz-column-gap: 3rem!important;
        column-gap: 3rem!important
    }

    .column-gap-md-6 {
        -webkit-column-gap: 4.5rem!important;
        -moz-column-gap: 4.5rem!important;
        column-gap: 4.5rem!important
    }

    .column-gap-md-7 {
        -webkit-column-gap: 6rem!important;
        -moz-column-gap: 6rem!important;
        column-gap: 6rem!important
    }

    .column-gap-md-8 {
        -webkit-column-gap: 7.5rem!important;
        -moz-column-gap: 7.5rem!important;
        column-gap: 7.5rem!important
    }

    .column-gap-md-9 {
        -webkit-column-gap: 9rem!important;
        -moz-column-gap: 9rem!important;
        column-gap: 9rem!important
    }

    .column-gap-md-10 {
        -webkit-column-gap: 10.5rem!important;
        -moz-column-gap: 10.5rem!important;
        column-gap: 10.5rem!important
    }

    .text-md-start {
        text-align: left!important
    }

    .text-md-end {
        text-align: right!important
    }

    .text-md-center {
        text-align: center!important
    }

    .navbar2 .sep {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex
    }

    .navbar2 .action-hover-menu:hover>.hover-warp {
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
        opacity: 1;
        visibility: visible;
        top: 55px
    }

    .avatar-xxl {
        width: 8rem;
        height: 8rem
    }

    .avatar-xxxl {
        width: 11rem;
        height: 11rem
    }
}

@media (min-width: 992px) {
    .container,.container-lg,.container-md,.container-sm {
        max-width:960px
    }

    .col-lg {
        -webkit-box-flex: 1;
        -ms-flex: 1 0 0%;
        flex: 1 0 0%
    }

    .row-cols-lg-auto>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }

    .row-cols-lg-1>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 100%
    }

    .row-cols-lg-2>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 50%
    }

    .row-cols-lg-3>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 33.3333333333%
    }

    .row-cols-lg-4>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 25%
    }

    .row-cols-lg-5>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 20%
    }

    .row-cols-lg-6>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 16.6666666667%
    }

    .col-lg-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }

    .col-lg-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 8.33333333%
    }

    .col-lg-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 16.66666667%
    }

    .col-lg-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 25%
    }

    .col-lg-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 33.33333333%
    }

    .col-lg-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 41.66666667%
    }

    .col-lg-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 50%
    }

    .col-lg-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 58.33333333%
    }

    .col-lg-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 66.66666667%
    }

    .col-lg-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 75%
    }

    .col-lg-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 83.33333333%
    }

    .col-lg-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 91.66666667%
    }

    .col-lg-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 100%
    }

    .offset-lg-0 {
        margin-left: 0
    }

    .offset-lg-1 {
        margin-left: 8.33333333%
    }

    .offset-lg-2 {
        margin-left: 16.66666667%
    }

    .offset-lg-3 {
        margin-left: 25%
    }

    .offset-lg-4 {
        margin-left: 33.33333333%
    }

    .offset-lg-5 {
        margin-left: 41.66666667%
    }

    .offset-lg-6 {
        margin-left: 50%
    }

    .offset-lg-7 {
        margin-left: 58.33333333%
    }

    .offset-lg-8 {
        margin-left: 66.66666667%
    }

    .offset-lg-9 {
        margin-left: 75%
    }

    .offset-lg-10 {
        margin-left: 83.33333333%
    }

    .offset-lg-11 {
        margin-left: 91.66666667%
    }

    .g-lg-0,.gx-lg-0 {
        --ri-gutter-x: 0
    }

    .g-lg-0,.gy-lg-0 {
        --ri-gutter-y: 0
    }

    .g-lg-1,.gx-lg-1 {
        --ri-gutter-x: 0.25rem
    }

    .g-lg-1,.gy-lg-1 {
        --ri-gutter-y: 0.25rem
    }

    .g-lg-2,.gx-lg-2 {
        --ri-gutter-x: 0.5rem
    }

    .g-lg-2,.gy-lg-2 {
        --ri-gutter-y: 0.5rem
    }

    .g-lg-3,.gx-lg-3 {
        --ri-gutter-x: 1rem
    }

    .g-lg-3,.gy-lg-3 {
        --ri-gutter-y: 1rem
    }

    .g-lg-4,.gx-lg-4 {
        --ri-gutter-x: 1.5rem
    }

    .g-lg-4,.gy-lg-4 {
        --ri-gutter-y: 1.5rem
    }

    .g-lg-5,.gx-lg-5 {
        --ri-gutter-x: 3rem
    }

    .g-lg-5,.gy-lg-5 {
        --ri-gutter-y: 3rem
    }

    .g-lg-6,.gx-lg-6 {
        --ri-gutter-x: 4.5rem
    }

    .g-lg-6,.gy-lg-6 {
        --ri-gutter-y: 4.5rem
    }

    .g-lg-7,.gx-lg-7 {
        --ri-gutter-x: 6rem
    }

    .g-lg-7,.gy-lg-7 {
        --ri-gutter-y: 6rem
    }

    .g-lg-8,.gx-lg-8 {
        --ri-gutter-x: 7.5rem
    }

    .g-lg-8,.gy-lg-8 {
        --ri-gutter-y: 7.5rem
    }

    .g-lg-9,.gx-lg-9 {
        --ri-gutter-x: 9rem
    }

    .g-lg-9,.gy-lg-9 {
        --ri-gutter-y: 9rem
    }

    .g-lg-10,.gx-lg-10 {
        --ri-gutter-x: 10.5rem
    }

    .g-lg-10,.gy-lg-10 {
        --ri-gutter-y: 10.5rem
    }

    .list-group-horizontal-lg {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row
    }

    .list-group-horizontal-lg>.list-group-item:first-child:not(:last-child) {
        border-bottom-left-radius: var(--ri-list-group-border-radius);
        border-top-right-radius: 0
    }

    .list-group-horizontal-lg>.list-group-item:last-child:not(:first-child) {
        border-top-right-radius: var(--ri-list-group-border-radius);
        border-bottom-left-radius: 0
    }

    .list-group-horizontal-lg>.list-group-item.active {
        margin-top: 0
    }

    .list-group-horizontal-lg>.list-group-item+.list-group-item {
        border-top-width: var(--ri-list-group-border-width);
        border-left-width: 0
    }

    .list-group-horizontal-lg>.list-group-item+.list-group-item.active {
        margin-left: calc(-1 * var(--ri-list-group-border-width));
        border-left-width: var(--ri-list-group-border-width)
    }

    .sticky-lg-top {
        position: sticky;
        top: 0;
        z-index: 1020
    }

    .sticky-lg-bottom {
        position: sticky;
        bottom: 0;
        z-index: 1020
    }

    .float-lg-start {
        float: left!important
    }

    .float-lg-end {
        float: right!important
    }

    .float-lg-none {
        float: none!important
    }

    .object-fit-lg-contain {
        -o-object-fit: contain!important;
        object-fit: contain!important
    }

    .object-fit-lg-cover {
        -o-object-fit: cover!important;
        object-fit: cover!important
    }

    .object-fit-lg-fill {
        -o-object-fit: fill!important;
        object-fit: fill!important
    }

    .object-fit-lg-scale {
        -o-object-fit: scale-down!important;
        object-fit: scale-down!important
    }

    .object-fit-lg-none {
        -o-object-fit: none!important;
        object-fit: none!important
    }

    .d-lg-inline {
        display: inline!important
    }

    .d-lg-inline-block {
        display: inline-block!important
    }

    .d-lg-block {
        display: block!important
    }

    .d-lg-grid {
        display: grid!important
    }

    .d-lg-table {
        display: table!important
    }

    .d-lg-table-row {
        display: table-row!important
    }

    .d-lg-table-cell {
        display: table-cell!important
    }

    .d-lg-flex {
        display: -webkit-box!important;
        display: -ms-flexbox!important;
        display: flex!important
    }

    .d-lg-inline-flex {
        display: -webkit-inline-box!important;
        display: -ms-inline-flexbox!important;
        display: inline-flex!important
    }

    .d-lg-none {
        display: none!important
    }

    .flex-lg-fill {
        -webkit-box-flex: 1!important;
        -ms-flex: 1 1 auto!important;
        flex: 1 1 auto!important
    }

    .flex-lg-row {
        -webkit-box-orient: horizontal!important;
        -webkit-box-direction: normal!important;
        -ms-flex-direction: row!important;
        flex-direction: row!important
    }

    .flex-lg-column {
        -webkit-box-orient: vertical!important;
        -webkit-box-direction: normal!important;
        -ms-flex-direction: column!important;
        flex-direction: column!important
    }

    .flex-lg-row-reverse {
        -webkit-box-orient: horizontal!important;
        -webkit-box-direction: reverse!important;
        -ms-flex-direction: row-reverse!important;
        flex-direction: row-reverse!important
    }

    .flex-lg-column-reverse {
        -webkit-box-orient: vertical!important;
        -webkit-box-direction: reverse!important;
        -ms-flex-direction: column-reverse!important;
        flex-direction: column-reverse!important
    }

    .flex-lg-grow-0 {
        -webkit-box-flex: 0!important;
        -ms-flex-positive: 0!important;
        flex-grow: 0!important
    }

    .flex-lg-grow-1 {
        -webkit-box-flex: 1!important;
        -ms-flex-positive: 1!important;
        flex-grow: 1!important
    }

    .flex-lg-shrink-0 {
        -ms-flex-negative: 0!important;
        flex-shrink: 0!important
    }

    .flex-lg-shrink-1 {
        -ms-flex-negative: 1!important;
        flex-shrink: 1!important
    }

    .flex-lg-wrap {
        -ms-flex-wrap: wrap!important;
        flex-wrap: wrap!important
    }

    .flex-lg-nowrap {
        -ms-flex-wrap: nowrap!important;
        flex-wrap: nowrap!important
    }

    .flex-lg-wrap-reverse {
        -ms-flex-wrap: wrap-reverse!important;
        flex-wrap: wrap-reverse!important
    }

    .justify-content-lg-start {
        -webkit-box-pack: start!important;
        -ms-flex-pack: start!important;
        justify-content: flex-start!important
    }

    .justify-content-lg-end {
        -webkit-box-pack: end!important;
        -ms-flex-pack: end!important;
        justify-content: flex-end!important
    }

    .justify-content-lg-center {
        -webkit-box-pack: center!important;
        -ms-flex-pack: center!important;
        justify-content: center!important
    }

    .justify-content-lg-between {
        -webkit-box-pack: justify!important;
        -ms-flex-pack: justify!important;
        justify-content: space-between!important
    }

    .justify-content-lg-around {
        -ms-flex-pack: distribute!important;
        justify-content: space-around!important
    }

    .justify-content-lg-evenly {
        -webkit-box-pack: space-evenly!important;
        -ms-flex-pack: space-evenly!important;
        justify-content: space-evenly!important
    }

    .align-items-lg-start {
        -webkit-box-align: start!important;
        -ms-flex-align: start!important;
        align-items: flex-start!important
    }

    .align-items-lg-end {
        -webkit-box-align: end!important;
        -ms-flex-align: end!important;
        align-items: flex-end!important
    }

    .align-items-lg-center {
        -webkit-box-align: center!important;
        -ms-flex-align: center!important;
        align-items: center!important
    }

    .align-items-lg-baseline {
        -webkit-box-align: baseline!important;
        -ms-flex-align: baseline!important;
        align-items: baseline!important
    }

    .align-items-lg-stretch {
        -webkit-box-align: stretch!important;
        -ms-flex-align: stretch!important;
        align-items: stretch!important
    }

    .align-content-lg-start {
        -ms-flex-line-pack: start!important;
        align-content: flex-start!important
    }

    .align-content-lg-end {
        -ms-flex-line-pack: end!important;
        align-content: flex-end!important
    }

    .align-content-lg-center {
        -ms-flex-line-pack: center!important;
        align-content: center!important
    }

    .align-content-lg-between {
        -ms-flex-line-pack: justify!important;
        align-content: space-between!important
    }

    .align-content-lg-around {
        -ms-flex-line-pack: distribute!important;
        align-content: space-around!important
    }

    .align-content-lg-stretch {
        -ms-flex-line-pack: stretch!important;
        align-content: stretch!important
    }

    .align-self-lg-auto {
        -ms-flex-item-align: auto!important;
        align-self: auto!important
    }

    .align-self-lg-start {
        -ms-flex-item-align: start!important;
        align-self: flex-start!important
    }

    .align-self-lg-end {
        -ms-flex-item-align: end!important;
        align-self: flex-end!important
    }

    .align-self-lg-center {
        -ms-flex-item-align: center!important;
        align-self: center!important
    }

    .align-self-lg-baseline {
        -ms-flex-item-align: baseline!important;
        align-self: baseline!important
    }

    .align-self-lg-stretch {
        -ms-flex-item-align: stretch!important;
        align-self: stretch!important
    }

    .order-lg-first {
        -webkit-box-ordinal-group: 0!important;
        -ms-flex-order: -1!important;
        order: -1!important
    }

    .order-lg-0 {
        -webkit-box-ordinal-group: 1!important;
        -ms-flex-order: 0!important;
        order: 0!important
    }

    .order-lg-1 {
        -webkit-box-ordinal-group: 2!important;
        -ms-flex-order: 1!important;
        order: 1!important
    }

    .order-lg-2 {
        -webkit-box-ordinal-group: 3!important;
        -ms-flex-order: 2!important;
        order: 2!important
    }

    .order-lg-3 {
        -webkit-box-ordinal-group: 4!important;
        -ms-flex-order: 3!important;
        order: 3!important
    }

    .order-lg-4 {
        -webkit-box-ordinal-group: 5!important;
        -ms-flex-order: 4!important;
        order: 4!important
    }

    .order-lg-5 {
        -webkit-box-ordinal-group: 6!important;
        -ms-flex-order: 5!important;
        order: 5!important
    }

    .order-lg-last {
        -webkit-box-ordinal-group: 7!important;
        -ms-flex-order: 6!important;
        order: 6!important
    }

    .m-lg-0 {
        margin: 0!important
    }

    .m-lg-1 {
        margin: .25rem!important
    }

    .m-lg-2 {
        margin: .5rem!important
    }

    .m-lg-3 {
        margin: 1rem!important
    }

    .m-lg-4 {
        margin: 1.5rem!important
    }

    .m-lg-5 {
        margin: 3rem!important
    }

    .m-lg-6 {
        margin: 4.5rem!important
    }

    .m-lg-7 {
        margin: 6rem!important
    }

    .m-lg-8 {
        margin: 7.5rem!important
    }

    .m-lg-9 {
        margin: 9rem!important
    }

    .m-lg-10 {
        margin: 10.5rem!important
    }

    .m-lg-auto {
        margin: auto!important
    }

    .mx-lg-0 {
        margin-right: 0!important;
        margin-left: 0!important
    }

    .mx-lg-1 {
        margin-right: .25rem!important;
        margin-left: .25rem!important
    }

    .mx-lg-2 {
        margin-right: .5rem!important;
        margin-left: .5rem!important
    }

    .mx-lg-3 {
        margin-right: 1rem!important;
        margin-left: 1rem!important
    }

    .mx-lg-4 {
        margin-right: 1.5rem!important;
        margin-left: 1.5rem!important
    }

    .mx-lg-5 {
        margin-right: 3rem!important;
        margin-left: 3rem!important
    }

    .mx-lg-6 {
        margin-right: 4.5rem!important;
        margin-left: 4.5rem!important
    }

    .mx-lg-7 {
        margin-right: 6rem!important;
        margin-left: 6rem!important
    }

    .mx-lg-8 {
        margin-right: 7.5rem!important;
        margin-left: 7.5rem!important
    }

    .mx-lg-9 {
        margin-right: 9rem!important;
        margin-left: 9rem!important
    }

    .mx-lg-10 {
        margin-right: 10.5rem!important;
        margin-left: 10.5rem!important
    }

    .mx-lg-auto {
        margin-right: auto!important;
        margin-left: auto!important
    }

    .my-lg-0 {
        margin-top: 0!important;
        margin-bottom: 0!important
    }

    .my-lg-1 {
        margin-top: .25rem!important;
        margin-bottom: .25rem!important
    }

    .my-lg-2 {
        margin-top: .5rem!important;
        margin-bottom: .5rem!important
    }

    .my-lg-3 {
        margin-top: 1rem!important;
        margin-bottom: 1rem!important
    }

    .my-lg-4 {
        margin-top: 1.5rem!important;
        margin-bottom: 1.5rem!important
    }

    .my-lg-5 {
        margin-top: 3rem!important;
        margin-bottom: 3rem!important
    }

    .my-lg-6 {
        margin-top: 4.5rem!important;
        margin-bottom: 4.5rem!important
    }

    .my-lg-7 {
        margin-top: 6rem!important;
        margin-bottom: 6rem!important
    }

    .my-lg-8 {
        margin-top: 7.5rem!important;
        margin-bottom: 7.5rem!important
    }

    .my-lg-9 {
        margin-top: 9rem!important;
        margin-bottom: 9rem!important
    }

    .my-lg-10 {
        margin-top: 10.5rem!important;
        margin-bottom: 10.5rem!important
    }

    .my-lg-auto {
        margin-top: auto!important;
        margin-bottom: auto!important
    }

    .mt-lg-0 {
        margin-top: 0!important
    }

    .mt-lg-1 {
        margin-top: .25rem!important
    }

    .mt-lg-2 {
        margin-top: .5rem!important
    }

    .mt-lg-3 {
        margin-top: 1rem!important
    }

    .mt-lg-4 {
        margin-top: 1.5rem!important
    }

    .mt-lg-5 {
        margin-top: 3rem!important
    }

    .mt-lg-6 {
        margin-top: 4.5rem!important
    }

    .mt-lg-7 {
        margin-top: 6rem!important
    }

    .mt-lg-8 {
        margin-top: 7.5rem!important
    }

    .mt-lg-9 {
        margin-top: 9rem!important
    }

    .mt-lg-10 {
        margin-top: 10.5rem!important
    }

    .mt-lg-auto {
        margin-top: auto!important
    }

    .me-lg-0 {
        margin-right: 0!important
    }

    .me-lg-1 {
        margin-right: .25rem!important
    }

    .me-lg-2 {
        margin-right: .5rem!important
    }

    .me-lg-3 {
        margin-right: 1rem!important
    }

    .me-lg-4 {
        margin-right: 1.5rem!important
    }

    .me-lg-5 {
        margin-right: 3rem!important
    }

    .me-lg-6 {
        margin-right: 4.5rem!important
    }

    .me-lg-7 {
        margin-right: 6rem!important
    }

    .me-lg-8 {
        margin-right: 7.5rem!important
    }

    .me-lg-9 {
        margin-right: 9rem!important
    }

    .me-lg-10 {
        margin-right: 10.5rem!important
    }

    .me-lg-auto {
        margin-right: auto!important
    }

    .mb-lg-0 {
        margin-bottom: 0!important
    }

    .mb-lg-1 {
        margin-bottom: .25rem!important
    }

    .mb-lg-2 {
        margin-bottom: .5rem!important
    }

    .mb-lg-3 {
        margin-bottom: 1rem!important
    }

    .mb-lg-4 {
        margin-bottom: 1.5rem!important
    }

    .mb-lg-5 {
        margin-bottom: 3rem!important
    }

    .mb-lg-6 {
        margin-bottom: 4.5rem!important
    }

    .mb-lg-7 {
        margin-bottom: 6rem!important
    }

    .mb-lg-8 {
        margin-bottom: 7.5rem!important
    }

    .mb-lg-9 {
        margin-bottom: 9rem!important
    }

    .mb-lg-10 {
        margin-bottom: 10.5rem!important
    }

    .mb-lg-auto {
        margin-bottom: auto!important
    }

    .ms-lg-0 {
        margin-left: 0!important
    }

    .ms-lg-1 {
        margin-left: .25rem!important
    }

    .ms-lg-2 {
        margin-left: .5rem!important
    }

    .ms-lg-3 {
        margin-left: 1rem!important
    }

    .ms-lg-4 {
        margin-left: 1.5rem!important
    }

    .ms-lg-5 {
        margin-left: 3rem!important
    }

    .ms-lg-6 {
        margin-left: 4.5rem!important
    }

    .ms-lg-7 {
        margin-left: 6rem!important
    }

    .ms-lg-8 {
        margin-left: 7.5rem!important
    }

    .ms-lg-9 {
        margin-left: 9rem!important
    }

    .ms-lg-10 {
        margin-left: 10.5rem!important
    }

    .ms-lg-auto {
        margin-left: auto!important
    }

    .p-lg-0 {
        padding: 0!important
    }

    .p-lg-1 {
        padding: .25rem!important
    }

    .p-lg-2 {
        padding: .5rem!important
    }

    .p-lg-3 {
        padding: 1rem!important
    }

    .p-lg-4 {
        padding: 1.5rem!important
    }

    .p-lg-5 {
        padding: 3rem!important
    }

    .p-lg-6 {
        padding: 4.5rem!important
    }

    .p-lg-7 {
        padding: 6rem!important
    }

    .p-lg-8 {
        padding: 7.5rem!important
    }

    .p-lg-9 {
        padding: 9rem!important
    }

    .p-lg-10 {
        padding: 10.5rem!important
    }

    .px-lg-0 {
        padding-right: 0!important;
        padding-left: 0!important
    }

    .px-lg-1 {
        padding-right: .25rem!important;
        padding-left: .25rem!important
    }

    .px-lg-2 {
        padding-right: .5rem!important;
        padding-left: .5rem!important
    }

    .px-lg-3 {
        padding-right: 1rem!important;
        padding-left: 1rem!important
    }

    .px-lg-4 {
        padding-right: 1.5rem!important;
        padding-left: 1.5rem!important
    }

    .px-lg-5 {
        padding-right: 3rem!important;
        padding-left: 3rem!important
    }

    .px-lg-6 {
        padding-right: 4.5rem!important;
        padding-left: 4.5rem!important
    }

    .px-lg-7 {
        padding-right: 6rem!important;
        padding-left: 6rem!important
    }

    .px-lg-8 {
        padding-right: 7.5rem!important;
        padding-left: 7.5rem!important
    }

    .px-lg-9 {
        padding-right: 9rem!important;
        padding-left: 9rem!important
    }

    .px-lg-10 {
        padding-right: 10.5rem!important;
        padding-left: 10.5rem!important
    }

    .py-lg-0 {
        padding-top: 0!important;
        padding-bottom: 0!important
    }

    .py-lg-1 {
        padding-top: .25rem!important;
        padding-bottom: .25rem!important
    }

    .py-lg-2 {
        padding-top: .5rem!important;
        padding-bottom: .5rem!important
    }

    .py-lg-3 {
        padding-top: 1rem!important;
        padding-bottom: 1rem!important
    }

    .py-lg-4 {
        padding-top: 1.5rem!important;
        padding-bottom: 1.5rem!important
    }

    .py-lg-5 {
        padding-top: 3rem!important;
        padding-bottom: 3rem!important
    }

    .py-lg-6 {
        padding-top: 4.5rem!important;
        padding-bottom: 4.5rem!important
    }

    .py-lg-7 {
        padding-top: 6rem!important;
        padding-bottom: 6rem!important
    }

    .py-lg-8 {
        padding-top: 7.5rem!important;
        padding-bottom: 7.5rem!important
    }

    .py-lg-9 {
        padding-top: 9rem!important;
        padding-bottom: 9rem!important
    }

    .py-lg-10 {
        padding-top: 10.5rem!important;
        padding-bottom: 10.5rem!important
    }

    .pt-lg-0 {
        padding-top: 0!important
    }

    .pt-lg-1 {
        padding-top: .25rem!important
    }

    .pt-lg-2 {
        padding-top: .5rem!important
    }

    .pt-lg-3 {
        padding-top: 1rem!important
    }

    .pt-lg-4 {
        padding-top: 1.5rem!important
    }

    .pt-lg-5 {
        padding-top: 3rem!important
    }

    .pt-lg-6 {
        padding-top: 4.5rem!important
    }

    .pt-lg-7 {
        padding-top: 6rem!important
    }

    .pt-lg-8 {
        padding-top: 7.5rem!important
    }

    .pt-lg-9 {
        padding-top: 9rem!important
    }

    .pt-lg-10 {
        padding-top: 10.5rem!important
    }

    .pe-lg-0 {
        padding-right: 0!important
    }

    .pe-lg-1 {
        padding-right: .25rem!important
    }

    .pe-lg-2 {
        padding-right: .5rem!important
    }

    .pe-lg-3 {
        padding-right: 1rem!important
    }

    .pe-lg-4 {
        padding-right: 1.5rem!important
    }

    .pe-lg-5 {
        padding-right: 3rem!important
    }

    .pe-lg-6 {
        padding-right: 4.5rem!important
    }

    .pe-lg-7 {
        padding-right: 6rem!important
    }

    .pe-lg-8 {
        padding-right: 7.5rem!important
    }

    .pe-lg-9 {
        padding-right: 9rem!important
    }

    .pe-lg-10 {
        padding-right: 10.5rem!important
    }

    .pb-lg-0 {
        padding-bottom: 0!important
    }

    .pb-lg-1 {
        padding-bottom: .25rem!important
    }

    .pb-lg-2 {
        padding-bottom: .5rem!important
    }

    .pb-lg-3 {
        padding-bottom: 1rem!important
    }

    .pb-lg-4 {
        padding-bottom: 1.5rem!important
    }

    .pb-lg-5 {
        padding-bottom: 3rem!important
    }

    .pb-lg-6 {
        padding-bottom: 4.5rem!important
    }

    .pb-lg-7 {
        padding-bottom: 6rem!important
    }

    .pb-lg-8 {
        padding-bottom: 7.5rem!important
    }

    .pb-lg-9 {
        padding-bottom: 9rem!important
    }

    .pb-lg-10 {
        padding-bottom: 10.5rem!important
    }

    .ps-lg-0 {
        padding-left: 0!important
    }

    .ps-lg-1 {
        padding-left: .25rem!important
    }

    .ps-lg-2 {
        padding-left: .5rem!important
    }

    .ps-lg-3 {
        padding-left: 1rem!important
    }

    .ps-lg-4 {
        padding-left: 1.5rem!important
    }

    .ps-lg-5 {
        padding-left: 3rem!important
    }

    .ps-lg-6 {
        padding-left: 4.5rem!important
    }

    .ps-lg-7 {
        padding-left: 6rem!important
    }

    .ps-lg-8 {
        padding-left: 7.5rem!important
    }

    .ps-lg-9 {
        padding-left: 9rem!important
    }

    .ps-lg-10 {
        padding-left: 10.5rem!important
    }

    .gap-lg-0 {
        gap: 0!important
    }

    .gap-lg-1 {
        gap: .25rem!important
    }

    .gap-lg-2 {
        gap: .5rem!important
    }

    .gap-lg-3 {
        gap: 1rem!important
    }

    .gap-lg-4 {
        gap: 1.5rem!important
    }

    .gap-lg-5 {
        gap: 3rem!important
    }

    .gap-lg-6 {
        gap: 4.5rem!important
    }

    .gap-lg-7 {
        gap: 6rem!important
    }

    .gap-lg-8 {
        gap: 7.5rem!important
    }

    .gap-lg-9 {
        gap: 9rem!important
    }

    .gap-lg-10 {
        gap: 10.5rem!important
    }

    .row-gap-lg-0 {
        row-gap: 0!important
    }

    .row-gap-lg-1 {
        row-gap: .25rem!important
    }

    .row-gap-lg-2 {
        row-gap: .5rem!important
    }

    .row-gap-lg-3 {
        row-gap: 1rem!important
    }

    .row-gap-lg-4 {
        row-gap: 1.5rem!important
    }

    .row-gap-lg-5 {
        row-gap: 3rem!important
    }

    .row-gap-lg-6 {
        row-gap: 4.5rem!important
    }

    .row-gap-lg-7 {
        row-gap: 6rem!important
    }

    .row-gap-lg-8 {
        row-gap: 7.5rem!important
    }

    .row-gap-lg-9 {
        row-gap: 9rem!important
    }

    .row-gap-lg-10 {
        row-gap: 10.5rem!important
    }

    .column-gap-lg-0 {
        -webkit-column-gap: 0!important;
        -moz-column-gap: 0!important;
        column-gap: 0!important
    }

    .column-gap-lg-1 {
        -webkit-column-gap: .25rem!important;
        -moz-column-gap: .25rem!important;
        column-gap: .25rem!important
    }

    .column-gap-lg-2 {
        -webkit-column-gap: .5rem!important;
        -moz-column-gap: .5rem!important;
        column-gap: .5rem!important
    }

    .column-gap-lg-3 {
        -webkit-column-gap: 1rem!important;
        -moz-column-gap: 1rem!important;
        column-gap: 1rem!important
    }

    .column-gap-lg-4 {
        -webkit-column-gap: 1.5rem!important;
        -moz-column-gap: 1.5rem!important;
        column-gap: 1.5rem!important
    }

    .column-gap-lg-5 {
        -webkit-column-gap: 3rem!important;
        -moz-column-gap: 3rem!important;
        column-gap: 3rem!important
    }

    .column-gap-lg-6 {
        -webkit-column-gap: 4.5rem!important;
        -moz-column-gap: 4.5rem!important;
        column-gap: 4.5rem!important
    }

    .column-gap-lg-7 {
        -webkit-column-gap: 6rem!important;
        -moz-column-gap: 6rem!important;
        column-gap: 6rem!important
    }

    .column-gap-lg-8 {
        -webkit-column-gap: 7.5rem!important;
        -moz-column-gap: 7.5rem!important;
        column-gap: 7.5rem!important
    }

    .column-gap-lg-9 {
        -webkit-column-gap: 9rem!important;
        -moz-column-gap: 9rem!important;
        column-gap: 9rem!important
    }

    .column-gap-lg-10 {
        -webkit-column-gap: 10.5rem!important;
        -moz-column-gap: 10.5rem!important;
        column-gap: 10.5rem!important
    }

    .text-lg-start {
        text-align: left!important
    }

    .text-lg-end {
        text-align: right!important
    }

    .text-lg-center {
        text-align: center!important
    }

    .login-and-register {
        height: 100vh
    }

    .archive-hero .search-form,.home-search-bg .search-form {
        max-width: 50%
    }

    .search-form .search-fields,.search-form .search-select .form-select {
        font-size: 1.25rem
    }
}

@media (min-width: 1200px) {
    .h1,h1 {
        font-size:2.34375rem
    }

    .h2,h2 {
        font-size: 1.875rem
    }

    .h3,h3 {
        font-size: 1.640625rem
    }

    .h4,h4 {
        font-size: 1.40625rem
    }

    legend {
        font-size: 1.5rem
    }

    .display-1 {
        font-size: 5rem
    }

    .display-2 {
        font-size: 4.5rem
    }

    .display-3 {
        font-size: 4rem
    }

    .display-4 {
        font-size: 3.5rem
    }

    .display-5 {
        font-size: 3rem
    }

    .display-6 {
        font-size: 2.5rem
    }

    .container,.container-lg,.container-md,.container-sm,.container-xl {
        max-width: 1140px
    }

    .col-xl {
        -webkit-box-flex: 1;
        -ms-flex: 1 0 0%;
        flex: 1 0 0%
    }

    .row-cols-xl-auto>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }

    .row-cols-xl-1>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 100%
    }

    .row-cols-xl-2>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 50%
    }

    .row-cols-xl-3>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 33.3333333333%
    }

    .row-cols-xl-4>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 25%
    }

    .row-cols-xl-5>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 20%
    }

    .row-cols-xl-6>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 16.6666666667%
    }

    .col-xl-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }

    .col-xl-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 8.33333333%
    }

    .col-xl-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 16.66666667%
    }

    .col-xl-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 25%
    }

    .col-xl-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 33.33333333%
    }

    .col-xl-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 41.66666667%
    }

    .col-xl-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 50%
    }

    .col-xl-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 58.33333333%
    }

    .col-xl-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 66.66666667%
    }

    .col-xl-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 75%
    }

    .col-xl-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 83.33333333%
    }

    .col-xl-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 91.66666667%
    }

    .col-xl-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 100%
    }

    .offset-xl-0 {
        margin-left: 0
    }

    .offset-xl-1 {
        margin-left: 8.33333333%
    }

    .offset-xl-2 {
        margin-left: 16.66666667%
    }

    .offset-xl-3 {
        margin-left: 25%
    }

    .offset-xl-4 {
        margin-left: 33.33333333%
    }

    .offset-xl-5 {
        margin-left: 41.66666667%
    }

    .offset-xl-6 {
        margin-left: 50%
    }

    .offset-xl-7 {
        margin-left: 58.33333333%
    }

    .offset-xl-8 {
        margin-left: 66.66666667%
    }

    .offset-xl-9 {
        margin-left: 75%
    }

    .offset-xl-10 {
        margin-left: 83.33333333%
    }

    .offset-xl-11 {
        margin-left: 91.66666667%
    }

    .g-xl-0,.gx-xl-0 {
        --ri-gutter-x: 0
    }

    .g-xl-0,.gy-xl-0 {
        --ri-gutter-y: 0
    }

    .g-xl-1,.gx-xl-1 {
        --ri-gutter-x: 0.25rem
    }

    .g-xl-1,.gy-xl-1 {
        --ri-gutter-y: 0.25rem
    }

    .g-xl-2,.gx-xl-2 {
        --ri-gutter-x: 0.5rem
    }

    .g-xl-2,.gy-xl-2 {
        --ri-gutter-y: 0.5rem
    }

    .g-xl-3,.gx-xl-3 {
        --ri-gutter-x: 1rem
    }

    .g-xl-3,.gy-xl-3 {
        --ri-gutter-y: 1rem
    }

    .g-xl-4,.gx-xl-4 {
        --ri-gutter-x: 1.5rem
    }

    .g-xl-4,.gy-xl-4 {
        --ri-gutter-y: 1.5rem
    }

    .g-xl-5,.gx-xl-5 {
        --ri-gutter-x: 3rem
    }

    .g-xl-5,.gy-xl-5 {
        --ri-gutter-y: 3rem
    }

    .g-xl-6,.gx-xl-6 {
        --ri-gutter-x: 4.5rem
    }

    .g-xl-6,.gy-xl-6 {
        --ri-gutter-y: 4.5rem
    }

    .g-xl-7,.gx-xl-7 {
        --ri-gutter-x: 6rem
    }

    .g-xl-7,.gy-xl-7 {
        --ri-gutter-y: 6rem
    }

    .g-xl-8,.gx-xl-8 {
        --ri-gutter-x: 7.5rem
    }

    .g-xl-8,.gy-xl-8 {
        --ri-gutter-y: 7.5rem
    }

    .g-xl-9,.gx-xl-9 {
        --ri-gutter-x: 9rem
    }

    .g-xl-9,.gy-xl-9 {
        --ri-gutter-y: 9rem
    }

    .g-xl-10,.gx-xl-10 {
        --ri-gutter-x: 10.5rem
    }

    .g-xl-10,.gy-xl-10 {
        --ri-gutter-y: 10.5rem
    }

    .list-group-horizontal-xl {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row
    }

    .list-group-horizontal-xl>.list-group-item:first-child:not(:last-child) {
        border-bottom-left-radius: var(--ri-list-group-border-radius);
        border-top-right-radius: 0
    }

    .list-group-horizontal-xl>.list-group-item:last-child:not(:first-child) {
        border-top-right-radius: var(--ri-list-group-border-radius);
        border-bottom-left-radius: 0
    }

    .list-group-horizontal-xl>.list-group-item.active {
        margin-top: 0
    }

    .list-group-horizontal-xl>.list-group-item+.list-group-item {
        border-top-width: var(--ri-list-group-border-width);
        border-left-width: 0
    }

    .list-group-horizontal-xl>.list-group-item+.list-group-item.active {
        margin-left: calc(-1 * var(--ri-list-group-border-width));
        border-left-width: var(--ri-list-group-border-width)
    }

    .sticky-xl-top {
        position: sticky;
        top: 0;
        z-index: 1020
    }

    .sticky-xl-bottom {
        position: sticky;
        bottom: 0;
        z-index: 1020
    }

    .float-xl-start {
        float: left!important
    }

    .float-xl-end {
        float: right!important
    }

    .float-xl-none {
        float: none!important
    }

    .object-fit-xl-contain {
        -o-object-fit: contain!important;
        object-fit: contain!important
    }

    .object-fit-xl-cover {
        -o-object-fit: cover!important;
        object-fit: cover!important
    }

    .object-fit-xl-fill {
        -o-object-fit: fill!important;
        object-fit: fill!important
    }

    .object-fit-xl-scale {
        -o-object-fit: scale-down!important;
        object-fit: scale-down!important
    }

    .object-fit-xl-none {
        -o-object-fit: none!important;
        object-fit: none!important
    }

    .d-xl-inline {
        display: inline!important
    }

    .d-xl-inline-block {
        display: inline-block!important
    }

    .d-xl-block {
        display: block!important
    }

    .d-xl-grid {
        display: grid!important
    }

    .d-xl-table {
        display: table!important
    }

    .d-xl-table-row {
        display: table-row!important
    }

    .d-xl-table-cell {
        display: table-cell!important
    }

    .d-xl-flex {
        display: -webkit-box!important;
        display: -ms-flexbox!important;
        display: flex!important
    }

    .d-xl-inline-flex {
        display: -webkit-inline-box!important;
        display: -ms-inline-flexbox!important;
        display: inline-flex!important
    }

    .d-xl-none {
        display: none!important
    }

    .flex-xl-fill {
        -webkit-box-flex: 1!important;
        -ms-flex: 1 1 auto!important;
        flex: 1 1 auto!important
    }

    .flex-xl-row {
        -webkit-box-orient: horizontal!important;
        -webkit-box-direction: normal!important;
        -ms-flex-direction: row!important;
        flex-direction: row!important
    }

    .flex-xl-column {
        -webkit-box-orient: vertical!important;
        -webkit-box-direction: normal!important;
        -ms-flex-direction: column!important;
        flex-direction: column!important
    }

    .flex-xl-row-reverse {
        -webkit-box-orient: horizontal!important;
        -webkit-box-direction: reverse!important;
        -ms-flex-direction: row-reverse!important;
        flex-direction: row-reverse!important
    }

    .flex-xl-column-reverse {
        -webkit-box-orient: vertical!important;
        -webkit-box-direction: reverse!important;
        -ms-flex-direction: column-reverse!important;
        flex-direction: column-reverse!important
    }

    .flex-xl-grow-0 {
        -webkit-box-flex: 0!important;
        -ms-flex-positive: 0!important;
        flex-grow: 0!important
    }

    .flex-xl-grow-1 {
        -webkit-box-flex: 1!important;
        -ms-flex-positive: 1!important;
        flex-grow: 1!important
    }

    .flex-xl-shrink-0 {
        -ms-flex-negative: 0!important;
        flex-shrink: 0!important
    }

    .flex-xl-shrink-1 {
        -ms-flex-negative: 1!important;
        flex-shrink: 1!important
    }

    .flex-xl-wrap {
        -ms-flex-wrap: wrap!important;
        flex-wrap: wrap!important
    }

    .flex-xl-nowrap {
        -ms-flex-wrap: nowrap!important;
        flex-wrap: nowrap!important
    }

    .flex-xl-wrap-reverse {
        -ms-flex-wrap: wrap-reverse!important;
        flex-wrap: wrap-reverse!important
    }

    .justify-content-xl-start {
        -webkit-box-pack: start!important;
        -ms-flex-pack: start!important;
        justify-content: flex-start!important
    }

    .justify-content-xl-end {
        -webkit-box-pack: end!important;
        -ms-flex-pack: end!important;
        justify-content: flex-end!important
    }

    .justify-content-xl-center {
        -webkit-box-pack: center!important;
        -ms-flex-pack: center!important;
        justify-content: center!important
    }

    .justify-content-xl-between {
        -webkit-box-pack: justify!important;
        -ms-flex-pack: justify!important;
        justify-content: space-between!important
    }

    .justify-content-xl-around {
        -ms-flex-pack: distribute!important;
        justify-content: space-around!important
    }

    .justify-content-xl-evenly {
        -webkit-box-pack: space-evenly!important;
        -ms-flex-pack: space-evenly!important;
        justify-content: space-evenly!important
    }

    .align-items-xl-start {
        -webkit-box-align: start!important;
        -ms-flex-align: start!important;
        align-items: flex-start!important
    }

    .align-items-xl-end {
        -webkit-box-align: end!important;
        -ms-flex-align: end!important;
        align-items: flex-end!important
    }

    .align-items-xl-center {
        -webkit-box-align: center!important;
        -ms-flex-align: center!important;
        align-items: center!important
    }

    .align-items-xl-baseline {
        -webkit-box-align: baseline!important;
        -ms-flex-align: baseline!important;
        align-items: baseline!important
    }

    .align-items-xl-stretch {
        -webkit-box-align: stretch!important;
        -ms-flex-align: stretch!important;
        align-items: stretch!important
    }

    .align-content-xl-start {
        -ms-flex-line-pack: start!important;
        align-content: flex-start!important
    }

    .align-content-xl-end {
        -ms-flex-line-pack: end!important;
        align-content: flex-end!important
    }

    .align-content-xl-center {
        -ms-flex-line-pack: center!important;
        align-content: center!important
    }

    .align-content-xl-between {
        -ms-flex-line-pack: justify!important;
        align-content: space-between!important
    }

    .align-content-xl-around {
        -ms-flex-line-pack: distribute!important;
        align-content: space-around!important
    }

    .align-content-xl-stretch {
        -ms-flex-line-pack: stretch!important;
        align-content: stretch!important
    }

    .align-self-xl-auto {
        -ms-flex-item-align: auto!important;
        align-self: auto!important
    }

    .align-self-xl-start {
        -ms-flex-item-align: start!important;
        align-self: flex-start!important
    }

    .align-self-xl-end {
        -ms-flex-item-align: end!important;
        align-self: flex-end!important
    }

    .align-self-xl-center {
        -ms-flex-item-align: center!important;
        align-self: center!important
    }

    .align-self-xl-baseline {
        -ms-flex-item-align: baseline!important;
        align-self: baseline!important
    }

    .align-self-xl-stretch {
        -ms-flex-item-align: stretch!important;
        align-self: stretch!important
    }

    .order-xl-first {
        -webkit-box-ordinal-group: 0!important;
        -ms-flex-order: -1!important;
        order: -1!important
    }

    .order-xl-0 {
        -webkit-box-ordinal-group: 1!important;
        -ms-flex-order: 0!important;
        order: 0!important
    }

    .order-xl-1 {
        -webkit-box-ordinal-group: 2!important;
        -ms-flex-order: 1!important;
        order: 1!important
    }

    .order-xl-2 {
        -webkit-box-ordinal-group: 3!important;
        -ms-flex-order: 2!important;
        order: 2!important
    }

    .order-xl-3 {
        -webkit-box-ordinal-group: 4!important;
        -ms-flex-order: 3!important;
        order: 3!important
    }

    .order-xl-4 {
        -webkit-box-ordinal-group: 5!important;
        -ms-flex-order: 4!important;
        order: 4!important
    }

    .order-xl-5 {
        -webkit-box-ordinal-group: 6!important;
        -ms-flex-order: 5!important;
        order: 5!important
    }

    .order-xl-last {
        -webkit-box-ordinal-group: 7!important;
        -ms-flex-order: 6!important;
        order: 6!important
    }

    .m-xl-0 {
        margin: 0!important
    }

    .m-xl-1 {
        margin: .25rem!important
    }

    .m-xl-2 {
        margin: .5rem!important
    }

    .m-xl-3 {
        margin: 1rem!important
    }

    .m-xl-4 {
        margin: 1.5rem!important
    }

    .m-xl-5 {
        margin: 3rem!important
    }

    .m-xl-6 {
        margin: 4.5rem!important
    }

    .m-xl-7 {
        margin: 6rem!important
    }

    .m-xl-8 {
        margin: 7.5rem!important
    }

    .m-xl-9 {
        margin: 9rem!important
    }

    .m-xl-10 {
        margin: 10.5rem!important
    }

    .m-xl-auto {
        margin: auto!important
    }

    .mx-xl-0 {
        margin-right: 0!important;
        margin-left: 0!important
    }

    .mx-xl-1 {
        margin-right: .25rem!important;
        margin-left: .25rem!important
    }

    .mx-xl-2 {
        margin-right: .5rem!important;
        margin-left: .5rem!important
    }

    .mx-xl-3 {
        margin-right: 1rem!important;
        margin-left: 1rem!important
    }

    .mx-xl-4 {
        margin-right: 1.5rem!important;
        margin-left: 1.5rem!important
    }

    .mx-xl-5 {
        margin-right: 3rem!important;
        margin-left: 3rem!important
    }

    .mx-xl-6 {
        margin-right: 4.5rem!important;
        margin-left: 4.5rem!important
    }

    .mx-xl-7 {
        margin-right: 6rem!important;
        margin-left: 6rem!important
    }

    .mx-xl-8 {
        margin-right: 7.5rem!important;
        margin-left: 7.5rem!important
    }

    .mx-xl-9 {
        margin-right: 9rem!important;
        margin-left: 9rem!important
    }

    .mx-xl-10 {
        margin-right: 10.5rem!important;
        margin-left: 10.5rem!important
    }

    .mx-xl-auto {
        margin-right: auto!important;
        margin-left: auto!important
    }

    .my-xl-0 {
        margin-top: 0!important;
        margin-bottom: 0!important
    }

    .my-xl-1 {
        margin-top: .25rem!important;
        margin-bottom: .25rem!important
    }

    .my-xl-2 {
        margin-top: .5rem!important;
        margin-bottom: .5rem!important
    }

    .my-xl-3 {
        margin-top: 1rem!important;
        margin-bottom: 1rem!important
    }

    .my-xl-4 {
        margin-top: 1.5rem!important;
        margin-bottom: 1.5rem!important
    }

    .my-xl-5 {
        margin-top: 3rem!important;
        margin-bottom: 3rem!important
    }

    .my-xl-6 {
        margin-top: 4.5rem!important;
        margin-bottom: 4.5rem!important
    }

    .my-xl-7 {
        margin-top: 6rem!important;
        margin-bottom: 6rem!important
    }

    .my-xl-8 {
        margin-top: 7.5rem!important;
        margin-bottom: 7.5rem!important
    }

    .my-xl-9 {
        margin-top: 9rem!important;
        margin-bottom: 9rem!important
    }

    .my-xl-10 {
        margin-top: 10.5rem!important;
        margin-bottom: 10.5rem!important
    }

    .my-xl-auto {
        margin-top: auto!important;
        margin-bottom: auto!important
    }

    .mt-xl-0 {
        margin-top: 0!important
    }

    .mt-xl-1 {
        margin-top: .25rem!important
    }

    .mt-xl-2 {
        margin-top: .5rem!important
    }

    .mt-xl-3 {
        margin-top: 1rem!important
    }

    .mt-xl-4 {
        margin-top: 1.5rem!important
    }

    .mt-xl-5 {
        margin-top: 3rem!important
    }

    .mt-xl-6 {
        margin-top: 4.5rem!important
    }

    .mt-xl-7 {
        margin-top: 6rem!important
    }

    .mt-xl-8 {
        margin-top: 7.5rem!important
    }

    .mt-xl-9 {
        margin-top: 9rem!important
    }

    .mt-xl-10 {
        margin-top: 10.5rem!important
    }

    .mt-xl-auto {
        margin-top: auto!important
    }

    .me-xl-0 {
        margin-right: 0!important
    }

    .me-xl-1 {
        margin-right: .25rem!important
    }

    .me-xl-2 {
        margin-right: .5rem!important
    }

    .me-xl-3 {
        margin-right: 1rem!important
    }

    .me-xl-4 {
        margin-right: 1.5rem!important
    }

    .me-xl-5 {
        margin-right: 3rem!important
    }

    .me-xl-6 {
        margin-right: 4.5rem!important
    }

    .me-xl-7 {
        margin-right: 6rem!important
    }

    .me-xl-8 {
        margin-right: 7.5rem!important
    }

    .me-xl-9 {
        margin-right: 9rem!important
    }

    .me-xl-10 {
        margin-right: 10.5rem!important
    }

    .me-xl-auto {
        margin-right: auto!important
    }

    .mb-xl-0 {
        margin-bottom: 0!important
    }

    .mb-xl-1 {
        margin-bottom: .25rem!important
    }

    .mb-xl-2 {
        margin-bottom: .5rem!important
    }

    .mb-xl-3 {
        margin-bottom: 1rem!important
    }

    .mb-xl-4 {
        margin-bottom: 1.5rem!important
    }

    .mb-xl-5 {
        margin-bottom: 3rem!important
    }

    .mb-xl-6 {
        margin-bottom: 4.5rem!important
    }

    .mb-xl-7 {
        margin-bottom: 6rem!important
    }

    .mb-xl-8 {
        margin-bottom: 7.5rem!important
    }

    .mb-xl-9 {
        margin-bottom: 9rem!important
    }

    .mb-xl-10 {
        margin-bottom: 10.5rem!important
    }

    .mb-xl-auto {
        margin-bottom: auto!important
    }

    .ms-xl-0 {
        margin-left: 0!important
    }

    .ms-xl-1 {
        margin-left: .25rem!important
    }

    .ms-xl-2 {
        margin-left: .5rem!important
    }

    .ms-xl-3 {
        margin-left: 1rem!important
    }

    .ms-xl-4 {
        margin-left: 1.5rem!important
    }

    .ms-xl-5 {
        margin-left: 3rem!important
    }

    .ms-xl-6 {
        margin-left: 4.5rem!important
    }

    .ms-xl-7 {
        margin-left: 6rem!important
    }

    .ms-xl-8 {
        margin-left: 7.5rem!important
    }

    .ms-xl-9 {
        margin-left: 9rem!important
    }

    .ms-xl-10 {
        margin-left: 10.5rem!important
    }

    .ms-xl-auto {
        margin-left: auto!important
    }

    .p-xl-0 {
        padding: 0!important
    }

    .p-xl-1 {
        padding: .25rem!important
    }

    .p-xl-2 {
        padding: .5rem!important
    }

    .p-xl-3 {
        padding: 1rem!important
    }

    .p-xl-4 {
        padding: 1.5rem!important
    }

    .p-xl-5 {
        padding: 3rem!important
    }

    .p-xl-6 {
        padding: 4.5rem!important
    }

    .p-xl-7 {
        padding: 6rem!important
    }

    .p-xl-8 {
        padding: 7.5rem!important
    }

    .p-xl-9 {
        padding: 9rem!important
    }

    .p-xl-10 {
        padding: 10.5rem!important
    }

    .px-xl-0 {
        padding-right: 0!important;
        padding-left: 0!important
    }

    .px-xl-1 {
        padding-right: .25rem!important;
        padding-left: .25rem!important
    }

    .px-xl-2 {
        padding-right: .5rem!important;
        padding-left: .5rem!important
    }

    .px-xl-3 {
        padding-right: 1rem!important;
        padding-left: 1rem!important
    }

    .px-xl-4 {
        padding-right: 1.5rem!important;
        padding-left: 1.5rem!important
    }

    .px-xl-5 {
        padding-right: 3rem!important;
        padding-left: 3rem!important
    }

    .px-xl-6 {
        padding-right: 4.5rem!important;
        padding-left: 4.5rem!important
    }

    .px-xl-7 {
        padding-right: 6rem!important;
        padding-left: 6rem!important
    }

    .px-xl-8 {
        padding-right: 7.5rem!important;
        padding-left: 7.5rem!important
    }

    .px-xl-9 {
        padding-right: 9rem!important;
        padding-left: 9rem!important
    }

    .px-xl-10 {
        padding-right: 10.5rem!important;
        padding-left: 10.5rem!important
    }

    .py-xl-0 {
        padding-top: 0!important;
        padding-bottom: 0!important
    }

    .py-xl-1 {
        padding-top: .25rem!important;
        padding-bottom: .25rem!important
    }

    .py-xl-2 {
        padding-top: .5rem!important;
        padding-bottom: .5rem!important
    }

    .py-xl-3 {
        padding-top: 1rem!important;
        padding-bottom: 1rem!important
    }

    .py-xl-4 {
        padding-top: 1.5rem!important;
        padding-bottom: 1.5rem!important
    }

    .py-xl-5 {
        padding-top: 3rem!important;
        padding-bottom: 3rem!important
    }

    .py-xl-6 {
        padding-top: 4.5rem!important;
        padding-bottom: 4.5rem!important
    }

    .py-xl-7 {
        padding-top: 6rem!important;
        padding-bottom: 6rem!important
    }

    .py-xl-8 {
        padding-top: 7.5rem!important;
        padding-bottom: 7.5rem!important
    }

    .py-xl-9 {
        padding-top: 9rem!important;
        padding-bottom: 9rem!important
    }

    .py-xl-10 {
        padding-top: 10.5rem!important;
        padding-bottom: 10.5rem!important
    }

    .pt-xl-0 {
        padding-top: 0!important
    }

    .pt-xl-1 {
        padding-top: .25rem!important
    }

    .pt-xl-2 {
        padding-top: .5rem!important
    }

    .pt-xl-3 {
        padding-top: 1rem!important
    }

    .pt-xl-4 {
        padding-top: 1.5rem!important
    }

    .pt-xl-5 {
        padding-top: 3rem!important
    }

    .pt-xl-6 {
        padding-top: 4.5rem!important
    }

    .pt-xl-7 {
        padding-top: 6rem!important
    }

    .pt-xl-8 {
        padding-top: 7.5rem!important
    }

    .pt-xl-9 {
        padding-top: 9rem!important
    }

    .pt-xl-10 {
        padding-top: 10.5rem!important
    }

    .pe-xl-0 {
        padding-right: 0!important
    }

    .pe-xl-1 {
        padding-right: .25rem!important
    }

    .pe-xl-2 {
        padding-right: .5rem!important
    }

    .pe-xl-3 {
        padding-right: 1rem!important
    }

    .pe-xl-4 {
        padding-right: 1.5rem!important
    }

    .pe-xl-5 {
        padding-right: 3rem!important
    }

    .pe-xl-6 {
        padding-right: 4.5rem!important
    }

    .pe-xl-7 {
        padding-right: 6rem!important
    }

    .pe-xl-8 {
        padding-right: 7.5rem!important
    }

    .pe-xl-9 {
        padding-right: 9rem!important
    }

    .pe-xl-10 {
        padding-right: 10.5rem!important
    }

    .pb-xl-0 {
        padding-bottom: 0!important
    }

    .pb-xl-1 {
        padding-bottom: .25rem!important
    }

    .pb-xl-2 {
        padding-bottom: .5rem!important
    }

    .pb-xl-3 {
        padding-bottom: 1rem!important
    }

    .pb-xl-4 {
        padding-bottom: 1.5rem!important
    }

    .pb-xl-5 {
        padding-bottom: 3rem!important
    }

    .pb-xl-6 {
        padding-bottom: 4.5rem!important
    }

    .pb-xl-7 {
        padding-bottom: 6rem!important
    }

    .pb-xl-8 {
        padding-bottom: 7.5rem!important
    }

    .pb-xl-9 {
        padding-bottom: 9rem!important
    }

    .pb-xl-10 {
        padding-bottom: 10.5rem!important
    }

    .ps-xl-0 {
        padding-left: 0!important
    }

    .ps-xl-1 {
        padding-left: .25rem!important
    }

    .ps-xl-2 {
        padding-left: .5rem!important
    }

    .ps-xl-3 {
        padding-left: 1rem!important
    }

    .ps-xl-4 {
        padding-left: 1.5rem!important
    }

    .ps-xl-5 {
        padding-left: 3rem!important
    }

    .ps-xl-6 {
        padding-left: 4.5rem!important
    }

    .ps-xl-7 {
        padding-left: 6rem!important
    }

    .ps-xl-8 {
        padding-left: 7.5rem!important
    }

    .ps-xl-9 {
        padding-left: 9rem!important
    }

    .ps-xl-10 {
        padding-left: 10.5rem!important
    }

    .gap-xl-0 {
        gap: 0!important
    }

    .gap-xl-1 {
        gap: .25rem!important
    }

    .gap-xl-2 {
        gap: .5rem!important
    }

    .gap-xl-3 {
        gap: 1rem!important
    }

    .gap-xl-4 {
        gap: 1.5rem!important
    }

    .gap-xl-5 {
        gap: 3rem!important
    }

    .gap-xl-6 {
        gap: 4.5rem!important
    }

    .gap-xl-7 {
        gap: 6rem!important
    }

    .gap-xl-8 {
        gap: 7.5rem!important
    }

    .gap-xl-9 {
        gap: 9rem!important
    }

    .gap-xl-10 {
        gap: 10.5rem!important
    }

    .row-gap-xl-0 {
        row-gap: 0!important
    }

    .row-gap-xl-1 {
        row-gap: .25rem!important
    }

    .row-gap-xl-2 {
        row-gap: .5rem!important
    }

    .row-gap-xl-3 {
        row-gap: 1rem!important
    }

    .row-gap-xl-4 {
        row-gap: 1.5rem!important
    }

    .row-gap-xl-5 {
        row-gap: 3rem!important
    }

    .row-gap-xl-6 {
        row-gap: 4.5rem!important
    }

    .row-gap-xl-7 {
        row-gap: 6rem!important
    }

    .row-gap-xl-8 {
        row-gap: 7.5rem!important
    }

    .row-gap-xl-9 {
        row-gap: 9rem!important
    }

    .row-gap-xl-10 {
        row-gap: 10.5rem!important
    }

    .column-gap-xl-0 {
        -webkit-column-gap: 0!important;
        -moz-column-gap: 0!important;
        column-gap: 0!important
    }

    .column-gap-xl-1 {
        -webkit-column-gap: .25rem!important;
        -moz-column-gap: .25rem!important;
        column-gap: .25rem!important
    }

    .column-gap-xl-2 {
        -webkit-column-gap: .5rem!important;
        -moz-column-gap: .5rem!important;
        column-gap: .5rem!important
    }

    .column-gap-xl-3 {
        -webkit-column-gap: 1rem!important;
        -moz-column-gap: 1rem!important;
        column-gap: 1rem!important
    }

    .column-gap-xl-4 {
        -webkit-column-gap: 1.5rem!important;
        -moz-column-gap: 1.5rem!important;
        column-gap: 1.5rem!important
    }

    .column-gap-xl-5 {
        -webkit-column-gap: 3rem!important;
        -moz-column-gap: 3rem!important;
        column-gap: 3rem!important
    }

    .column-gap-xl-6 {
        -webkit-column-gap: 4.5rem!important;
        -moz-column-gap: 4.5rem!important;
        column-gap: 4.5rem!important
    }

    .column-gap-xl-7 {
        -webkit-column-gap: 6rem!important;
        -moz-column-gap: 6rem!important;
        column-gap: 6rem!important
    }

    .column-gap-xl-8 {
        -webkit-column-gap: 7.5rem!important;
        -moz-column-gap: 7.5rem!important;
        column-gap: 7.5rem!important
    }

    .column-gap-xl-9 {
        -webkit-column-gap: 9rem!important;
        -moz-column-gap: 9rem!important;
        column-gap: 9rem!important
    }

    .column-gap-xl-10 {
        -webkit-column-gap: 10.5rem!important;
        -moz-column-gap: 10.5rem!important;
        column-gap: 10.5rem!important
    }

    .text-xl-start {
        text-align: left!important
    }

    .text-xl-end {
        text-align: right!important
    }

    .text-xl-center {
        text-align: center!important
    }

    .fs-1 {
        font-size: 2.34375rem!important
    }

    .fs-2 {
        font-size: 1.875rem!important
    }

    .fs-3 {
        font-size: 1.640625rem!important
    }

    .fs-4 {
        font-size: 1.40625rem!important
    }
}

@media (min-width: 1400px) {
    .container,.container-lg,.container-md,.container-sm,.container-xl,.container-xxl {
        max-width:1280px
    }

    .col-xxl {
        -webkit-box-flex: 1;
        -ms-flex: 1 0 0%;
        flex: 1 0 0%
    }

    .row-cols-xxl-auto>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }

    .row-cols-xxl-1>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 100%
    }

    .row-cols-xxl-2>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 50%
    }

    .row-cols-xxl-3>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 33.3333333333%
    }

    .row-cols-xxl-4>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 25%
    }

    .row-cols-xxl-5>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 20%
    }

    .row-cols-xxl-6>* {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 16.6666666667%
    }

    .col-xxl-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }

    .col-xxl-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 8.33333333%
    }

    .col-xxl-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 16.66666667%
    }

    .col-xxl-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 25%
    }

    .col-xxl-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 33.33333333%
    }

    .col-xxl-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 41.66666667%
    }

    .col-xxl-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 50%
    }

    .col-xxl-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 58.33333333%
    }

    .col-xxl-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 66.66666667%
    }

    .col-xxl-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 75%
    }

    .col-xxl-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 83.33333333%
    }

    .col-xxl-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 91.66666667%
    }

    .col-xxl-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 100%
    }

    .offset-xxl-0 {
        margin-left: 0
    }

    .offset-xxl-1 {
        margin-left: 8.33333333%
    }

    .offset-xxl-2 {
        margin-left: 16.66666667%
    }

    .offset-xxl-3 {
        margin-left: 25%
    }

    .offset-xxl-4 {
        margin-left: 33.33333333%
    }

    .offset-xxl-5 {
        margin-left: 41.66666667%
    }

    .offset-xxl-6 {
        margin-left: 50%
    }

    .offset-xxl-7 {
        margin-left: 58.33333333%
    }

    .offset-xxl-8 {
        margin-left: 66.66666667%
    }

    .offset-xxl-9 {
        margin-left: 75%
    }

    .offset-xxl-10 {
        margin-left: 83.33333333%
    }

    .offset-xxl-11 {
        margin-left: 91.66666667%
    }

    .g-xxl-0,.gx-xxl-0 {
        --ri-gutter-x: 0
    }

    .g-xxl-0,.gy-xxl-0 {
        --ri-gutter-y: 0
    }

    .g-xxl-1,.gx-xxl-1 {
        --ri-gutter-x: 0.25rem
    }

    .g-xxl-1,.gy-xxl-1 {
        --ri-gutter-y: 0.25rem
    }

    .g-xxl-2,.gx-xxl-2 {
        --ri-gutter-x: 0.5rem
    }

    .g-xxl-2,.gy-xxl-2 {
        --ri-gutter-y: 0.5rem
    }

    .g-xxl-3,.gx-xxl-3 {
        --ri-gutter-x: 1rem
    }

    .g-xxl-3,.gy-xxl-3 {
        --ri-gutter-y: 1rem
    }

    .g-xxl-4,.gx-xxl-4 {
        --ri-gutter-x: 1.5rem
    }

    .g-xxl-4,.gy-xxl-4 {
        --ri-gutter-y: 1.5rem
    }

    .g-xxl-5,.gx-xxl-5 {
        --ri-gutter-x: 3rem
    }

    .g-xxl-5,.gy-xxl-5 {
        --ri-gutter-y: 3rem
    }

    .g-xxl-6,.gx-xxl-6 {
        --ri-gutter-x: 4.5rem
    }

    .g-xxl-6,.gy-xxl-6 {
        --ri-gutter-y: 4.5rem
    }

    .g-xxl-7,.gx-xxl-7 {
        --ri-gutter-x: 6rem
    }

    .g-xxl-7,.gy-xxl-7 {
        --ri-gutter-y: 6rem
    }

    .g-xxl-8,.gx-xxl-8 {
        --ri-gutter-x: 7.5rem
    }

    .g-xxl-8,.gy-xxl-8 {
        --ri-gutter-y: 7.5rem
    }

    .g-xxl-9,.gx-xxl-9 {
        --ri-gutter-x: 9rem
    }

    .g-xxl-9,.gy-xxl-9 {
        --ri-gutter-y: 9rem
    }

    .g-xxl-10,.gx-xxl-10 {
        --ri-gutter-x: 10.5rem
    }

    .g-xxl-10,.gy-xxl-10 {
        --ri-gutter-y: 10.5rem
    }

    .list-group-horizontal-xxl {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row
    }

    .list-group-horizontal-xxl>.list-group-item:first-child:not(:last-child) {
        border-bottom-left-radius: var(--ri-list-group-border-radius);
        border-top-right-radius: 0
    }

    .list-group-horizontal-xxl>.list-group-item:last-child:not(:first-child) {
        border-top-right-radius: var(--ri-list-group-border-radius);
        border-bottom-left-radius: 0
    }

    .list-group-horizontal-xxl>.list-group-item.active {
        margin-top: 0
    }

    .list-group-horizontal-xxl>.list-group-item+.list-group-item {
        border-top-width: var(--ri-list-group-border-width);
        border-left-width: 0
    }

    .list-group-horizontal-xxl>.list-group-item+.list-group-item.active {
        margin-left: calc(-1 * var(--ri-list-group-border-width));
        border-left-width: var(--ri-list-group-border-width)
    }

    .sticky-xxl-top {
        position: sticky;
        top: 0;
        z-index: 1020
    }

    .sticky-xxl-bottom {
        position: sticky;
        bottom: 0;
        z-index: 1020
    }

    .float-xxl-start {
        float: left!important
    }

    .float-xxl-end {
        float: right!important
    }

    .float-xxl-none {
        float: none!important
    }

    .object-fit-xxl-contain {
        -o-object-fit: contain!important;
        object-fit: contain!important
    }

    .object-fit-xxl-cover {
        -o-object-fit: cover!important;
        object-fit: cover!important
    }

    .object-fit-xxl-fill {
        -o-object-fit: fill!important;
        object-fit: fill!important
    }

    .object-fit-xxl-scale {
        -o-object-fit: scale-down!important;
        object-fit: scale-down!important
    }

    .object-fit-xxl-none {
        -o-object-fit: none!important;
        object-fit: none!important
    }

    .d-xxl-inline {
        display: inline!important
    }

    .d-xxl-inline-block {
        display: inline-block!important
    }

    .d-xxl-block {
        display: block!important
    }

    .d-xxl-grid {
        display: grid!important
    }

    .d-xxl-table {
        display: table!important
    }

    .d-xxl-table-row {
        display: table-row!important
    }

    .d-xxl-table-cell {
        display: table-cell!important
    }

    .d-xxl-flex {
        display: -webkit-box!important;
        display: -ms-flexbox!important;
        display: flex!important
    }

    .d-xxl-inline-flex {
        display: -webkit-inline-box!important;
        display: -ms-inline-flexbox!important;
        display: inline-flex!important
    }

    .d-xxl-none {
        display: none!important
    }

    .flex-xxl-fill {
        -webkit-box-flex: 1!important;
        -ms-flex: 1 1 auto!important;
        flex: 1 1 auto!important
    }

    .flex-xxl-row {
        -webkit-box-orient: horizontal!important;
        -webkit-box-direction: normal!important;
        -ms-flex-direction: row!important;
        flex-direction: row!important
    }

    .flex-xxl-column {
        -webkit-box-orient: vertical!important;
        -webkit-box-direction: normal!important;
        -ms-flex-direction: column!important;
        flex-direction: column!important
    }

    .flex-xxl-row-reverse {
        -webkit-box-orient: horizontal!important;
        -webkit-box-direction: reverse!important;
        -ms-flex-direction: row-reverse!important;
        flex-direction: row-reverse!important
    }

    .flex-xxl-column-reverse {
        -webkit-box-orient: vertical!important;
        -webkit-box-direction: reverse!important;
        -ms-flex-direction: column-reverse!important;
        flex-direction: column-reverse!important
    }

    .flex-xxl-grow-0 {
        -webkit-box-flex: 0!important;
        -ms-flex-positive: 0!important;
        flex-grow: 0!important
    }

    .flex-xxl-grow-1 {
        -webkit-box-flex: 1!important;
        -ms-flex-positive: 1!important;
        flex-grow: 1!important
    }

    .flex-xxl-shrink-0 {
        -ms-flex-negative: 0!important;
        flex-shrink: 0!important
    }

    .flex-xxl-shrink-1 {
        -ms-flex-negative: 1!important;
        flex-shrink: 1!important
    }

    .flex-xxl-wrap {
        -ms-flex-wrap: wrap!important;
        flex-wrap: wrap!important
    }

    .flex-xxl-nowrap {
        -ms-flex-wrap: nowrap!important;
        flex-wrap: nowrap!important
    }

    .flex-xxl-wrap-reverse {
        -ms-flex-wrap: wrap-reverse!important;
        flex-wrap: wrap-reverse!important
    }

    .justify-content-xxl-start {
        -webkit-box-pack: start!important;
        -ms-flex-pack: start!important;
        justify-content: flex-start!important
    }

    .justify-content-xxl-end {
        -webkit-box-pack: end!important;
        -ms-flex-pack: end!important;
        justify-content: flex-end!important
    }

    .justify-content-xxl-center {
        -webkit-box-pack: center!important;
        -ms-flex-pack: center!important;
        justify-content: center!important
    }

    .justify-content-xxl-between {
        -webkit-box-pack: justify!important;
        -ms-flex-pack: justify!important;
        justify-content: space-between!important
    }

    .justify-content-xxl-around {
        -ms-flex-pack: distribute!important;
        justify-content: space-around!important
    }

    .justify-content-xxl-evenly {
        -webkit-box-pack: space-evenly!important;
        -ms-flex-pack: space-evenly!important;
        justify-content: space-evenly!important
    }

    .align-items-xxl-start {
        -webkit-box-align: start!important;
        -ms-flex-align: start!important;
        align-items: flex-start!important
    }

    .align-items-xxl-end {
        -webkit-box-align: end!important;
        -ms-flex-align: end!important;
        align-items: flex-end!important
    }

    .align-items-xxl-center {
        -webkit-box-align: center!important;
        -ms-flex-align: center!important;
        align-items: center!important
    }

    .align-items-xxl-baseline {
        -webkit-box-align: baseline!important;
        -ms-flex-align: baseline!important;
        align-items: baseline!important
    }

    .align-items-xxl-stretch {
        -webkit-box-align: stretch!important;
        -ms-flex-align: stretch!important;
        align-items: stretch!important
    }

    .align-content-xxl-start {
        -ms-flex-line-pack: start!important;
        align-content: flex-start!important
    }

    .align-content-xxl-end {
        -ms-flex-line-pack: end!important;
        align-content: flex-end!important
    }

    .align-content-xxl-center {
        -ms-flex-line-pack: center!important;
        align-content: center!important
    }

    .align-content-xxl-between {
        -ms-flex-line-pack: justify!important;
        align-content: space-between!important
    }

    .align-content-xxl-around {
        -ms-flex-line-pack: distribute!important;
        align-content: space-around!important
    }

    .align-content-xxl-stretch {
        -ms-flex-line-pack: stretch!important;
        align-content: stretch!important
    }

    .align-self-xxl-auto {
        -ms-flex-item-align: auto!important;
        align-self: auto!important
    }

    .align-self-xxl-start {
        -ms-flex-item-align: start!important;
        align-self: flex-start!important
    }

    .align-self-xxl-end {
        -ms-flex-item-align: end!important;
        align-self: flex-end!important
    }

    .align-self-xxl-center {
        -ms-flex-item-align: center!important;
        align-self: center!important
    }

    .align-self-xxl-baseline {
        -ms-flex-item-align: baseline!important;
        align-self: baseline!important
    }

    .align-self-xxl-stretch {
        -ms-flex-item-align: stretch!important;
        align-self: stretch!important
    }

    .order-xxl-first {
        -webkit-box-ordinal-group: 0!important;
        -ms-flex-order: -1!important;
        order: -1!important
    }

    .order-xxl-0 {
        -webkit-box-ordinal-group: 1!important;
        -ms-flex-order: 0!important;
        order: 0!important
    }

    .order-xxl-1 {
        -webkit-box-ordinal-group: 2!important;
        -ms-flex-order: 1!important;
        order: 1!important
    }

    .order-xxl-2 {
        -webkit-box-ordinal-group: 3!important;
        -ms-flex-order: 2!important;
        order: 2!important
    }

    .order-xxl-3 {
        -webkit-box-ordinal-group: 4!important;
        -ms-flex-order: 3!important;
        order: 3!important
    }

    .order-xxl-4 {
        -webkit-box-ordinal-group: 5!important;
        -ms-flex-order: 4!important;
        order: 4!important
    }

    .order-xxl-5 {
        -webkit-box-ordinal-group: 6!important;
        -ms-flex-order: 5!important;
        order: 5!important
    }

    .order-xxl-last {
        -webkit-box-ordinal-group: 7!important;
        -ms-flex-order: 6!important;
        order: 6!important
    }

    .m-xxl-0 {
        margin: 0!important
    }

    .m-xxl-1 {
        margin: .25rem!important
    }

    .m-xxl-2 {
        margin: .5rem!important
    }

    .m-xxl-3 {
        margin: 1rem!important
    }

    .m-xxl-4 {
        margin: 1.5rem!important
    }

    .m-xxl-5 {
        margin: 3rem!important
    }

    .m-xxl-6 {
        margin: 4.5rem!important
    }

    .m-xxl-7 {
        margin: 6rem!important
    }

    .m-xxl-8 {
        margin: 7.5rem!important
    }

    .m-xxl-9 {
        margin: 9rem!important
    }

    .m-xxl-10 {
        margin: 10.5rem!important
    }

    .m-xxl-auto {
        margin: auto!important
    }

    .mx-xxl-0 {
        margin-right: 0!important;
        margin-left: 0!important
    }

    .mx-xxl-1 {
        margin-right: .25rem!important;
        margin-left: .25rem!important
    }

    .mx-xxl-2 {
        margin-right: .5rem!important;
        margin-left: .5rem!important
    }

    .mx-xxl-3 {
        margin-right: 1rem!important;
        margin-left: 1rem!important
    }

    .mx-xxl-4 {
        margin-right: 1.5rem!important;
        margin-left: 1.5rem!important
    }

    .mx-xxl-5 {
        margin-right: 3rem!important;
        margin-left: 3rem!important
    }

    .mx-xxl-6 {
        margin-right: 4.5rem!important;
        margin-left: 4.5rem!important
    }

    .mx-xxl-7 {
        margin-right: 6rem!important;
        margin-left: 6rem!important
    }

    .mx-xxl-8 {
        margin-right: 7.5rem!important;
        margin-left: 7.5rem!important
    }

    .mx-xxl-9 {
        margin-right: 9rem!important;
        margin-left: 9rem!important
    }

    .mx-xxl-10 {
        margin-right: 10.5rem!important;
        margin-left: 10.5rem!important
    }

    .mx-xxl-auto {
        margin-right: auto!important;
        margin-left: auto!important
    }

    .my-xxl-0 {
        margin-top: 0!important;
        margin-bottom: 0!important
    }

    .my-xxl-1 {
        margin-top: .25rem!important;
        margin-bottom: .25rem!important
    }

    .my-xxl-2 {
        margin-top: .5rem!important;
        margin-bottom: .5rem!important
    }

    .my-xxl-3 {
        margin-top: 1rem!important;
        margin-bottom: 1rem!important
    }

    .my-xxl-4 {
        margin-top: 1.5rem!important;
        margin-bottom: 1.5rem!important
    }

    .my-xxl-5 {
        margin-top: 3rem!important;
        margin-bottom: 3rem!important
    }

    .my-xxl-6 {
        margin-top: 4.5rem!important;
        margin-bottom: 4.5rem!important
    }

    .my-xxl-7 {
        margin-top: 6rem!important;
        margin-bottom: 6rem!important
    }

    .my-xxl-8 {
        margin-top: 7.5rem!important;
        margin-bottom: 7.5rem!important
    }

    .my-xxl-9 {
        margin-top: 9rem!important;
        margin-bottom: 9rem!important
    }

    .my-xxl-10 {
        margin-top: 10.5rem!important;
        margin-bottom: 10.5rem!important
    }

    .my-xxl-auto {
        margin-top: auto!important;
        margin-bottom: auto!important
    }

    .mt-xxl-0 {
        margin-top: 0!important
    }

    .mt-xxl-1 {
        margin-top: .25rem!important
    }

    .mt-xxl-2 {
        margin-top: .5rem!important
    }

    .mt-xxl-3 {
        margin-top: 1rem!important
    }

    .mt-xxl-4 {
        margin-top: 1.5rem!important
    }

    .mt-xxl-5 {
        margin-top: 3rem!important
    }

    .mt-xxl-6 {
        margin-top: 4.5rem!important
    }

    .mt-xxl-7 {
        margin-top: 6rem!important
    }

    .mt-xxl-8 {
        margin-top: 7.5rem!important
    }

    .mt-xxl-9 {
        margin-top: 9rem!important
    }

    .mt-xxl-10 {
        margin-top: 10.5rem!important
    }

    .mt-xxl-auto {
        margin-top: auto!important
    }

    .me-xxl-0 {
        margin-right: 0!important
    }

    .me-xxl-1 {
        margin-right: .25rem!important
    }

    .me-xxl-2 {
        margin-right: .5rem!important
    }

    .me-xxl-3 {
        margin-right: 1rem!important
    }

    .me-xxl-4 {
        margin-right: 1.5rem!important
    }

    .me-xxl-5 {
        margin-right: 3rem!important
    }

    .me-xxl-6 {
        margin-right: 4.5rem!important
    }

    .me-xxl-7 {
        margin-right: 6rem!important
    }

    .me-xxl-8 {
        margin-right: 7.5rem!important
    }

    .me-xxl-9 {
        margin-right: 9rem!important
    }

    .me-xxl-10 {
        margin-right: 10.5rem!important
    }

    .me-xxl-auto {
        margin-right: auto!important
    }

    .mb-xxl-0 {
        margin-bottom: 0!important
    }

    .mb-xxl-1 {
        margin-bottom: .25rem!important
    }

    .mb-xxl-2 {
        margin-bottom: .5rem!important
    }

    .mb-xxl-3 {
        margin-bottom: 1rem!important
    }

    .mb-xxl-4 {
        margin-bottom: 1.5rem!important
    }

    .mb-xxl-5 {
        margin-bottom: 3rem!important
    }

    .mb-xxl-6 {
        margin-bottom: 4.5rem!important
    }

    .mb-xxl-7 {
        margin-bottom: 6rem!important
    }

    .mb-xxl-8 {
        margin-bottom: 7.5rem!important
    }

    .mb-xxl-9 {
        margin-bottom: 9rem!important
    }

    .mb-xxl-10 {
        margin-bottom: 10.5rem!important
    }

    .mb-xxl-auto {
        margin-bottom: auto!important
    }

    .ms-xxl-0 {
        margin-left: 0!important
    }

    .ms-xxl-1 {
        margin-left: .25rem!important
    }

    .ms-xxl-2 {
        margin-left: .5rem!important
    }

    .ms-xxl-3 {
        margin-left: 1rem!important
    }

    .ms-xxl-4 {
        margin-left: 1.5rem!important
    }

    .ms-xxl-5 {
        margin-left: 3rem!important
    }

    .ms-xxl-6 {
        margin-left: 4.5rem!important
    }

    .ms-xxl-7 {
        margin-left: 6rem!important
    }

    .ms-xxl-8 {
        margin-left: 7.5rem!important
    }

    .ms-xxl-9 {
        margin-left: 9rem!important
    }

    .ms-xxl-10 {
        margin-left: 10.5rem!important
    }

    .ms-xxl-auto {
        margin-left: auto!important
    }

    .p-xxl-0 {
        padding: 0!important
    }

    .p-xxl-1 {
        padding: .25rem!important
    }

    .p-xxl-2 {
        padding: .5rem!important
    }

    .p-xxl-3 {
        padding: 1rem!important
    }

    .p-xxl-4 {
        padding: 1.5rem!important
    }

    .p-xxl-5 {
        padding: 3rem!important
    }

    .p-xxl-6 {
        padding: 4.5rem!important
    }

    .p-xxl-7 {
        padding: 6rem!important
    }

    .p-xxl-8 {
        padding: 7.5rem!important
    }

    .p-xxl-9 {
        padding: 9rem!important
    }

    .p-xxl-10 {
        padding: 10.5rem!important
    }

    .px-xxl-0 {
        padding-right: 0!important;
        padding-left: 0!important
    }

    .px-xxl-1 {
        padding-right: .25rem!important;
        padding-left: .25rem!important
    }

    .px-xxl-2 {
        padding-right: .5rem!important;
        padding-left: .5rem!important
    }

    .px-xxl-3 {
        padding-right: 1rem!important;
        padding-left: 1rem!important
    }

    .px-xxl-4 {
        padding-right: 1.5rem!important;
        padding-left: 1.5rem!important
    }

    .px-xxl-5 {
        padding-right: 3rem!important;
        padding-left: 3rem!important
    }

    .px-xxl-6 {
        padding-right: 4.5rem!important;
        padding-left: 4.5rem!important
    }

    .px-xxl-7 {
        padding-right: 6rem!important;
        padding-left: 6rem!important
    }

    .px-xxl-8 {
        padding-right: 7.5rem!important;
        padding-left: 7.5rem!important
    }

    .px-xxl-9 {
        padding-right: 9rem!important;
        padding-left: 9rem!important
    }

    .px-xxl-10 {
        padding-right: 10.5rem!important;
        padding-left: 10.5rem!important
    }

    .py-xxl-0 {
        padding-top: 0!important;
        padding-bottom: 0!important
    }

    .py-xxl-1 {
        padding-top: .25rem!important;
        padding-bottom: .25rem!important
    }

    .py-xxl-2 {
        padding-top: .5rem!important;
        padding-bottom: .5rem!important
    }

    .py-xxl-3 {
        padding-top: 1rem!important;
        padding-bottom: 1rem!important
    }

    .py-xxl-4 {
        padding-top: 1.5rem!important;
        padding-bottom: 1.5rem!important
    }

    .py-xxl-5 {
        padding-top: 3rem!important;
        padding-bottom: 3rem!important
    }

    .py-xxl-6 {
        padding-top: 4.5rem!important;
        padding-bottom: 4.5rem!important
    }

    .py-xxl-7 {
        padding-top: 6rem!important;
        padding-bottom: 6rem!important
    }

    .py-xxl-8 {
        padding-top: 7.5rem!important;
        padding-bottom: 7.5rem!important
    }

    .py-xxl-9 {
        padding-top: 9rem!important;
        padding-bottom: 9rem!important
    }

    .py-xxl-10 {
        padding-top: 10.5rem!important;
        padding-bottom: 10.5rem!important
    }

    .pt-xxl-0 {
        padding-top: 0!important
    }

    .pt-xxl-1 {
        padding-top: .25rem!important
    }

    .pt-xxl-2 {
        padding-top: .5rem!important
    }

    .pt-xxl-3 {
        padding-top: 1rem!important
    }

    .pt-xxl-4 {
        padding-top: 1.5rem!important
    }

    .pt-xxl-5 {
        padding-top: 3rem!important
    }

    .pt-xxl-6 {
        padding-top: 4.5rem!important
    }

    .pt-xxl-7 {
        padding-top: 6rem!important
    }

    .pt-xxl-8 {
        padding-top: 7.5rem!important
    }

    .pt-xxl-9 {
        padding-top: 9rem!important
    }

    .pt-xxl-10 {
        padding-top: 10.5rem!important
    }

    .pe-xxl-0 {
        padding-right: 0!important
    }

    .pe-xxl-1 {
        padding-right: .25rem!important
    }

    .pe-xxl-2 {
        padding-right: .5rem!important
    }

    .pe-xxl-3 {
        padding-right: 1rem!important
    }

    .pe-xxl-4 {
        padding-right: 1.5rem!important
    }

    .pe-xxl-5 {
        padding-right: 3rem!important
    }

    .pe-xxl-6 {
        padding-right: 4.5rem!important
    }

    .pe-xxl-7 {
        padding-right: 6rem!important
    }

    .pe-xxl-8 {
        padding-right: 7.5rem!important
    }

    .pe-xxl-9 {
        padding-right: 9rem!important
    }

    .pe-xxl-10 {
        padding-right: 10.5rem!important
    }

    .pb-xxl-0 {
        padding-bottom: 0!important
    }

    .pb-xxl-1 {
        padding-bottom: .25rem!important
    }

    .pb-xxl-2 {
        padding-bottom: .5rem!important
    }

    .pb-xxl-3 {
        padding-bottom: 1rem!important
    }

    .pb-xxl-4 {
        padding-bottom: 1.5rem!important
    }

    .pb-xxl-5 {
        padding-bottom: 3rem!important
    }

    .pb-xxl-6 {
        padding-bottom: 4.5rem!important
    }

    .pb-xxl-7 {
        padding-bottom: 6rem!important
    }

    .pb-xxl-8 {
        padding-bottom: 7.5rem!important
    }

    .pb-xxl-9 {
        padding-bottom: 9rem!important
    }

    .pb-xxl-10 {
        padding-bottom: 10.5rem!important
    }

    .ps-xxl-0 {
        padding-left: 0!important
    }

    .ps-xxl-1 {
        padding-left: .25rem!important
    }

    .ps-xxl-2 {
        padding-left: .5rem!important
    }

    .ps-xxl-3 {
        padding-left: 1rem!important
    }

    .ps-xxl-4 {
        padding-left: 1.5rem!important
    }

    .ps-xxl-5 {
        padding-left: 3rem!important
    }

    .ps-xxl-6 {
        padding-left: 4.5rem!important
    }

    .ps-xxl-7 {
        padding-left: 6rem!important
    }

    .ps-xxl-8 {
        padding-left: 7.5rem!important
    }

    .ps-xxl-9 {
        padding-left: 9rem!important
    }

    .ps-xxl-10 {
        padding-left: 10.5rem!important
    }

    .gap-xxl-0 {
        gap: 0!important
    }

    .gap-xxl-1 {
        gap: .25rem!important
    }

    .gap-xxl-2 {
        gap: .5rem!important
    }

    .gap-xxl-3 {
        gap: 1rem!important
    }

    .gap-xxl-4 {
        gap: 1.5rem!important
    }

    .gap-xxl-5 {
        gap: 3rem!important
    }

    .gap-xxl-6 {
        gap: 4.5rem!important
    }

    .gap-xxl-7 {
        gap: 6rem!important
    }

    .gap-xxl-8 {
        gap: 7.5rem!important
    }

    .gap-xxl-9 {
        gap: 9rem!important
    }

    .gap-xxl-10 {
        gap: 10.5rem!important
    }

    .row-gap-xxl-0 {
        row-gap: 0!important
    }

    .row-gap-xxl-1 {
        row-gap: .25rem!important
    }

    .row-gap-xxl-2 {
        row-gap: .5rem!important
    }

    .row-gap-xxl-3 {
        row-gap: 1rem!important
    }

    .row-gap-xxl-4 {
        row-gap: 1.5rem!important
    }

    .row-gap-xxl-5 {
        row-gap: 3rem!important
    }

    .row-gap-xxl-6 {
        row-gap: 4.5rem!important
    }

    .row-gap-xxl-7 {
        row-gap: 6rem!important
    }

    .row-gap-xxl-8 {
        row-gap: 7.5rem!important
    }

    .row-gap-xxl-9 {
        row-gap: 9rem!important
    }

    .row-gap-xxl-10 {
        row-gap: 10.5rem!important
    }

    .column-gap-xxl-0 {
        -webkit-column-gap: 0!important;
        -moz-column-gap: 0!important;
        column-gap: 0!important
    }

    .column-gap-xxl-1 {
        -webkit-column-gap: .25rem!important;
        -moz-column-gap: .25rem!important;
        column-gap: .25rem!important
    }

    .column-gap-xxl-2 {
        -webkit-column-gap: .5rem!important;
        -moz-column-gap: .5rem!important;
        column-gap: .5rem!important
    }

    .column-gap-xxl-3 {
        -webkit-column-gap: 1rem!important;
        -moz-column-gap: 1rem!important;
        column-gap: 1rem!important
    }

    .column-gap-xxl-4 {
        -webkit-column-gap: 1.5rem!important;
        -moz-column-gap: 1.5rem!important;
        column-gap: 1.5rem!important
    }

    .column-gap-xxl-5 {
        -webkit-column-gap: 3rem!important;
        -moz-column-gap: 3rem!important;
        column-gap: 3rem!important
    }

    .column-gap-xxl-6 {
        -webkit-column-gap: 4.5rem!important;
        -moz-column-gap: 4.5rem!important;
        column-gap: 4.5rem!important
    }

    .column-gap-xxl-7 {
        -webkit-column-gap: 6rem!important;
        -moz-column-gap: 6rem!important;
        column-gap: 6rem!important
    }

    .column-gap-xxl-8 {
        -webkit-column-gap: 7.5rem!important;
        -moz-column-gap: 7.5rem!important;
        column-gap: 7.5rem!important
    }

    .column-gap-xxl-9 {
        -webkit-column-gap: 9rem!important;
        -moz-column-gap: 9rem!important;
        column-gap: 9rem!important
    }

    .column-gap-xxl-10 {
        -webkit-column-gap: 10.5rem!important;
        -moz-column-gap: 10.5rem!important;
        column-gap: 10.5rem!important
    }

    .text-xxl-start {
        text-align: left!important
    }

    .text-xxl-end {
        text-align: right!important
    }

    .text-xxl-center {
        text-align: center!important
    }
}

@media (max-width: 991.98px) {
    .post-item .entry-media .media-preview .centered-html-cd {
        -webkit-transform:translate(-50%,-50%) scale(.35);
        transform: translate(-50%,-50%) scale(.35)
    }

    .post-item .entry-title {
        font-size: .875rem
    }

    .post-item .entry-desc {
        margin-top: 2px;
        font-size: .75rem
    }

    .post-item .entry-cat-dot {
        margin-bottom: 2px
    }

    .post-item.item-list {
        padding: .5rem
    }

    .post-item.item-list .entry-media {
        max-width: 140px;
        margin-right: .5rem
    }

    .post-item.item-list .entry-title {
        font-size: .95rem;
        -webkit-line-clamp: 1
    }

    .video-hero .video-hero-container {
        padding-top: 0;
        padding-bottom: 0;
        padding-left: 0;
        padding-right: 0
    }

    .centered-html-cd {
        -webkit-transform: translate(-50%,-50%) scale(.6);
        transform: translate(-50%,-50%) scale(.6)
    }

    .ri-video-list {
        padding: .5rem
    }

    .ri-video-list .video-title {
        font-size: .875rem
    }

    .ri-video-list .video-nav .switch-video {
        width: 40px;
        height: 25px;
        font-size: .875rem
    }

    .archive-media-preview .centered-html-cd {
        -webkit-transform: translate(-50%,-50%) scale(.35);
        transform: translate(-50%,-50%) scale(.35)
    }
}

@media (max-width: 767.98px) {
    .sidebar .widget {
        padding:1rem;
        margin-bottom: 1rem
    }

    .home-search-box .search-warp {
        padding-top: 40px;
        padding-bottom: 40px
    }

    .home-search-box .search-desc {
        display: none
    }

    .home-search-box .search-title {
        font-size: 1.5rem
    }

    .home-search-box .search-desc {
        margin-bottom: 1rem;
        font-size: .875rem
    }

    .home-search-box .search-warp {
        max-width: 100%
    }

    .home-search-box .search-hots {
        font-size: .875rem
    }

    .home-cat-box .widget-catbox-item {
        height: 75px
    }

    .home-division .division-item {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        text-align: center
    }

    .home-division .division-icon {
        margin-bottom: .35rem;
        padding: 1rem
    }

    .home-division .division-warp {
        margin-left: .5rem
    }

    .home-division .division-title {
        font-size: 1rem;
        margin-bottom: 0
    }

    .home-division .division-desc {
        font-size: .75rem
    }

    .home-scratch-card .scratch-item .code-content {
        padding: 1rem
    }

    .home-scratch-card .scratch-item .code-title {
        font-size: 1rem
    }

    .home-scratch-card .scratch-item .gift {
        width: 50px
    }

    .home-vip-card .price-card .price-body .price-desc {
        display: none
    }

    .home-vip-card .price-card .price-body {
        padding: .5rem 0
    }

    .home-vip-card .price-card .price-footer {
        padding-bottom: 1rem
    }

    .post-item .entry-media .media-preview .centered-html-cd {
        -webkit-transform: translate(-50%,-50%) scale(.3);
        transform: translate(-50%,-50%) scale(.3)
    }

    .centered-html-cd {
        -webkit-transform: translate(-50%,-50%) scale(.4);
        transform: translate(-50%,-50%) scale(.4)
    }

    .archive-media-preview .centered-html-cd {
        -webkit-transform: translate(-50%,-50%) scale(.2);
        transform: translate(-50%,-50%) scale(.2)
    }

    .filter-warp .filter-item {
        padding: 4px 0;
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
        overflow-x: auto
    }

    .filter-warp .filter-item .filter-link,.filter-warp .filter-item .filter-name {
        margin-right: 4px;
        margin-top: 0
    }

    .filter-warp .filter-item .filter-name {
        min-width: auto
    }

    .rollbar {
        display: none
    }

    .back-top {
        display: none;
        bottom: 10px;
        right: 20px
    }

    .site-footer {
        margin-bottom: 53px
    }

    .m-navbar2 {
        display: block
    }
}

@media (max-width: 575.98px) {
    .ri-video-warp .video-buy-warp {
        -webkit-transform:translate(-50%,-50%) scale(.8);
        transform: translate(-50%,-50%) scale(.8)
    }

    .post-content {
        position: relative
    }

    .article-header .post-title {
        font-size: 1.25rem
    }

    .article-header .article-meta {
        font-size: .75rem
    }

    #comments .comment-form-author,#comments .comment-form-email,#comments .comment-form-url {
        width: 100%!important;
        margin: 0!important
    }

    .card {
        padding: 1rem
    }

    section {
        padding-top: 1rem;
        padding-bottom: 1rem
    }
}

@media print {
    .d-print-inline {
        display: inline!important
    }

    .d-print-inline-block {
        display: inline-block!important
    }

    .d-print-block {
        display: block!important
    }

    .d-print-grid {
        display: grid!important
    }

    .d-print-table {
        display: table!important
    }

    .d-print-table-row {
        display: table-row!important
    }

    .d-print-table-cell {
        display: table-cell!important
    }

    .d-print-flex {
        display: -webkit-box!important;
        display: -ms-flexbox!important;
        display: flex!important
    }

    .d-print-inline-flex {
        display: -webkit-inline-box!important;
        display: -ms-inline-flexbox!important;
        display: inline-flex!important
    }

    .d-print-none {
        display: none!important
    }
}

@-webkit-keyframes spinner-border {
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@keyframes spinner-border {
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@-webkit-keyframes spinner-grow {
    0% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }

    50% {
        opacity: 1;
        -webkit-transform: none;
        transform: none
    }
}

@keyframes spinner-grow {
    0% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }

    50% {
        opacity: 1;
        -webkit-transform: none;
        transform: none
    }
}

@-webkit-keyframes rotate {
    0% {
        -webkit-transform: rotate(0) scale(1);
        transform: rotate(0) scale(1)
    }

    50% {
        -webkit-transform: rotate(180deg) scale(.6);
        transform: rotate(180deg) scale(.6)
    }

    100% {
        -webkit-transform: rotate(360deg) scale(1);
        transform: rotate(360deg) scale(1)
    }
}

@keyframes rotate {
    0% {
        -webkit-transform: rotate(0) scale(1);
        transform: rotate(0) scale(1)
    }

    50% {
        -webkit-transform: rotate(180deg) scale(.6);
        transform: rotate(180deg) scale(.6)
    }

    100% {
        -webkit-transform: rotate(360deg) scale(1);
        transform: rotate(360deg) scale(1)
    }
}

@-webkit-keyframes fadeInAnimation {
    0% {
        opacity: 0
    }

    100% {
        opacity: 1
    }
}

@keyframes fadeInAnimation {
    0% {
        opacity: 0
    }

    100% {
        opacity: 1
    }
}

@-webkit-keyframes fadeOutAnimation {
    0% {
        opacity: 1
    }

    100% {
        opacity: 0
    }
}

@keyframes fadeOutAnimation {
    0% {
        opacity: 1
    }

    100% {
        opacity: 0
    }
}

@keyframes rotate {
    from {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@-webkit-keyframes fadeOut {
    0% {
        opacity: 1
    }

    100% {
        opacity: 0
    }
}

@keyframes fadeOut {
    0% {
        opacity: 1
    }

    100% {
        opacity: 0
    }
}
