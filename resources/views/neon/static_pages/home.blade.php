@extends('neon.layouts.default')

@section('content')
<!-- 首页 - 现代化设计 -->

<!-- 英雄区域 -->
<section class="hero-section relative min-h-screen flex items-center justify-center overflow-hidden" data-aos="fade-up">
    <!-- 动态背景 -->
    <div class="absolute inset-0 z-0">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 opacity-80"></div>
        <div id="particles-js" class="absolute inset-0"></div>

        <!-- 浮动元素 -->
        <div class="floating-elements">
            <div class="floating-element absolute top-20 left-10 w-20 h-20 bg-blue-500 rounded-full opacity-20 animate-bounce"></div>
            <div class="floating-element absolute top-40 right-20 w-16 h-16 bg-purple-500 rounded-full opacity-30 animate-pulse"></div>
            <div class="floating-element absolute bottom-20 left-20 w-24 h-24 bg-pink-500 rounded-full opacity-25 animate-bounce" style="animation-delay: 1s;"></div>
            <div class="floating-element absolute bottom-40 right-10 w-12 h-12 bg-green-500 rounded-full opacity-20 animate-pulse" style="animation-delay: 2s;"></div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- 左侧内容 -->
            <div class="text-center lg:text-left" data-aos="fade-right" data-aos-delay="200">
                <div class="mb-6">
                    <span class="inline-block px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-sm font-medium rounded-full mb-4">
                        🚀 专业数字商品交易平台
                    </span>
                </div>

                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                    <span class="gradient-text">{{ dujiaoka_config_get('title', '自动发卡平台') }}</span>
                    <br>
                    <span class="text-white">安全 · 快速 · 可靠</span>
                </h1>

                <p class="text-xl text-gray-300 mb-8 leading-relaxed">
                    {{ dujiaoka_config_get('description', '提供全球数字商品自动发卡服务，支持多种支付方式，7x24小时自动发货，让您的购物体验更加便捷安全。') }}
                </p>

                <!-- 特色数据 -->
                <div class="grid grid-cols-3 gap-6 mb-8">
                    <div class="text-center" data-aos="fade-up" data-aos-delay="400">
                        <div class="text-3xl font-bold gradient-text" data-count="50000">0</div>
                        <div class="text-sm text-gray-400">用户信赖</div>
                    </div>
                    <div class="text-center" data-aos="fade-up" data-aos-delay="500">
                        <div class="text-3xl font-bold gradient-text" data-count="99.9">0</div>
                        <div class="text-sm text-gray-400">成功率 %</div>
                    </div>
                    <div class="text-center" data-aos="fade-up" data-aos-delay="600">
                        <div class="text-3xl font-bold gradient-text" data-count="24">0</div>
                        <div class="text-sm text-gray-400">小时服务</div>
                    </div>
                </div>

                <!-- 行动按钮 -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <a href="#products" class="btn-primary inline-flex items-center justify-center px-8 py-4 text-lg font-semibold">
                        <i class="fas fa-shopping-bag mr-2"></i>
                        立即购买
                    </a>
                    <a href="{{ url('/article') }}" class="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white border-2 border-white border-opacity-30 rounded-xl hover:bg-white hover:bg-opacity-10 transition-all duration-300">
                        <i class="fas fa-question-circle mr-2"></i>
                        了解更多
                    </a>
                </div>

                <!-- 支付方式 -->
                <div class="mt-12" data-aos="fade-up" data-aos-delay="700">
                    <p class="text-sm text-gray-400 mb-4">支持多种支付方式</p>
                    <div class="flex flex-wrap gap-4 justify-center lg:justify-start">
                        <div class="payment-icon">
                            <i class="fab fa-alipay text-blue-500 text-2xl"></i>
                        </div>
                        <div class="payment-icon">
                            <i class="fab fa-weixin text-green-500 text-2xl"></i>
                        </div>
                        <div class="payment-icon">
                            <i class="fab fa-bitcoin text-yellow-500 text-2xl"></i>
                        </div>
                        <div class="payment-icon">
                            <i class="fab fa-paypal text-blue-600 text-2xl"></i>
                        </div>
                        <div class="payment-icon">
                            <i class="fas fa-credit-card text-purple-500 text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧图片/动画 -->
            <div class="relative" data-aos="fade-left" data-aos-delay="300">
                <div class="relative z-10">
                    <!-- 主要展示卡片 -->
                    <div class="card-modern p-8 text-center transform rotate-3 hover:rotate-0 transition-transform duration-500">
                        <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 glow-effect">
                            <i class="fas fa-bolt text-white text-3xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white mb-4">极速发货</h3>
                        <p class="text-gray-300 mb-6">自动化系统确保订单在2分钟内完成处理</p>
                        <div class="flex justify-center space-x-2">
                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse" style="animation-delay: 0.2s;"></div>
                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
                        </div>
                    </div>

                    <!-- 浮动卡片 -->
                    <div class="absolute -top-6 -left-6 card-modern p-4 w-48 transform -rotate-12 hover:rotate-0 transition-transform duration-500" data-aos="fade-up" data-aos-delay="800">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-shield-alt text-white"></i>
                            </div>
                            <div>
                                <div class="text-white font-semibold">安全保障</div>
                                <div class="text-sm text-gray-400">SSL加密</div>
                            </div>
                        </div>
                    </div>

                    <div class="absolute -bottom-6 -right-6 card-modern p-4 w-48 transform rotate-12 hover:rotate-0 transition-transform duration-500" data-aos="fade-up" data-aos-delay="900">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-headset text-white"></i>
                            </div>
                            <div>
                                <div class="text-white font-semibold">24/7客服</div>
                                <div class="text-sm text-gray-400">全天候支持</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 背景装饰 -->
                <div class="absolute inset-0 -z-10">
                    <div class="absolute top-10 left-10 w-32 h-32 bg-blue-500 rounded-full blur-3xl opacity-30"></div>
                    <div class="absolute bottom-10 right-10 w-40 h-40 bg-purple-500 rounded-full blur-3xl opacity-30"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 滚动指示器 -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <a href="#features" class="flex flex-col items-center">
            <span class="text-sm mb-2">向下滚动</span>
            <i class="fas fa-chevron-down"></i>
        </a>
    </div>
</section>

<!-- 特色功能区域 -->
<section id="features" class="py-20 relative" data-aos="fade-up">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 标题 -->
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold gradient-text mb-4">为什么选择我们</h2>
            <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                我们致力于为用户提供最优质的数字商品购买体验，以下是我们的核心优势
            </p>
        </div>

        <!-- 特色网格 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- 特色1 -->
            <div class="feature-card group" data-aos="fade-up" data-aos-delay="100">
                <div class="card-modern p-8 text-center h-full hover:scale-105 transition-transform duration-300">
                    <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-bolt text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">极速发货</h3>
                    <p class="text-gray-400 leading-relaxed">
                        采用全自动化系统，订单确认后2分钟内自动发货，无需等待人工处理，让您第一时间获得商品。
                    </p>
                    <div class="mt-6 flex justify-center">
                        <div class="flex space-x-1">
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style="animation-delay: 0.2s;"></div>
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 特色2 -->
            <div class="feature-card group" data-aos="fade-up" data-aos-delay="200">
                <div class="card-modern p-8 text-center h-full hover:scale-105 transition-transform duration-300">
                    <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-shield-alt text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">安全保障</h3>
                    <p class="text-gray-400 leading-relaxed">
                        采用银行级SSL加密技术，保护您的个人信息和交易数据安全，所有商品均来源正规渠道。
                    </p>
                    <div class="mt-6">
                        <div class="flex justify-center items-center space-x-2">
                            <i class="fas fa-lock text-green-500"></i>
                            <span class="text-sm text-green-500 font-medium">SSL加密保护</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 特色3 -->
            <div class="feature-card group" data-aos="fade-up" data-aos-delay="300">
                <div class="card-modern p-8 text-center h-full hover:scale-105 transition-transform duration-300">
                    <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-headset text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">24/7客服</h3>
                    <p class="text-gray-400 leading-relaxed">
                        专业客服团队全天候在线，通过QQ、微信、Telegram等多种方式为您提供及时的售前售后服务。
                    </p>
                    <div class="mt-6 flex justify-center space-x-3">
                        <i class="fab fa-qq text-blue-500 text-lg"></i>
                        <i class="fab fa-weixin text-green-500 text-lg"></i>
                        <i class="fab fa-telegram text-blue-400 text-lg"></i>
                    </div>
                </div>
            </div>

            <!-- 特色4 -->
            <div class="feature-card group" data-aos="fade-up" data-aos-delay="400">
                <div class="card-modern p-8 text-center h-full hover:scale-105 transition-transform duration-300">
                    <div class="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-credit-card text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">多种支付</h3>
                    <p class="text-gray-400 leading-relaxed">
                        支持支付宝、微信、银行卡、数字货币等多种支付方式，满足不同用户的支付需求。
                    </p>
                    <div class="mt-6 flex justify-center space-x-3">
                        <i class="fab fa-alipay text-blue-500 text-lg"></i>
                        <i class="fab fa-weixin text-green-500 text-lg"></i>
                        <i class="fab fa-bitcoin text-yellow-500 text-lg"></i>
                    </div>
                </div>
            </div>

            <!-- 特色5 -->
            <div class="feature-card group" data-aos="fade-up" data-aos-delay="500">
                <div class="card-modern p-8 text-center h-full hover:scale-105 transition-transform duration-300">
                    <div class="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-undo text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">售后保障</h3>
                    <p class="text-gray-400 leading-relaxed">
                        提供完善的售后服务，如遇商品问题支持退换，让您购买无忧，使用放心。
                    </p>
                    <div class="mt-6">
                        <div class="flex justify-center items-center space-x-2">
                            <i class="fas fa-check-circle text-green-500"></i>
                            <span class="text-sm text-green-500 font-medium">品质保证</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 特色6 -->
            <div class="feature-card group" data-aos="fade-up" data-aos-delay="600">
                <div class="card-modern p-8 text-center h-full hover:scale-105 transition-transform duration-300">
                    <div class="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-users text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">用户信赖</h3>
                    <p class="text-gray-400 leading-relaxed">
                        已服务超过50,000名用户，获得99.9%的好评率，是您值得信赖的数字商品购买平台。
                    </p>
                    <div class="mt-6">
                        <div class="flex justify-center items-center space-x-2">
                            <div class="flex text-yellow-500">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="text-sm text-gray-400">5.0分</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<main class="content-wrapper">
    <section class="container pt-4">
        <div class="row">
            <div class="w-100">
                <div class="position-relative">
                    <span class="position-absolute top-0 start-0 w-100 h-100 rounded-5 d-none-dark rtl-flip"
                        style="background: linear-gradient(90deg, #accbee 0%, #e7f0fd 100%)"></span>
                    <span class="position-absolute top-0 start-0 w-100 h-100 rounded-5 d-none d-block-dark rtl-flip"
                        style="background: linear-gradient(90deg, #1b273a 0%, #1f2632 100%)"></span>
                    <div class="row justify-content-center position-relative z-2">
                        <div class="col-xl-5 col-xxl-5  d-flex align-items-center mt-xl-n3">

                            <!-- Text content master slider -->
                            <div class="swiper px-5 pe-xl-0 ps-xxl-0 me-xl-n5" data-swiper='{
                    "spaceBetween": 64,
                    "loop": true,
                    "speed": 400,
                    "controlSlider": "#sliderImages",
                    "autoplay": {
                      "delay": 5500,
                      "disableOnInteraction": false
                    },
                    "scrollbar": {
                      "el": ".swiper-scrollbar"
                    }
                  }'>
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide text-center text-xl-start pt-5 py-xl-5">
                                        <p class="text-body">近期热销产品</p>
                                        <h2 class="display-4 pb-2 pb-xl-4">美区礼品卡50美刀</h2>
                                        <a class="btn btn-lg btn-dark" href="buy/1">
                                            立即 购买
                                            <i class="ci-arrow-up-right fs-lg ms-2 me-n1"></i>
                                        </a>
                                    </div>
                                    <div class="swiper-slide text-center text-xl-start pt-5 py-xl-5">
                                        <p class="text-body">Telegram社区数万人社区</p>
                                        <h2 class="display-4 pb-2 pb-xl-4">专业售后服务</h2>
                                        <a class="btn btn-lg btn-dark" href="hhttps://t.me/RinibaGroup">
                                            加入 社区
                                            <i class="ci-arrow-up-right fs-lg ms-2 me-n1"></i>
                                        </a>
                                    </div>
                                    <div class="swiper-slide text-center text-xl-start pt-5 py-xl-5">
                                        <p class="text-body">发货时间少于两分钟</p>
                                        <h2 class="display-4 pb-2 pb-xl-4">及时交货</h2>
                                        <a class="btn btn-lg btn-dark rounded-pill"
                                            href="https://youtu.be/me_Dc5PJrXk?si=QSILRHdbjUIJ7SmV" data-glightbox
                                            data-gallery="video">
                                            <i class="ci-play fs-lg ms-n1 me-2"></i>
                                            播放 视频
                                        </a>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="col-9 col-sm-7 col-md-6 col-lg-5 col-xl-5">
                            <!-- Binded images (controlled slider) -->
                            <div class="swiper user-select-none" id="sliderImages" data-swiper='{
                    "allowTouchMove": false,
                    "loop": true,
                    "effect": "fade",
                    "fadeEffect": {
                      "crossFade": true
                    }
                  }'>
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide d-flex justify-content-end">
                                        <div class="ratio rtl-flip"
                                            style="max-width: 400px; --cz-aspect-ratio: calc(537 / 495 * 100%)">
                                            <img src="/assets/riniba_03/banner/1.webp" alt="Image">
                                        </div>
                                    </div>
                                    <div class="swiper-slide d-flex justify-content-end">
                                        <div class="ratio rtl-flip"
                                            style="max-width: 400px; --cz-aspect-ratio: calc(537 / 495 * 100%)">
                                            <img src="/assets/riniba_03/banner/2.webp" alt="Image">
                                        </div>
                                    </div>
                                    <div class="swiper-slide d-flex justify-content-end">
                                        <div class="ratio rtl-flip"
                                            style="max-width: 400px; --cz-aspect-ratio: calc(537 / 495 * 100%)">
                                            <img src="/assets/riniba_03/banner/3.webp" alt="Image">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Scrollbar -->
                    <div class="row justify-content-center" data-bs-theme="dark">
                        <div class="col-xxl-10">
                            <div class="position-relative mx-5 mx-xxl-0">
                                <div class="swiper-scrollbar mb-4"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<!-- 分类与商品区 -->
<section class="container pt-3 mt-2 mt-sm-3 mt-lg-4 mt-xl-2">
    <div class="row g-0 overflow-x-auto pb-2 pb-sm-3 mb-3">
        <div class="col-auto pb-1 pb-sm-0 mx-auto">
            <ul class="nav nav-pills justify-content-center">
                <!-- 全部分类按钮 -->
                <li class="nav-item">
                    <a href="#group-all" data-bs-toggle="pill" class="btn btn-outline-secondary d-flex align-items-center active" style="min-width: 10px;">
                        <svg width="25" height="25" viewBox="0 0 24 24" fill="none" style="margin-right:8px;">
                            <path d="M6 6h15l-1.5 9h-13l-1-5H4" stroke="#000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <circle cx="9" cy="25" r="1" fill="#000"/>
                            <circle cx="18" cy="25" r="1" fill="#000"/>
                        </svg>
                        全部
                    </a>
                </li>
                @foreach($data as $group)
                <li class="nav-item">
                    <a href="#group-{{ $group['id'] }}" data-bs-toggle="pill" class="btn btn-outline-secondary d-flex align-items-center" style="min-width: 100px;">
                        @php
                            $imgSrc = category_picture_url($group['picture'] ?? '');
                        @endphp
                        <div class="breathing-img-container">
                            <div class="breathing-glow"></div>
                            <img src="{{ $imgSrc }}" class="breathing-img">
                        </div>
                        <span>{{ $group['gp_name'] }}</span>
                    </a>
                </li>
                @endforeach
            </ul>
        </div>
    </div>
</section>

<!-- 搜索框 -->
<div class="d-flex justify-content-center mb-4">
    <div class="position-relative col-12 col-md-6">
        <i class="ci-search position-absolute top-50 start-0 translate-middle-y ms-3"></i>
        <input type="search" class="quicksearch form-control form-icon-start" placeholder="搜索您的商品..." id="searchInput">
        <button class="btn btn-sm btn-outline-secondary w-auto border-0 p-1 position-absolute top-50 end-0 translate-middle-y me-2 clear-btn" id="clearBtn">
            <svg class="opacity-75" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M18.619 5.381a.875.875 0 0 1 0 1.238l-12 12a.875.875 0 0 1-1.238-1.238l12-12a.875.875 0 0 1 1.238 0Z"></path>
                <path d="M5.381 5.381a.875.875 0 0 1 1.238 0l12 12a.875.875 0 1 1-1.238 1.238l-12-12a.875.875 0 0 1 0-1.238Z"></path>
            </svg>
        </button>
    </div>
</div>

<!-- 置顶按钮，带呼吸灯 -->
<div class="floating-buttons position-fixed top-50 end-0 z-sticky me-3 me-xl-4 pb-4">
    <a class="btn-scroll-top btn btn-sm bg-body border-0 rounded-pill shadow animate-slide-end btn-breathing" href="#top">
        <span style="display: inline-block; transform: rotate(90deg);">顶</span>
        <span style="display: inline-block; transform: rotate(90deg);">置</span>
        <i class="ci-arrow-right fs-base ms-1 me-n1 animate-target"></i>
        <span class="position-absolute top-0 start-0 w-100 h-100 border rounded-pill z-0"></span>
        <svg class="position-absolute top-0 start-0 w-100 h-100 z-1" viewBox="0 0 62 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x=".75" y=".75" width="60.5" height="30.5" rx="15.25" stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10" style="stroke-dasharray: 155.201; stroke-dashoffset: 0;"></rect>
        </svg>
    </a>
</div>

<!-- 商品展示区 -->
<div class="container pt-4" id="goodsContainer">
    <div class="tab-content">
        <!-- 全部商品 -->
        <div class="tab-pane fade show active" id="group-all">
            <div class="row row-cols-2 row-cols-md-3 row-cols-lg-4 row-cols-xl-5 row-cols-xxl-6 g-4 pt-4 goods-list">
                @foreach($data as $group)
                    @foreach($group['goods'] as $goods)
                        @include('neon.layouts._goods', ['goods' => $goods, 'group' => $group])
                    @endforeach
                @endforeach
            </div>
        </div>
        <!-- 分类商品 -->
        @foreach($data as $group)
        <div class="tab-pane fade" id="group-{{ $group['id'] }}">
            <div class="row row-cols-2 row-cols-md-3 row-cols-lg-4 row-cols-xl-5 row-cols-xxl-6 g-4 pt-4 goods-list">
                @foreach($group['goods'] as $goods)
                    @include('neon.layouts._goods', ['goods' => $goods, 'group' => $group])
                @endforeach
            </div>
        </div>
        @endforeach
    </div>
</div>
@endsection

@section('scripts')
<script>

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 搜索功能
    var searchInput = document.getElementById('searchInput');
    var clearBtn = document.getElementById('clearBtn');
    var tabs = document.querySelectorAll('.tab-pane');
    searchInput.addEventListener('input', function(){
        var val = searchInput.value.toLowerCase().trim();
        tabs.forEach(function(tab) {
            tab.querySelectorAll('.goods-item').forEach(function(card) {
                var goodsName = card.getAttribute("data-name").toLowerCase();
                var groupName = card.getAttribute("data-group").toLowerCase();
                if(goodsName.indexOf(val) !== -1 || groupName.indexOf(val) !== -1){
                    card.style.display = "";
                }else{
                    card.style.display = "none";
                }
            });
        });
    });
    clearBtn.addEventListener('click',function(){
        searchInput.value = '';
        searchInput.dispatchEvent(new Event('input'));
    });
    clearBtn.addEventListener('click',function(){
        searchInput.value = '';
        searchInput.dispatchEvent(new Event('input'));
    });
    
    // 强制应用呼吸灯效果到所有商品图片
    // 这是确保效果生效的关键部分
    setTimeout(function() {
        document.querySelectorAll('.goods-item img').forEach(function(img) {
            img.style.animation = 'epic-breathing-glow 3s infinite';
            img.style.borderRadius = '8px';
        });
    }, 100);
});
    // 史诗级修复：强制应用呼吸灯效果到所有商品图片
    // 这是确保效果生效的终极方案
    setTimeout(function() {
        // 针对您提供的实际HTML结构的选择器
        document.querySelectorAll('.product-card .ratio img').forEach(function(img) {
            img.style.animation = 'epic-breathing-glow 3s infinite';
            img.style.borderRadius = '8px';
            // 确保父容器设置为overflow:visible
            var ratioDiv = img.closest('.ratio');
            if(ratioDiv) {
                ratioDiv.style.overflow = 'visible';
            }
            // 确保祖父容器也设置为overflow:visible
            var positionDiv = ratioDiv ? ratioDiv.parentElement : null;
            if(positionDiv) {
                positionDiv.style.overflow = 'visible';
            }
        });
    }, 100);
});
</script>
@endsection
</script>
@endsection