@extends('viego.layouts.default')
@section('content')
 <div class="nav2-header">
<nav2>
  <ul>
    <li class="active">
      <button class="toggleButton" data-target="slider-container">站点推荐</button>
    </li>
    <li>
      <button class="toggleButton" data-target="noticeContainer">公告提示</button>
    </li>
    <li>
      <button class="toggleButton" data-target="artists">联系客服</button>
    </li>
  </ul>
</div>





 <section class="daohang">
   <div class="left-content">
          <div id="slider-container" class="slider-container">
           <div class="swiper">
              <div class="swiper-wrapper">
                <div class="swiper-slide">
                  <img
                    src="https://github.com/ecemgo/mini-samples-great-tricks/assets/13468728/95b52c32-f5da-4fe6-956d-a5ed118bbdd2" />
                  <div class="slide-overlay">
                    <h2>Facebook 专区</h2>
                    <button>
                      查看详情<i class="fa-solid fa-circle-play"></i>
                    </button>
                  </div>
                </div>
                <div class="swiper-slide">
                  <img
                    src="https://github.com/ecemgo/mini-samples-great-tricks/assets/13468728/6ddf81f5-2689-4f34-bf80-a1e07f14621c" />
                  <div class="slide-overlay">
                    <h2>新手 教程</h2>
                    <button>
                      点击观看 <i class="fa-solid fa-circle-play"></i>
                    </button>
                  </div>
                </div>
                <div class="swiper-slide">
                  <img
                    src="https://github.com/ecemgo/mini-samples-great-tricks/assets/13468728/ab52d9d0-308e-43e0-a577-dce35fedd2a3" />
                  <div class="slide-overlay">
                    <h2>Apple 兑换码</h2>
                    <button>
                      立即购买 <i class="fa-solid fa-circle-play"></i>
                    </button>
                  </div>
                </div>
                <div class="swiper-slide">
                  <img
                    src="https://github.com/ecemgo/mini-samples-great-tricks/assets/13468728/20c8fdd5-9f4a-4917-ae90-0239a52e8334" />
                  <div class="slide-overlay">
                    <h2>Telegram 售后群组</h2>
                    <button>
                      加入我们<i class="fa-solid fa-circle-play"></i>
                    </button>
                  </div>
                </div>
                <div class="swiper-slide">
                  <img
                    src="https://github.com/ecemgo/mini-samples-great-tricks/assets/13468728/df461a99-2fb3-4d55-ac16-2e0c6dd783e1" />
                  <div class="slide-overlay">
                    <h2>Telegram 频道</h2>
                    <button>
                      订阅我们 <i class="fa-solid fa-circle-play"></i>
                    </button>
                  </div>
                </div>
              </div>
              <div class="swiper-pagination"></div>
            </div>
          </div>

          <div id="artists" class="artists">
           
            <div class="artist-container containers">
            <a href="https://t.me/viegoooo" target="_blank" class="artist">
                <img src="/assets/viego/logo.jpg">
                <p>Whatsapp客服</p>
              </a>
                 <a href="https://t.me/viegoooo" target="_blank" class="artist">
                <img src="/assets/viego/logo.jpg">
                <p>Telegram客服</p>
              </a>
                 <a href="https://t.me/viegoooo" target="_blank" class="artist">
                <img src="/assets/viego/logo.jpg">
                <p>Telegram频道</p>
              </a>
              </div>
          </div>
         </section>
 
       


<div id="noticeContainer" class="card-body2">
    <div class="notice">{!! dujiaoka_config_get('notice') !!}</div>
</div>





<div class="nav nav-list">

    <a href="#group-all" class="btn-11" active" data-bs-toggle="tab" aria-expanded="false" role="tab" data-toggle="tab">
        <span class="tab-title">
       <i class="fas fa-stroopwafel fa-xl"></i></i>&nbsp;
全部商品
        </span>
    </a>
    @foreach($data as  $index => $group)
    <a href="#group-{{ $group['id'] }}" class="btn-11" data-bs-toggle="tab" aria-expanded="false" role="tab" data-toggle="tab">
        <img src="{{ category_picture_url($group['picture'] ?? '') }}" alt="{{ $group['gp_name'] }}" style="width: 24px; height: 24px; border-radius: 4px; margin-right: 8px; vertical-align: middle;">
        <span class="tab-title">
           {!! $group['gp_name'] !!}
        </span>
    </a>
    @endforeach
</div>





 <div class="app-content-actions">
      <input type="text" class="search-bar" id="search" placeholder="{{ __('hyper.home_search_box') }}" >
      <div class="app-content-actions-wrapper">
        <button class="action-button list active" title="列表视图">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-list"><line x1="8" y1="6" x2="21" y2="6"/><line x1="8" y1="12" x2="21" y2="12"/><line x1="8" y1="18" x2="21" y2="18"/><line x1="3" y1="6" x2="3.01" y2="6"/><line x1="3" y1="12" x2="3.01" y2="12"/><line x1="3" y1="18" x2="3.01" y2="18"/></svg>
        </button>
        <button class="action-button grid" title="网格视图">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-grid"><rect x="3" y="3" width="7" height="7"/><rect x="14" y="3" width="7" height="7"/><rect x="14" y="14" width="7" height="7"/><rect x="3" y="14" width="7" height="7"/></svg>
        </button>
      </div>
    </div>


<div class="tab-content">
    <div class="tab-pane active" id="group-all">
    <div class="products-area-wrapper tableView">
                    <div class="products-header">
                       <div  class="product-cell image">
                        {{ __('hyper.home_product_name') }}
                     
                        </div>

                        <div class="product-cell status-cell">
                          发货方式
                        </div>
                        <div class="product-cell stock">
                          {{ __('hyper.home_in_stock') }}
                          
                        </div>
                        <div class="product-cell price">
                         {{ __('hyper.home_price') }}
                          
                        </div>
                    </div>
                    
                   @foreach($data as $group)
                @foreach($group['goods'] as $goods)
                 @if($goods['in_stock'] > 0)
                 <a href="{{ url("/buy/{$goods['id']}") }}" class="products-row">
                         @else
                    <a href="javascript:void(0);" onclick="sell_out_tip()" class="products-row">
                    @endif
                 <dvi a href="{{ url("/buy/{$goods['id']}") }}" class="product-cell image"">
                            <img src="{{ picture_ulr($goods['picture']) }}" alt="product">
                            <span>{{ $goods['gd_name'] }}</span></dvi>
                       

                    <div class="product-cell status-cell">
                            <span class="cell-label">发货方式:</span>
                            @if($goods['type'] == \App\Models\Goods::AUTOMATIC_DELIVERY)
                            <span class="status active">{{ __('hyper.home_automatic_delivery') }}</span>
                            @else
                            <span class="status disabled">{{ __('hyper.home_charge') }}</span>
                            @endif</span>
                        </div>
                   <div class="product-cell stock">
                            <span class="cell-label">库存:</span>
                           {{ $goods['in_stock'] }}
                        </div>
                      <div class="product-cell price">
                            <span class="cell-label">价格:</span>
                            {{(dujiaoka_config_get('global_currency')) }} {{ $goods['actual_price'] }}
                        </div>
                    </a>
 @endforeach
             @endforeach
              </div>
    </div>
             
             @foreach($data as $index => $group)
       <div class="tab-pane"  id="group-{{ $group['id'] }}">
           <div class="products-area-wrapper tableView">
                            <div class="products-header">
                       <div  class="product-cell image">
                        {{ __('hyper.home_product_name') }}
                     
                        </div>

                        <div class="product-cell status-cell">
                            发货方式
                           
                        </div>
                        <div class="product-cell stock">
                          {{ __('hyper.home_in_stock') }}
                          
                        </div>
                        <div class="product-cell price">
                         {{ __('hyper.home_price') }}
                          
                        </div>
                    </div>
         
                @foreach($group['goods'] as $goods)
                    @if($goods['in_stock'] > 0)
                 <a href="{{ url("/buy/{$goods['id']}") }}" class="products-row">
                         @else
                    <a href="javascript:void(0);" onclick="sell_out_tip()" class="products-row">
                    @endif
                        
                      <dvi a href="{{ url("/buy/{$goods['id']}") }}" class="product-cell image"">
                            <img src="{{ picture_ulr($goods['picture']) }}" alt="product">
                            <span>{{ $goods['gd_name'] }}</span></dvi>
                       

                    <div class="product-cell status-cell">
                            <span class="cell-label">发货方式:</span>
                            @if($goods['type'] == \App\Models\Goods::AUTOMATIC_DELIVERY)
                            <span class="status active">{{ __('hyper.home_automatic_delivery') }}</span>
                            @else
                            <span class="status disabled">{{ __('hyper.home_charge') }}</span>
                            @endif</span>
                        </div>
                   <div class="product-cell stock">
                            <span class="cell-label">库存:</span>
                           {{ $goods['in_stock'] }}
                        </div>
                      <div class="product-cell price">
                            <span class="cell-label">价格:</span>
                            {{(dujiaoka_config_get('global_currency')) }} {{ $goods['actual_price'] }}
                        </div>
                    
                    </a>
 @endforeach
  </div>
        </div>
             @endforeach
      
</div>


 <div class="contacts">
          <h1>售后文章-使用教程</h1>
            @foreach ($articles->shuffle()->take(6) as $article)
          <div class="contacts-container">
        
            <div class="contact-status">
              <a href="article/{{ !empty($article['link']) ? $article['link'] : $article['id'] }}" class="contact-activity">
                <img
                  src="{{ picture_ulr($article['picture']) }}"
                  alt="" />
                <p>{{ $article['title'] }}</span></p>
              </a>
              <small>发布时间：{{ $article['updated_at'] }}</small>
            </div>  @endforeach
            


<div class="modal fade" id="notice-modal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="myCenterModalLabel">{{ __('hyper.notice_announcement') }}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                {!! dujiaoka_config_get('notice') !!}
            </div>
        </div>
    </div>
</div>
@stop 
@section('js')

<script>
    $('#notice-open').click(function() {
        $('#notice-modal').modal();
    });
$("#search").on("input", function (e) {
    var txt = $("#search").val();
    if ($.trim(txt) !== "") {
        // 使用正则表达式进行不区分大小写的搜索
        var regex = new RegExp(txt, "i");
        $(".products-row").hide().filter(function () {
            return regex.test($(this).text());
        }).show();
    } else {
        $(".products-row").show();
    }
});
    function sell_out_tip() {
        $.NotificationApp.send("{{ __('hyper.home_tip') }}","{{ __('hyper.home_sell_out_tip') }}","top-center","rgba(0,0,0,0.2)","info");
    }
</script>

<script>
    // 获取所有的按钮
    const buttons = document.querySelectorAll('.toggleButton');

    // 给每个按钮添加点击事件监听器
    buttons.forEach(button => {
        button.addEventListener('click', () => {
            // 获取按钮对应的目标元素的id
            const targetId = button.getAttribute('data-target');
            const targetElement = document.getElementById(targetId);

            // 隐藏所有的元素
            document.querySelectorAll('.slider-container, .artists,.card-body2').forEach(element => {
                element.style.display = 'none';
            });

            // 显示目标元素
            targetElement.style.display = 'block';
        });
    });
</script>


@stop