<?php

/**
 * 批量升级所有PHP文件到Laravel 10 + PHP 8.2标准
 */

// 需要升级的文件列表
$files = [
    // Admin Controllers
    'app/Admin/Controllers/ArticleCategoryController.php',
    'app/Admin/Controllers/ArticleController.php',
    'app/Admin/Controllers/AuthController.php',
    'app/Admin/Controllers/CarmisController.php',
    'app/Admin/Controllers/CouponController.php',
    'app/Admin/Controllers/EmailTestController.php',
    'app/Admin/Controllers/EmailtplController.php',
    'app/Admin/Controllers/GoodsController.php',
    'app/Admin/Controllers/GoodsGroupController.php',
    'app/Admin/Controllers/InviteUserController.php',
    'app/Admin/Controllers/OrderController.php',
    'app/Admin/Controllers/PayController.php',
    'app/Admin/Controllers/SystemSettingController.php',
    'app/Admin/Controllers/TempController.php',
    'app/Admin/Controllers/WithdrawController.php',
    
    // Admin Forms
    'app/Admin/Forms/EmailTest.php',
    'app/Admin/Forms/ImportCarmis.php',
    'app/Admin/Forms/SystemSetting.php',
    
    // Admin Repositories
    'app/Admin/Repositories/ArticleCategory.php',
    'app/Admin/Repositories/Articles.php',
    'app/Admin/Repositories/Carmis.php',
    'app/Admin/Repositories/Coupon.php',
    'app/Admin/Repositories/Emailtpl.php',
    'app/Admin/Repositories/Goods.php',
    'app/Admin/Repositories/GoodsGroup.php',
    'app/Admin/Repositories/InviteUser.php',
    'app/Admin/Repositories/Order.php',
    'app/Admin/Repositories/Pay.php',
    'app/Admin/Repositories/User.php',
    'app/Admin/Repositories/Withdraw.php',
    
    // Admin Charts
    'app/Admin/Charts/DashBoard.php',
    'app/Admin/Charts/PayoutRateCard.php',
    'app/Admin/Charts/SalesCard.php',
    'app/Admin/Charts/SuccessOrderCard.php',
    
    // Admin Actions
    'app/Admin/Actions/Post/BatchRestore.php',
    'app/Admin/Actions/Post/Restore.php',
    
    // Listeners
    'app/Listeners/GoodsDeleted.php',
    'app/Listeners/GoodsGroupDeleted.php',
    'app/Listeners/OrderUpdated.php',
    
    // Events (剩余的)
    'app/Events/ArticleCategoryDeleted.php',
    'app/Events/ArticlesDeleted.php',
    'app/Events/GoodsDeleted.php',
    'app/Events/GoodsGroupDeleted.php',
    
    // Jobs
    'app/Jobs/ApiHook.php',
    'app/Jobs/BarkPush.php',
    'app/Jobs/CouponBack.php',
    'app/Jobs/CreateCarmiPush.php',
    'app/Jobs/CreateGoodsPush.php',
    'app/Jobs/GoodsPriceReducePush.php',
    'app/Jobs/OrderExpired.php',
    'app/Jobs/ServerJiang.php',
    'app/Jobs/TelegramPush.php',
    'app/Jobs/WorkWeiXinPush.php',
    
    // Pay Controllers
    'app/Http/Controllers/Pay/AlipayController.php',
    'app/Http/Controllers/Pay/AlphaController.php',
    'app/Http/Controllers/Pay/BinancePayController.php',
    'app/Http/Controllers/Pay/CoinbaseController.php',
    'app/Http/Controllers/Pay/EpusdtController.php',
    'app/Http/Controllers/Pay/MapayController.php',
    'app/Http/Controllers/Pay/PayjsController.php',
    'app/Http/Controllers/Pay/PaypalPayController.php',
    'app/Http/Controllers/Pay/PaysapiController.php',
    'app/Http/Controllers/Pay/StripeController.php',
    'app/Http/Controllers/Pay/TokenPayController.php',
    'app/Http/Controllers/Pay/VpayController.php',
    'app/Http/Controllers/Pay/WepayController.php',
    'app/Http/Controllers/Pay/YipayController.php',
    'app/Http/Controllers/PayController.php',
    
    // Home Controllers
    'app/Http/Controllers/Home/ArticleController.php',
    'app/Http/Controllers/Home/OrderController.php',
    
    // Console
    'app/Console/Kernel.php',
    
    // Exceptions
    'app/Exceptions/AppException.php',
    'app/Exceptions/RuleValidationException.php',
    
    // Services
    'app/Service/CarmisService.php',
    'app/Service/CouponService.php',
    'app/Service/EmailtplService.php',
    'app/Service/OrderProcessService.php',
    'app/Service/OrderService.php',
    'app/Service/PayService.php',
    'app/Service/Util.php',
    
    // Middleware (剩余的)
    'app/Http/Middleware/Challenge.php',
    'app/Http/Middleware/DujiaoSystem.php',
    'app/Http/Middleware/EncryptCookies.php',
    'app/Http/Middleware/InstallCheck.php',
    'app/Http/Middleware/PayGateWay.php',
    'app/Http/Middleware/TrimStrings.php',
    
    // 自定义包
    'packages/dujiaoka/currency/src/Currency.php',
    'packages/dujiaoka/currency/src/CurrencyServiceProvider.php',
    'packages/dujiaoka/currency/src/Facades/Currency.php',
    'packages/dujiaoka/geetest/src/Facades/Geetest.php',
    'packages/dujiaoka/geetest/src/Geetest.php',
    'packages/dujiaoka/geetest/src/GeetestServiceProvider.php',
    
    // 测试文件
    'tests/CreatesApplication.php',
    'tests/Feature/ExampleTest.php',
    'tests/TestCase.php',
    'tests/Unit/ExampleTest.php',
    
    // 其他核心文件
    'app/Admin/bootstrap.php',
    'app/Admin/routes.php',
    'app/Helpers/functions.php',
    'bootstrap/app.php',
    'public/index.php',
    'server.php',
    'routes/api.php',
    'routes/channels.php',
    'routes/console.php',
    'dcat_admin_ide_helper.php',
];

echo "找到 " . count($files) . " 个PHP文件需要升级\n";

// 升级模式
$upgrades = [
    // 移除 @return void
    '/@return void\s*\*\/\s*public function/m' => '*/\n    public function',
    '/@return void\s*\*\/\s*protected function/m' => '*/\n    protected function',
    '/@return void\s*\*\/\s*private function/m' => '*/\n    private function',
    
    // 添加返回类型
    '/public function (\w+)\(\)(\s*{)/' => 'public function $1(): void$2',
    '/protected function (\w+)\(\)(\s*{)/' => 'protected function $1(): void$2',
    '/private function (\w+)\(\)(\s*{)/' => 'private function $1(): void$2',
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "升级文件: $file\n";
        $content = file_get_contents($file);
        
        foreach ($upgrades as $pattern => $replacement) {
            $content = preg_replace($pattern, $replacement, $content);
        }
        
        file_put_contents($file, $content);
    } else {
        echo "文件不存在: $file\n";
    }
}

echo "所有文件升级完成！\n";
