document.addEventListener('DOMContentLoaded', function() {
    // 获取所有主题选项
    const themeItems = document.querySelectorAll('[data-bs-theme-value]');
    
    // 初始化主题切换器
    themeItems.forEach(item => {
        item.addEventListener('click', function() {
            const themeValue = this.dataset.bsThemeValue;
            document.documentElement.setAttribute('data-bs-theme', themeValue);
            
            // 更新活动指示符
            themeItems.forEach(i => i.classList.remove('active'));
            this.classList.add('active');
        });
    });
});


    // 获取保存的主题
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
        document.documentElement.setAttribute('data-bs-theme', savedTheme);
        // 更新活动指示符
        const themeItems = document.querySelectorAll('[data-bs-theme-value]');
        themeItems.forEach(item => {
            if (item.dataset.bsThemeValue === savedTheme) {
                item.classList.add('active');
            }
        });
    }
    
    
document.addEventListener('DOMContentLoaded', function() {
    // 获取保存的主题
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
        document.documentElement.setAttribute('data-bs-theme', savedTheme);
        // 更新活动指示符
        const themeItems = document.querySelectorAll('[data-bs-theme-value]');
        themeItems.forEach(item => {
            if (item.dataset.bsThemeValue === savedTheme) {
                item.classList.add('active');
            }
        });
    }




    // 监听主题切换事件
    const themeItems = document.querySelectorAll('[data-bs-theme-value]');
    themeItems.forEach(item => {
        item.addEventListener('click', function() {
            const themeValue = this.dataset.bsThemeValue;
            document.documentElement.setAttribute('data-bs-theme', themeValue);
            localStorage.setItem('theme', themeValue);
            
            // 更新活动指示符
            themeItems.forEach(i => i.classList.remove('active'));
            this.classList.add('active');
        });
    });
});
