@import url(https://fonts.googleapis.com/css?family=Open+Sans:400|Raleway:300);
html *,
html *:before,
html *:after {
  box-sizing: border-box;
  transition: 0.5s ease-in-out;
}
html i, html em,
html b, html strong,
html span {
  transition: none;
}

*:before,
*:after {
  z-index: -1;
}

h1,
h4 {
  font-family: "Raleway", "Open Sans", sans-serif;
  margin: 0;
  line-height: 1;
}

a {
  text-decoration: none;
  color: black;
}

.centerer {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 600px) {
  .wrap {
    width: 50%;
    float: left;
  }
}
[class^=btn-] {
  display: grid;
  place-items: center;
  overflow: hidden;
  width: 100%;
  height: 40px;
  max-width: 120px;
  margin: 0.1rem auto;
  border: 1px solid currentColor;
}

.btn-0 {
  color: #366796;
}
.btn-0:before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: #0b2e4f;
  transform: translateX(-100%);
}
.btn-0:hover {
  color: #c4d2e0;
}
.btn-0:hover:before {
  transform: translateX(0);
}

.btn-1 {
  color: #9ace59;
}
.btn-1:before {
  content: "";
  position: absolute;
  top: 0;
  right: -50px;
  bottom: 0;
  left: 0;
  border-right: 50px solid transparent;
  border-bottom: 80px solid #527624;
  transform: translateX(-100%);
}
.btn-1:hover {
  color: #e1f1ce;
}
.btn-1:hover:before {
  transform: translateX(0);
}

.btn-1-2 {
  color: #4661ad;
}
.btn-1-2:before, .btn-1-2:after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-bottom: 80px solid #16295f;
}
.btn-1-2:before {
  right: -50px;
  border-right: 50px solid transparent;
  transform: translateX(-100%);
}
.btn-1-2:after {
  left: -50px;
  border-left: 50px solid transparent;
  transform: translateX(100%);
}
.btn-1-2:hover {
  color: #c9d1e7;
}
.btn-1-2:hover:before {
  transform: translateX(-40%);
}
.btn-1-2:hover:after {
  transform: translateX(40%);
}

.btn-2 {
  color: #b7a735;
}
.btn-2:before, .btn-2:after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.btn-2:before {
  right: -50px;
  border-right: 50px solid transparent;
  border-bottom: 80px solid #665b0a;
  transform: translateX(-100%);
}
.btn-2:after {
  left: -50px;
  border-left: 50px solid transparent;
  border-top: 80px solid #665b0a;
  transform: translateX(100%);
}
.btn-2:hover {
  color: #eae5c4;
}
.btn-2:hover:before {
  transform: translateX(-49%);
}
.btn-2:hover:after {
  transform: translateX(49%);
}

.btn-3:before, .btn-3:after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-top: 40px solid #0d6158;
  border-bottom: 40px solid #0d6158;
}
.btn-3:before {
  border-right: 40px solid transparent;
  transform: translateX(-100%);
}
.btn-3:after {
  border-left: 40px solid transparent;
  transform: translateX(100%);
}
.btn-3:hover {
  color: #c5e8e4;
}
.btn-3:hover:before {
  transform: translateX(-30%);
}
.btn-3:hover:after {
  transform: translateX(30%);
}

.btn-4 {
  color: #ce446e;
}
.btn-4:before, .btn-4:after,
.btn-4 span:before,
.btn-4 span:after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: #761532;
}
.btn-4:before {
  transform: translate(-100%, -100%);
}
.btn-4:after {
  transform: translate(-100%, 100%);
}
.btn-4 span:before {
  transform: translate(100%, -100%);
}
.btn-4 span:after {
  transform: translate(100%, 100%);
}
.btn-4:hover {
  color: #f1c8d4;
}
.btn-4:hover:before {
  transform: translate(-50%, -50%);
}
.btn-4:hover:after {
  transform: translate(-50%, 50%);
}
.btn-4:hover span:before {
  transform: translate(50%, -50%);
}
.btn-4:hover span:after {
  transform: translate(50%, 50%);
}

.btn-5 {
  color: #6366ba;
}
.btn-5:after {
  content: "";
  width: 0;
  height: 0;
  -webkit-transform: rotate(360deg);
  border-style: solid;
  border-width: 0 0 0 0;
  border-color: transparent #2b2d68 transparent transparent;
  position: absolute;
  top: 0;
  right: 0;
}
.btn-5:before {
  content: "";
  width: 0;
  height: 0;
  -webkit-transform: rotate(360deg);
  border-style: solid;
  border-width: 0 0 0 0;
  border-color: transparent transparent transparent #2b2d68;
  position: absolute;
  bottom: 0;
  left: 0;
}
.btn-5:before, .btn-5:after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border: 0 solid;
  transform: rotate(360deg);
}
.btn-5:before {
  bottom: 0;
  left: 0;
  border-color: transparent transparent transparent #2b2d68;
}
.btn-5:after {
  top: 0;
  right: 0;
  border-color: transparent #2b2d68 transparent transparent;
}
.btn-5:hover {
  color: #d1d2eb;
}
.btn-5:hover:before, .btn-5:hover:after {
  border-width: 80px 262.5px;
}

.btn-6 {
  color: #55509a;
}
.btn-6 span {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-radius: 50%;
  background-color: #211d52;
  transition: width 0.4s ease-in-out, height 0.4s ease-in-out;
  transform: translate(-50%, -50%);
  z-index: -1;
}
.btn-6:hover {
  color: #cdcce1;
}
.btn-6:hover span {
  width: 225%;
  height: 562.5px;
}
.btn-6:active {
  background-color: #373188;
}

.btn-7 {
  color: #8d4f2b;
}
.btn-7:before, .btn-7:after,
.btn-7 span:before,
.btn-7 span:after {
  content: "";
  position: absolute;
  top: 0;
  width: 25.25%;
  height: 0;
  background-color: #491d03;
}
.btn-7:before {
  left: 0;
}
.btn-7:after {
  left: 50%;
}
.btn-7 span:before, .btn-7 span:after {
  top: auto;
  bottom: 0;
}
.btn-7 span:before {
  left: 25%;
}
.btn-7 span:after {
  left: 75%;
}
.btn-7:hover {
  color: #decbc1;
}
.btn-7:hover:before, .btn-7:hover:after,
.btn-7:hover span:before,
.btn-7:hover span:after {
  height: 80px;
}

.btn-8 {
  color: #a333bb;
}
.btn-8:before, .btn-8:after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: #580969;
}
.btn-8:before {
  transform: translateY(-100%);
}
.btn-8:after {
  transform: translateY(100%);
}
.btn-8:hover {
  color: #e4c3eb;
}
.btn-8:hover:before {
  transform: translateY(-50%);
}
.btn-8:hover:after {
  transform: translateY(50%);
}

.btn-9 {
  color: #48b6a0;
}
.btn-9:before, .btn-9:after,
.btn-9 span:before,
.btn-9 span:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
  background-color: rgba(24, 101, 86, 0.25);
  transition: 0.4s ease-in-out;
}
.btn-9:after,
.btn-9 span:before {
  top: auto;
  bottom: 0;
}
.btn-9 span:before,
.btn-9 span:after {
  transition-delay: 0.4s;
}
.btn-9:hover {
  color: #c9eae3;
}
.btn-9:hover:before, .btn-9:hover:after,
.btn-9:hover span:before,
.btn-9:hover span:after {
  height: 80px;
}
.btn-9:active {
  background-color: #28a98f;
}

.btn-10 {
  color: #b9b85e;
}
.btn-10:before, .btn-10:after,
.btn-10 span:before,
.btn-10 span:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 80px;
  background-color: rgba(104, 103, 39, 0.25);
  transition: 0.4s;
}
.btn-10:after,
.btn-10 span:before {
  left: auto;
  right: 0;
}
.btn-10 span:before,
.btn-10 span:after {
  transition-delay: 0.4s;
}
.btn-10:hover {
  color: #ebead0;
}
.btn-10:hover:before, .btn-10:hover:after,
.btn-10:hover span:before,
.btn-10:hover span:after {
  width: 250px;
}
.btn-10:active {
  background-color: #adac41;
}

@-webkit-keyframes criss-cross-left {
  0% {
    left: -20px;
  }
  50% {
    left: 50%;
    width: 20px;
    height: 20px;
  }
  100% {
    left: 50%;
    width: 375px;
    height: 375px;
  }
}

@keyframes criss-cross-left {
  0% {
    left: -20px;
  }
  50% {
    left: 50%;
    width: 20px;
    height: 20px;
  }
  100% {
    left: 50%;
    width: 375px;
    height: 375px;
  }
}
@-webkit-keyframes criss-cross-right {
  0% {
    right: -20px;
  }
  50% {
    right: 50%;
    width: 20px;
    height: 20px;
  }
  100% {
    right: 50%;
    width: 375px;
    height: 375px;
  }
}
@keyframes criss-cross-right {
  0% {
    right: -20px;
  }
  50% {
    right: 50%;
    width: 20px;
    height: 20px;
  }
  100% {
    right: 50%;
    width: 375px;
    height: 375px;
  }
}
.btn-11 {
  position: relative;
  color: #000000;
}
.btn-11.active {
   color: #ffffff;
   background-color: #000000;
    border: 1px solid #FF0000;
}
.btn-11:before, .btn-11:after {
  position: absolute;
  top: 50%;
  content: "";
  width: 20px;
  height: 20px;
  background-color: #000000;
  border-radius: 50%;
}
.btn-11:before {
  left: -20px;
  transform: translate(-50%, -50%);
}
.btn-11:after {
  right: -20px;
  transform: translate(50%, -50%);
}
.btn-11:hover {
  color: #ffffff;
}
.btn-11:hover:before {
  -webkit-animation: criss-cross-left 0.8s both;
          animation: criss-cross-left 0.8s both;
  -webkit-animation-direction: alternate;
          animation-direction: alternate;
}
.btn-11:hover:after {
  -webkit-animation: criss-cross-right 0.8s both;
          animation: criss-cross-right 0.8s both;
  -webkit-animation-direction: alternate;
          animation-direction: alternate;
}