<?php

require_once 'vendor/autoload.php';

// 启动Laravel应用
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// 创建一个假的请求
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);

// 测试GoodsService
try {
    $goodsService = new App\Service\GoodsService();
    $data = $goodsService->withGroup();
    
    echo "商品数据获取成功！\n";
    echo "分组数量: " . count($data) . "\n";
    
    if (!empty($data)) {
        foreach ($data as $group) {
            echo "分组: " . $group['gp_name'] . "\n";
            echo "商品数量: " . count($group['goods']) . "\n";
            
            if (!empty($group['goods'])) {
                foreach ($group['goods'] as $goods) {
                    echo "  商品: " . $goods['gd_name'] . "\n";
                    echo "  实际价格: " . $goods['actual_price'] . "\n";
                    echo "  零售价格: " . (isset($goods['retail_price']) ? $goods['retail_price'] : '未设置') . "\n";
                    echo "  库存: " . $goods['in_stock'] . "\n";
                    echo "  ---\n";
                }
            }
        }
    } else {
        echo "没有找到商品数据\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
