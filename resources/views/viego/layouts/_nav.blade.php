 <style type="text/css">
 @media (min-width: 765px) {
            .search-order-mobile {
                display: none;
            }
        }
</style>
<body class="app-container" style="display: none;">
  <div class="app-content">
    <div class="app-content-header">
       <a href="/"><svg width="0" height="0">
  <filter id="f" x="-50%" y="-200%" width="200%" height="500%">
    <feGaussianBlur stdDeviation="35"></feGaussianBlur>
    <feColorMatrix type="saturate" values="1.3"></feColorMatrix>
    <feComposite in="SourceGraphic"></feComposite>
  </filter>
</svg>
<h1>{{ dujiaoka_config_get('text_logo') }}</h1></a> 
     
      <button onclick="toggleTheme()" class="mode-switch" title="切换主题">
        <svg class="moon" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="24" height="24" viewBox="0 0 24 24">
          <defs></defs>
          <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
        </svg>
      </button>


      @if(Auth::check())
         <a  href="{{ url('order-search') }}"><button class="app-content-headerButton search-order">查单</button></a>
        
            @else
        <a  href="{{ url('order-search') }}"><button class="app-content-headerButton">查单</button></a>
            @endif
    
      @if(Auth::check())
      <button class="app-content-headerButton" type="button" id="dropdownMenuButton"
                    data-toggle="dropdown" aria-expanded="false">
                余额￥{{ Auth::user()->money }}
            </button>
            <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                <a class="dropdown-item" href="{{ url('user') }}">个人中心</a>
             <a class="dropdown-item search-order-mobile" href="{{ url('order-search') }}">查询订单</a>
                <a class="dropdown-item" href="{{ url('logout') }}">退出登录</a>
            </div>
            @else
           <a  href="{{ url('login') }}"><button class="app-content-headerButton">登录</button></a>
            @endif
           
    </div>
     </div> 
     </div>
     



