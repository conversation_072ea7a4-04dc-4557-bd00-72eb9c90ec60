@import url("https://fonts.googleapis.com/css2?family=Comfortaa:wght@400;500&display=swap");
* {
  box-sizing: border-box;
}

:root {
  font-family: "Comfortaa", cursive;
}

body {
  --bg-color: #f3f4f4;
  --link-color: #7c7f8e;
  --link-hover-color: #242a37;
  --link-active-1: #ff4350;
  --wrapper-bg: #fff;
  --box-shadow: 0px 0px 10px 6px rgba(235,235,235,.6);
  margin: 0;
  width: 100%;
  height: 100vh;
  background-color: #f3f4f4;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-color);
  transition: background 0.3s ease-in;
}

body.dark {
  --bg-color: #333a48;
  --link-color: #b0b4c7;
  --link-hover-color: #b5bbd0;
  --link-active-1: #ff4350;
  --wrapper-bg: #242a37;
  --box-shadow: 0px 0px 8px 0px #282b33;
}

.wrapper {
  background-color: var(--wrapper-bg);
  padding: 0 40px;
  flex-shrink: 0;
  border-radius: 6px;
  box-shadow: var(--box-shadow);
  height: 60px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease-in;
}

.item-link {
  position: relative;
  display: flex;
  align-items: center;
  color: #7c7f8e;
  text-decoration: none;
  padding: 18px 0;
  margin: 0 10px;
  letter-spacing: 0.5px;
}
.item-link:before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  height: 4px;
  background-color: var(--link-hover-color);
  z-index: 2;
  width: 0;
  border-radius: 2px;
  transition: width 0.4s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.item-link:not(.active):hover:before {
  width: 100%;
  opacity: 1;
}
.item-link:not(.active):hover {
  color: var(--link-hover-color);
}
.item-link.active:before {
  width: 100%;
}

.active:first-child {
  color: var(--link-active-1);
}
.active:first-child:before {
  background-color: var(--link-active-1);
}
.active:first-child svg {
  transition: all 0.2s ease-in;
  transform: scale(1.1);
}

.active:nth-child(2) {
  color: #fbb046;
}
.active:nth-child(2):before {
  background-color: #fbb046;
}
.active:nth-child(2) svg circle {
  animation: headbang 0.3s ease-in both;
}

@keyframes headbang {
  0% {
    transform: translate3d(0, 0, 0);
  }
  25% {
    transform: translate3d(2px, 0, 0);
  }
  50% {
    transform: translate3d(0, 0, 0);
  }
  75% {
    transform: translate3d(-2px, 0, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
.active:nth-child(3) {
  color: #47caa4;
}
.active:nth-child(3):before {
  background-color: #47caa4;
}
.active:nth-child(3) svg path {
  animation: shake 0.6s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}
.active:nth-child(3) svg path:nth-child(2) {
  animation-delay: 0.1s;
}

@keyframes shake {
  10%, 90% {
    transform: translate3d(-1px, 0, 0);
  }
  20%, 80% {
    transform: translate3d(2px, 0, 0);
  }
  30%, 50%, 70% {
    transform: translate3d(-4px, 0, 0);
  }
  40%, 60% {
    transform: translate3d(4px, 0, 0);
  }
}
.active:nth-child(4) {
  color: #495fcc;
}
.active:nth-child(4):before {
  background-color: #495fcc;
}
.active:nth-child(4) svg {
  transition: 0.3s ease-in-out;
  transform: rotate(360deg);
}

.link-icon {
  width: 20px;
  height: 18px;
  margin-right: 4px;
  flex-grow: 0;
}

.switch-mode {
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: transparent;
  border: none;
  color: #ddd;
  outline: none;
  cursor: pointer;
}
.switch-mode svg {
  width: 0;
  height: 24px;
  transition: all 0.3s ease-in;
  transform-origin: center;
}
.switch-mode .moon {
  opacity: 0;
}
.switch-mode .sun {
  width: 24px;
}

.dark .moon {
  opacity: 1;
  width: 24px;
}

.dark .sun {
  opacity: 0;
  width: 0;
}

@media screen and (max-width: 580px) {
  .wrapper {
    transform: scale(0.8);
  }

  .switch-mode {
    right: 8px;
  }
}
@media screen and (max-width: 520px) {
  .wrapper {
    transform: scale(0.6);
  }
}