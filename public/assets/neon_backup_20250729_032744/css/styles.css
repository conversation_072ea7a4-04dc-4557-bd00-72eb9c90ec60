:root {
    /* 默认主题颜色 */
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --background-color: #ffffff;
    --text-color: #212529;
}

/* 定义浅蓝主题 */
[data-bs-theme="light-blue"] {
    --primary-color: #a8e6cf;
    --secondary-color: #e3f2fd;
    --background-color: #f8f9fa;
    --text-color: #212529;
}

/* 定义浅灰主题 */
[data-bs-theme="light-gray"] {
    --primary-color: #f4f4f4;
    --secondary-color: #e0e0e0;
    --background-color: #ffffff;
    --text-color: #333333;
}

/* 定义浅木色主题 */
[data-bs-theme="light-wood"] {
    --primary-color: #e6e6c1;
    --secondary-color: #f5f5e0;
    --background-color: #ffffff;
    --text-color: #212529;
}

/* 定义浅棕色主题 */
[data-bs-theme="light-brown"] {
    --primary-color: #d3c1b1;
    --secondary-color: #e6d5c6;
    --background-color: #ffffff;
    --text-color: #212529;
}

/* 定义浅藏色主题 */
[data-bs-theme="light-cyan"] {
    --primary-color: #cfd8dc;
    --secondary-color: #e8f5f5;
    --background-color: #ffffff;
    --text-color: #212529;
}

/* 定义雾霾色主题 */
[data-bs-theme="haze"] {
    --primary-color: #e8e8e8;
    --secondary-color: #f0f0f0;
    --background-color: #ffffff;
    --text-color: #444444;
}

/* 应用主题颜色 */
body {
    background-color: var(--background-color);
    color: var(--text-color);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.card {
    background-color: var(--background-color);
    border-color: var(--secondary-color);
}
