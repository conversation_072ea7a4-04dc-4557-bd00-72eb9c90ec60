
<!-- 现代化导航栏 -->
<nav class="navbar-glass fixed top-0 left-0 right-0 z-50 transition-all duration-300" id="mainNavbar">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16 lg:h-20">
            <!-- Logo区域 -->
            <div class="flex items-center space-x-4">
                <a href="{{ url('/') }}" class="flex items-center space-x-3 group">
                    <div class="relative">
                        <div class="w-10 h-10 lg:w-12 lg:h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center glow-effect group-hover:scale-110 transition-transform duration-300">
                            @if(dujiaoka_config_get('img_logo'))
                                <img src="{{ picture_url(dujiaoka_config_get('img_logo')) }}"
                                     alt="Logo"
                                     class="w-8 h-8 lg:w-10 lg:h-10 object-contain">
                            @else
                                <i class="fas fa-star text-white text-lg lg:text-xl"></i>
                            @endif
                        </div>
                        <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse"></div>
                    </div>
                    <div class="hidden sm:block">
                        <h1 class="text-xl lg:text-2xl font-bold gradient-text">
                            {{ dujiaoka_config_get('text_logo', dujiaoka_config_get('title', '自动发卡平台')) }}
                        </h1>
                        <p class="text-xs text-gray-400 -mt-1">专业数字商品交易平台</p>
                    </div>
                </a>
            </div>

            <!-- 桌面端导航菜单 -->
            <div class="hidden lg:flex items-center space-x-8">
                <div class="flex items-center space-x-6">
                    <a href="{{ url('/') }}" class="nav-link {{ request()->is('/') ? 'active' : '' }}">
                        <i class="fas fa-home mr-2"></i>
                        <span>首页</span>
                    </a>

                    <div class="relative group">
                        <button class="nav-link flex items-center">
                            <i class="fas fa-shopping-bag mr-2"></i>
                            <span>商品分类</span>
                            <i class="fas fa-chevron-down ml-1 text-xs transition-transform group-hover:rotate-180"></i>
                        </button>

                        <!-- 下拉菜单 -->
                        <div class="absolute top-full left-0 mt-2 w-64 glass-effect rounded-xl shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                            <div class="p-4">
                                <div class="grid grid-cols-1 gap-2">
                                    <a href="{{ url('/?category=gift') }}" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                                        <div class="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-gift text-white text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-white">礼品卡</div>
                                            <div class="text-xs text-gray-400">各类数字礼品卡</div>
                                        </div>
                                    </a>
                                    <a href="{{ url('/?category=game') }}" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                                        <div class="w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-gamepad text-white text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-white">游戏充值</div>
                                            <div class="text-xs text-gray-400">热门游戏充值卡</div>
                                        </div>
                                    </a>
                                    <a href="{{ url('/?category=mobile') }}" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                                        <div class="w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-500 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-mobile-alt text-white text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-white">话费充值</div>
                                            <div class="text-xs text-gray-400">手机话费充值</div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="{{ url('/article') }}" class="nav-link {{ request()->is('article*') ? 'active' : '' }}">
                        <i class="fas fa-newspaper mr-2"></i>
                        <span>帮助中心</span>
                    </a>

                    <a href="{{ url('/search-order') }}" class="nav-link {{ request()->is('search-order*') ? 'active' : '' }}">
                        <i class="fas fa-search mr-2"></i>
                        <span>订单查询</span>
                    </a>

                    <div class="relative group">
                        <button class="nav-link flex items-center">
                            <i class="fas fa-headset mr-2"></i>
                            <span>客服支持</span>
                            <i class="fas fa-chevron-down ml-1 text-xs transition-transform group-hover:rotate-180"></i>
                        </button>

                        <!-- 客服下拉菜单 -->
                        <div class="absolute top-full right-0 mt-2 w-56 glass-effect rounded-xl shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                            <div class="p-4">
                                <div class="space-y-2">
                                    <a href="#" onclick="openCustomerService('qq')" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                                        <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fab fa-qq text-white"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-white">QQ客服</div>
                                            <div class="text-xs text-gray-400">在线咨询</div>
                                        </div>
                                    </a>
                                    <a href="#" onclick="openCustomerService('wechat')" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                                        <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fab fa-weixin text-white"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-white">微信客服</div>
                                            <div class="text-xs text-gray-400">扫码咨询</div>
                                        </div>
                                    </a>
                                    <a href="https://t.me/riniba" target="_blank" class="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                                        <div class="w-8 h-8 bg-blue-400 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fab fa-telegram text-white"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-white">Telegram</div>
                                            <div class="text-xs text-gray-400">飞机</div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 搜索框 -->
                <div class="relative">
                    <div class="relative">
                        <input type="text"
                               placeholder="搜索商品..."
                               class="w-64 pl-10 pr-4 py-2 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                               id="globalSearch">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>

                    <!-- 搜索建议下拉 -->
                    <div id="searchSuggestions" class="absolute top-full left-0 right-0 mt-2 glass-effect rounded-xl shadow-xl opacity-0 invisible transition-all duration-300 transform translate-y-2">
                        <div class="p-4">
                            <div class="text-sm text-gray-400 mb-2">热门搜索</div>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-white bg-opacity-10 rounded-full text-sm text-white cursor-pointer hover:bg-opacity-20 transition-colors">Steam</span>
                                <span class="px-3 py-1 bg-white bg-opacity-10 rounded-full text-sm text-white cursor-pointer hover:bg-opacity-20 transition-colors">iTunes</span>
                                <span class="px-3 py-1 bg-white bg-opacity-10 rounded-full text-sm text-white cursor-pointer hover:bg-opacity-20 transition-colors">Google Play</span>
                                <span class="px-3 py-1 bg-white bg-opacity-10 rounded-full text-sm text-white cursor-pointer hover:bg-opacity-20 transition-colors">Netflix</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户菜单 -->
                <div class="flex items-center space-x-4">
                    @auth
                        <!-- 购物车 -->
                        <div class="relative">
                            <button class="relative p-2 text-gray-300 hover:text-white transition-colors">
                                <i class="fas fa-shopping-cart text-lg"></i>
                                <span class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
                            </button>
                        </div>

                        <!-- 通知 -->
                        <div class="relative">
                            <button class="relative p-2 text-gray-300 hover:text-white transition-colors">
                                <i class="fas fa-bell text-lg"></i>
                                <span class="absolute -top-1 -right-1 w-5 h-5 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center">2</span>
                            </button>
                        </div>

                        <!-- 用户头像菜单 -->
                        <div class="relative group">
                            <button class="flex items-center space-x-2 p-2 rounded-xl hover:bg-white hover:bg-opacity-10 transition-colors">
                                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                    @if(Auth::user()->avatar)
                                        <img src="{{ Auth::user()->avatar }}" alt="头像" class="w-8 h-8 rounded-full object-cover">
                                    @else
                                        <i class="fas fa-user text-white text-sm"></i>
                                    @endif
                                </div>
                                <span class="text-white font-medium">{{ Str::limit(Auth::user()->email, 15) }}</span>
                                <i class="fas fa-chevron-down text-xs text-gray-400 transition-transform group-hover:rotate-180"></i>
                            </button>

                            <!-- 用户下拉菜单 -->
                            <div class="absolute top-full right-0 mt-2 w-64 glass-effect rounded-xl shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                                <div class="p-4">
                                    <div class="border-b border-white border-opacity-10 pb-3 mb-3">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                                @if(Auth::user()->avatar)
                                                    <img src="{{ Auth::user()->avatar }}" alt="头像" class="w-12 h-12 rounded-full object-cover">
                                                @else
                                                    <i class="fas fa-user text-white"></i>
                                                @endif
                                            </div>
                                            <div>
                                                <div class="font-medium text-white">{{ Auth::user()->email }}</div>
                                                <div class="text-sm text-gray-400">
                                                    余额: {{ number_format(Auth::user()->money/100, 2) }} {{ dujiaoka_config_get('global_currency', '元') }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="space-y-1">
                                        <a href="{{ url('/user') }}" class="flex items-center p-2 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                                            <i class="fas fa-tachometer-alt w-5 text-blue-400 mr-3"></i>
                                            <span class="text-white">用户中心</span>
                                        </a>
                                        <a href="{{ url('/order-search') }}" class="flex items-center p-2 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                                            <i class="fas fa-shopping-bag w-5 text-green-400 mr-3"></i>
                                            <span class="text-white">我的订单</span>
                                        </a>
                                        <a href="{{ url('/user/invite') }}" class="flex items-center p-2 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                                            <i class="fas fa-gift w-5 text-purple-400 mr-3"></i>
                                            <span class="text-white">邀请返利</span>
                                        </a>
                                        <a href="{{ url('/account') }}" class="flex items-center p-2 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors">
                                            <i class="fas fa-cog w-5 text-gray-400 mr-3"></i>
                                            <span class="text-white">账户设置</span>
                                        </a>
                                        <div class="border-t border-white border-opacity-10 pt-2 mt-2">
                                            <form method="POST" action="{{ url('/logout') }}" class="mb-0">
                                                @csrf
                                                <button type="submit" class="flex items-center w-full p-2 rounded-lg hover:bg-red-500 hover:bg-opacity-20 transition-colors">
                                                    <i class="fas fa-sign-out-alt w-5 text-red-400 mr-3"></i>
                                                    <span class="text-white">退出登录</span>
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @else
                        <!-- 未登录状态 -->
                        <div class="flex items-center space-x-3">
                            <a href="{{ url('/login') }}" class="px-4 py-2 text-white hover:text-blue-400 transition-colors font-medium">
                                登录
                            </a>
                            <a href="{{ url('/register') }}" class="px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-medium hover:scale-105 transition-transform duration-300 glow-effect">
                                注册
                            </a>
                        </div>
                    @endauth
                </div>
            </div>

            <!-- 移动端菜单按钮 -->
            <div class="lg:hidden">
                <button id="mobileMenuBtn" class="p-2 text-white hover:text-blue-400 transition-colors">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </div>


    <!-- 移动端菜单 -->
    <div id="mobileMenu" class="lg:hidden fixed inset-0 z-50 transform translate-x-full transition-transform duration-300">
        <div class="flex">
            <!-- 菜单内容 -->
            <div class="w-80 max-w-full h-full glass-effect">
                <div class="p-6">
                    <!-- 关闭按钮 -->
                    <div class="flex justify-between items-center mb-8">
                        <h2 class="text-xl font-bold text-white">菜单</h2>
                        <button id="closeMobileMenu" class="p-2 text-white hover:text-red-400 transition-colors">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <!-- 用户信息 -->
                    @auth
                        <div class="bg-white bg-opacity-10 rounded-xl p-4 mb-6">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                    @if(Auth::user()->avatar)
                                        <img src="{{ Auth::user()->avatar }}" alt="头像" class="w-12 h-12 rounded-full object-cover">
                                    @else
                                        <i class="fas fa-user text-white"></i>
                                    @endif
                                </div>
                                <div>
                                    <div class="font-medium text-white">{{ Str::limit(Auth::user()->email, 20) }}</div>
                                    <div class="text-sm text-gray-400">
                                        余额: {{ number_format(Auth::user()->money/100, 2) }} {{ dujiaoka_config_get('global_currency', '元') }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endauth

                    <!-- 菜单项 -->
                    <div class="space-y-2">
                        <a href="{{ url('/') }}" class="flex items-center p-3 rounded-xl hover:bg-white hover:bg-opacity-10 transition-colors">
                            <i class="fas fa-home w-6 text-blue-400 mr-3"></i>
                            <span class="text-white font-medium">首页</span>
                        </a>

                        <div class="mobile-dropdown">
                            <button class="flex items-center justify-between w-full p-3 rounded-xl hover:bg-white hover:bg-opacity-10 transition-colors">
                                <div class="flex items-center">
                                    <i class="fas fa-shopping-bag w-6 text-green-400 mr-3"></i>
                                    <span class="text-white font-medium">商品分类</span>
                                </div>
                                <i class="fas fa-chevron-down text-gray-400 transition-transform"></i>
                            </button>
                            <div class="ml-9 mt-2 space-y-1 hidden">
                                <a href="{{ url('/?category=gift') }}" class="block p-2 text-gray-300 hover:text-white transition-colors">礼品卡</a>
                                <a href="{{ url('/?category=game') }}" class="block p-2 text-gray-300 hover:text-white transition-colors">游戏充值</a>
                                <a href="{{ url('/?category=mobile') }}" class="block p-2 text-gray-300 hover:text-white transition-colors">话费充值</a>
                            </div>
                        </div>

                        <a href="{{ url('/article') }}" class="flex items-center p-3 rounded-xl hover:bg-white hover:bg-opacity-10 transition-colors">
                            <i class="fas fa-newspaper w-6 text-purple-400 mr-3"></i>
                            <span class="text-white font-medium">帮助中心</span>
                        </a>

                        <a href="{{ url('/search-order') }}" class="flex items-center p-3 rounded-xl hover:bg-white hover:bg-opacity-10 transition-colors">
                            <i class="fas fa-search w-6 text-yellow-400 mr-3"></i>
                            <span class="text-white font-medium">订单查询</span>
                        </a>

                        <div class="mobile-dropdown">
                            <button class="flex items-center justify-between w-full p-3 rounded-xl hover:bg-white hover:bg-opacity-10 transition-colors">
                                <div class="flex items-center">
                                    <i class="fas fa-headset w-6 text-pink-400 mr-3"></i>
                                    <span class="text-white font-medium">客服支持</span>
                                </div>
                                <i class="fas fa-chevron-down text-gray-400 transition-transform"></i>
                            </button>
                            <div class="ml-9 mt-2 space-y-1 hidden">
                                <a href="#" onclick="openCustomerService('qq')" class="block p-2 text-gray-300 hover:text-white transition-colors">QQ客服</a>
                                <a href="#" onclick="openCustomerService('wechat')" class="block p-2 text-gray-300 hover:text-white transition-colors">微信客服</a>
                                <a href="https://t.me/riniba" target="_blank" class="block p-2 text-gray-300 hover:text-white transition-colors">Telegram</a>
                            </div>
                        </div>

                        @auth
                            <div class="border-t border-white border-opacity-10 pt-4 mt-4">
                                <a href="{{ url('/user') }}" class="flex items-center p-3 rounded-xl hover:bg-white hover:bg-opacity-10 transition-colors">
                                    <i class="fas fa-tachometer-alt w-6 text-blue-400 mr-3"></i>
                                    <span class="text-white font-medium">用户中心</span>
                                </a>
                                <a href="{{ url('/order-search') }}" class="flex items-center p-3 rounded-xl hover:bg-white hover:bg-opacity-10 transition-colors">
                                    <i class="fas fa-shopping-bag w-6 text-green-400 mr-3"></i>
                                    <span class="text-white font-medium">我的订单</span>
                                </a>
                                <a href="{{ url('/user/invite') }}" class="flex items-center p-3 rounded-xl hover:bg-white hover:bg-opacity-10 transition-colors">
                                    <i class="fas fa-gift w-6 text-purple-400 mr-3"></i>
                                    <span class="text-white font-medium">邀请返利</span>
                                </a>
                                <a href="{{ url('/account') }}" class="flex items-center p-3 rounded-xl hover:bg-white hover:bg-opacity-10 transition-colors">
                                    <i class="fas fa-cog w-6 text-gray-400 mr-3"></i>
                                    <span class="text-white font-medium">账户设置</span>
                                </a>
                                <form method="POST" action="{{ url('/logout') }}" class="mb-0">
                                    @csrf
                                    <button type="submit" class="flex items-center w-full p-3 rounded-xl hover:bg-red-500 hover:bg-opacity-20 transition-colors">
                                        <i class="fas fa-sign-out-alt w-6 text-red-400 mr-3"></i>
                                        <span class="text-white font-medium">退出登录</span>
                                    </button>
                                </form>
                            </div>
                        @else
                            <div class="border-t border-white border-opacity-10 pt-4 mt-4 space-y-3">
                                <a href="{{ url('/login') }}" class="block w-full p-3 text-center border border-white border-opacity-20 rounded-xl text-white font-medium hover:bg-white hover:bg-opacity-10 transition-colors">
                                    登录
                                </a>
                                <a href="{{ url('/register') }}" class="block w-full p-3 text-center bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl text-white font-medium hover:scale-105 transition-transform duration-300">
                                    注册
                                </a>
                            </div>
                        @endauth
                    </div>
                </div>
            </div>

            <!-- 遮罩层 -->
            <div class="flex-1 bg-black bg-opacity-50" id="mobileMenuOverlay"></div>
        </div>
    </div>
</nav>

<!-- 站点公告模态框 -->
<div id="announcementModal" class="modal-overlay hidden">
    <div class="modal-content max-w-2xl">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-2xl font-bold gradient-text">{{ __('dujiaoka.site_announcement') }}</h3>
            <button onclick="closeAnnouncementModal()" class="text-gray-400 hover:text-white transition-colors">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="prose prose-invert max-w-none">
            {!! dujiaoka_config_get('notice', '暂无公告') !!}
        </div>
        <div class="flex justify-end mt-6 space-x-3">
            <button onclick="closeAnnouncementModal()" class="px-6 py-2 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-colors">
                关闭
            </button>
            <button onclick="closeAnnouncementModal()" class="btn-primary">
                确定
            </button>
        </div>
    </div>
</div>

<!-- 导航栏样式 -->
<style>
    .nav-link {
        @apply flex items-center px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 rounded-lg relative;
    }

    .nav-link.active {
        @apply text-white bg-white bg-opacity-10;
    }

    .nav-link::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background: linear-gradient(90deg, var(--glow-primary), var(--glow-secondary));
        transition: all 0.3s ease;
        transform: translateX(-50%);
    }

    .nav-link:hover::before,
    .nav-link.active::before {
        width: 80%;
    }

    /* 移动端下拉菜单 */
    .mobile-dropdown button:hover + div,
    .mobile-dropdown button:focus + div {
        display: block !important;
    }

    /* 搜索框动画 */
    #globalSearch:focus + div + #searchSuggestions {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }
</style>

<!-- 导航栏JavaScript -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 移动端菜单控制
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const mobileMenu = document.getElementById('mobileMenu');
        const closeMobileMenu = document.getElementById('closeMobileMenu');
        const mobileMenuOverlay = document.getElementById('mobileMenuOverlay');

        function openMobileMenu() {
            mobileMenu.classList.remove('translate-x-full');
            document.body.style.overflow = 'hidden';
        }

        function closeMobileMenuFunc() {
            mobileMenu.classList.add('translate-x-full');
            document.body.style.overflow = '';
        }

        mobileMenuBtn?.addEventListener('click', openMobileMenu);
        closeMobileMenu?.addEventListener('click', closeMobileMenuFunc);
        mobileMenuOverlay?.addEventListener('click', closeMobileMenuFunc);

        // 搜索功能
        const globalSearch = document.getElementById('globalSearch');
        const searchSuggestions = document.getElementById('searchSuggestions');

        globalSearch?.addEventListener('focus', function() {
            searchSuggestions.classList.remove('opacity-0', 'invisible', 'translate-y-2');
        });

        globalSearch?.addEventListener('blur', function() {
            setTimeout(() => {
                searchSuggestions.classList.add('opacity-0', 'invisible', 'translate-y-2');
            }, 200);
        });

        // 搜索建议点击
        document.querySelectorAll('#searchSuggestions span').forEach(span => {
            span.addEventListener('click', function() {
                globalSearch.value = this.textContent;
                // 这里可以添加搜索逻辑
                window.location.href = `/?search=${encodeURIComponent(this.textContent)}`;
            });
        });

        // 滚动时导航栏效果
        let lastScrollY = window.scrollY;
        const navbar = document.getElementById('mainNavbar');

        window.addEventListener('scroll', function() {
            const currentScrollY = window.scrollY;

            if (currentScrollY > 100) {
                navbar.classList.add('bg-opacity-95');
            } else {
                navbar.classList.remove('bg-opacity-95');
            }

            if (currentScrollY > lastScrollY && currentScrollY > 200) {
                navbar.style.transform = 'translateY(-100%)';
            } else {
                navbar.style.transform = 'translateY(0)';
            }

            lastScrollY = currentScrollY;
        });

        // 移动端下拉菜单
        document.querySelectorAll('.mobile-dropdown button').forEach(button => {
            button.addEventListener('click', function() {
                const dropdown = this.nextElementSibling;
                const icon = this.querySelector('.fa-chevron-down');

                dropdown.classList.toggle('hidden');
                icon.classList.toggle('rotate-180');
            });
        });
    });

    // 客服功能
    function openCustomerService(type) {
        const services = {
            qq: {
                title: 'QQ客服',
                content: '请添加QQ：123456789',
                action: () => window.open('tencent://message/?uin=123456789&Site=&Menu=yes')
            },
            wechat: {
                title: '微信客服',
                content: '请扫描二维码添加微信客服',
                action: () => showModal('<div class="text-center"><img src="/assets/images/wechat-qr.png" alt="微信二维码" class="mx-auto mb-4"><p>扫描二维码添加微信客服</p></div>', '微信客服')
            },
            telegram: {
                title: 'Telegram客服',
                content: '点击链接联系Telegram客服',
                action: () => window.open('https://t.me/riniba')
            }
        };

        const service = services[type];
        if (service) {
            if (confirm(`${service.title}\n${service.content}\n\n点击确定打开客服`)) {
                service.action();
            }
        }
    }

    // 公告模态框
    function showAnnouncement() {
        document.getElementById('announcementModal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    function closeAnnouncementModal() {
        document.getElementById('announcementModal').classList.add('hidden');
        document.body.style.overflow = '';
    }

    // 如果有公告内容，可以在页面加载时显示
    @if(dujiaoka_config_get('notice'))
        // 可以在这里添加自动显示公告的逻辑
        // setTimeout(showAnnouncement, 2000);
    @endif
</script>